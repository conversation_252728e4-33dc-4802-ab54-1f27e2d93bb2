import { sys } from "cc";
import { JSB } from "cc/env";

export default class FaceBookMgr {

    private static androidClass = "org/cocos2dx/javascript/FaceBookMgr";

    public static sendCustomEvent(eventName: string) {
        if (this.isAndroid()) {
            jsb.reflection.callStaticMethod(this.androidClass, "onUserAgreed", "(Ljava/lang/String;)V", eventName);
        } else {
            console.error("please run on android");
        }
    }

    private static isAndroid() {
        return JSB && sys.os == sys.OS.ANDROID;
    }
    private static isIos() {
        return JSB && sys.os == sys.OS.IOS;
    }

}