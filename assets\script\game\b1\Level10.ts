
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, ERigidBody2DType, UITransform, v2, RigidBody2D, Sprite, SpriteFrame} from 'cc';
import {Constants} from "../Constants";
import {uiManager} from "../../framework/uiManager";
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level10')
export class Level10 extends LevelDialogBase {

    @property(Node)
    LeftBoxNode:Node = null

    @property(Node)
    RightBoxNode:Node = null

    @property(Node)
    HotBoomNode:Node = null

    @property(Node)
    EndPos:Node = null

    @property(SpriteFrame)
    BoxSpriteFrames:SpriteFrame[] = [null, null, null]

    isFly:boolean = false
    failCnt:number = 0
    isAngry:boolean = false

    leftPicNode:Node=null
    leftPicsNode:Node=null
    rightPicNode:Node=null
    rightPicsNode:Node=null

    start(){
        super.start()
        this.LeftBoxNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onLeftBoxNodeContact, this);
        this.RightBoxNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onRightBoxNodeContact, this);
        this.HotBoomNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onHotBoomContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        this.HotBoomNode.active = false

        this.leftPicNode = this.LeftBoxNode.getChildByName("Mask").getChildByName("Pic")
        this.rightPicNode = this.RightBoxNode.getChildByName("Mask").getChildByName("Pic")

        this.leftPicsNode = this.LeftBoxNode.getChildByName("Mask").getChildByName("Pics")
        this.rightPicsNode = this.RightBoxNode.getChildByName("Mask").getChildByName("Pics")
    }

    roleRun(){
        if(this.isAngry){
            this.roleSpine.setAnimation(1,"run", true)
        }else{
            this.roleSpine.setAnimation(1,"run", true)
        }
    }

    roleJump(){
        if(this.isAngry){
            this.roleSpine.setAnimation(1,"jump", false)
        }else{
            this.roleSpine.setAnimation(1,"jump", false)
        }

    }

    roleStandby(){
        if(this.isAngry){
            this.roleSpine.setAnimation(1,"angry3", true)
        }else{
            if(this.failCnt==1){
                this.roleSpine.setAnimation(1,"angry1", true)
            }else if(this.failCnt==2){
                this.roleSpine.setAnimation(1,"angry2", true)
            }else{
                this.roleSpine.setAnimation(1,"standBy", true)
            }
        }

    }

    checkFailCnt(){
        if(this.failCnt>=3){
            audioManager.instance.playSound(Constants.Sounds.prompt)
            this.HotBoomNode.active = true
            this.isAngry = true
        }
    }

    onHotBoomContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
            if(this.isFly==false){
                this.isFly = true
                audioManager.instance.playSound(Constants.Sounds.prompt)
                Public.Bezier2DShow(this.HotBoomNode, this.EndPos, 0.5, -10, -10, 22, ()=>{
                    // this.showKey()
                })
                this.scheduleOnce(()=>{
                   this.showKey()
                },3)
            }
        })
    }

    getWinner(randInt){
        let winner = randInt
        if(randInt==0){ //锤子
            winner=2 //布
        }else if(randInt==1){ //剪刀
            winner=0 //锤子
        }else if(randInt==2){ //布
            winner=1 //剪刀
        }
        return winner
    }

    chgPics(n:number, cb:Function){
        if(n<=0){
            cb && cb()
            return
        }
        n = n-1
        let posLeft = this.leftPicsNode.position
        let posRight = this.rightPicsNode.position
        let posLeftUIT = this.leftPicsNode.getComponent(UITransform)
        let posRightUIT = this.rightPicsNode.getComponent(UITransform)
        console.log("--posLeftUIT----",posLeftUIT,n)

        tween(this.rightPicsNode).to(0.4, {position: new Vec3(posRight.x, posRight.y+posRightUIT.height, posRight.z)}).call(()=>{
            this.rightPicsNode.position = new Vec3(posLeft.x, posLeft.y-posRightUIT.height, posLeft.z)
        }).start()

        tween(this.leftPicsNode).to(0.4, {position: new Vec3(posLeft.x, posLeft.y+posLeftUIT.height, posLeft.z)}).call(()=>{
            this.leftPicsNode.position = new Vec3(posLeft.x, posLeft.y-posLeftUIT.height, posLeft.z)
            this.chgPics(n, cb)
        }).start()
    }

    chgPic(cb:Function){
        this.leftPicNode.active=false
        this.rightPicNode.active=false
        this.leftPicsNode.active=true
        this.rightPicsNode.active=true

        this.chgPics(3, ()=>{
            this.leftPicNode.active=true
            this.rightPicNode.active=true
            this.leftPicsNode.active=false
            this.rightPicsNode.active=false
            cb && cb()
        })


        // let randInt = Public.RanInt(0,2)
        // this.LeftBoxNode.getChildByName("Pic").getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[randInt]
        // this.RightBoxNode.getChildByName("Pic").getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[this.getWinner(randInt)]

    }

    onLeftBoxNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            console.log("------left touch----below----")
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.LeftBoxNode.position.y
            tween(this.LeftBoxNode)
                .to(0.05,{position:new Vec3(this.LeftBoxNode.position.x,y+10,this.LeftBoxNode.position.z)})
                .to(0.1,{position:new Vec3(this.LeftBoxNode.position.x,y,this.LeftBoxNode.position.z)})
                .call(()=>{
                    //播放滚动动画，
                    this.chgPic(()=>{
                        let randInt = Public.RanInt(0,2)
                        this.leftPicNode.getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[randInt]
                        this.rightPicNode.getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[this.getWinner(randInt)]
                        uiManager.instance.showTips(`<color=${Constants.Colors.black}>失败！</color>`);
                        this.failCnt +=1
                        this.checkFailCnt()
                    })

                }).start()
        })
    }

    onRightBoxNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            console.log("------left touch----below----")
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.RightBoxNode.position.y
            tween(this.RightBoxNode)
                .to(0.05,{position:new Vec3(this.RightBoxNode.position.x,y+10,this.RightBoxNode.position.z)})
                .to(0.1,{position:new Vec3(this.RightBoxNode.position.x,y,this.RightBoxNode.position.z)})
                .call(()=>{
                    this.chgPic(()=>{
                        let randInt = Public.RanInt(0,2)
                        this.rightPicNode.getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[randInt]
                        this.leftPicNode.getComponent(Sprite).spriteFrame = this.BoxSpriteFrames[this.getWinner(randInt)]
                        uiManager.instance.showTips(`<color=${Constants.Colors.black}>失败！</color>`);
                        this.failCnt +=1
                        this.checkFailCnt()
                    })

                }).start()
        })
    }

}
