{"skeleton": {"hash": "/RFWYR8yPQQ", "spine": "3.8-from-4.0.09", "x": -72.74, "y": 0.12, "width": 119, "height": 111.6, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/虎啸龙吟/猴"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 34.45, "rotation": 89.26, "x": -5.83, "y": 15.18}, {"name": "bone2", "parent": "bone", "length": 12.58, "rotation": -132.83, "x": 30.1, "y": -7.83}, {"name": "bone3", "parent": "bone2", "length": 12.92, "rotation": 47.51, "x": 14.35, "y": 2}, {"name": "bone4", "parent": "bone3", "length": 19.6, "rotation": 82.15, "x": 12.84, "y": 2.01}, {"name": "bone5", "parent": "bone", "length": 27.78, "rotation": 1.2, "x": 35.15, "y": -2.66}, {"name": "bone6", "parent": "bone5", "length": 16.88, "rotation": -74.41, "x": 27.1, "y": -2.22}, {"name": "bone7", "parent": "bone", "length": 16.9, "rotation": 65.86, "x": 1.85, "y": 29.14}, {"name": "bone8", "parent": "bone7", "length": 19.61, "rotation": -29.81, "x": 20.04, "y": -1.48}, {"name": "bone9", "parent": "bone8", "length": 16.32, "rotation": -29.06, "x": 21.73, "y": -1.23}, {"name": "bone10", "parent": "bone9", "length": 13.68, "rotation": -53.23, "x": 18.16, "y": -1.54}, {"name": "bone11", "parent": "bone10", "length": 6.85, "rotation": -97.27, "x": 14.19, "y": -1.08}, {"name": "bone12", "parent": "bone11", "length": 4.1, "rotation": -48.28, "x": 6.66, "y": -1.78}], "slots": [{"name": "w", "bone": "root", "attachment": "w"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "h", "bone": "bone5", "attachment": "h"}, {"name": "xj", "bone": "bone4", "attachment": "xj"}, {"name": "s", "bone": "bone2", "attachment": "s"}, {"name": "yan", "bone": "bone6", "attachment": "yan"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.34052, 0, 0.22538, 0.07171, 0.11023, 0.2393, 0.02776, 0.43668, 0, 0.57075, 0.01221, 0.82958, 0.09156, 0.98599, 0.26427, 0.99344, 0.63304, 0.97296, 0.96602, 0.94689, 1, 0.80165, 0.9847, 0.7253, 0.89445, 0.67689, 0.95358, 0.46461, 0.89134, 0.33054, 0.75752, 0.30075, 0.73574, 0.13688, 0.67194, 0.05868, 0.51479, 0, 0.21759, 0.34357, 0.33429, 0.2933, 0.46655, 0.29143, 0.53813, 0.3864, 0.58014, 0.48695, 0.55524, 0.60799, 0.52412, 0.69737, 0.54591, 0.71413, 0.59259, 0.73461, 0.62371, 0.7793, 0.63927, 0.85751, 0.65327, 0.91151, 0.74196, 0.41992, 0.72329, 0.55213, 0.67817, 0.69737, 0.64549, 0.74578], "triangles": [21, 0, 18, 21, 18, 17, 20, 1, 0, 21, 20, 0, 19, 2, 1, 20, 19, 1, 16, 22, 21, 16, 21, 17, 22, 16, 15, 31, 22, 15, 31, 15, 14, 3, 2, 19, 23, 22, 31, 32, 23, 31, 24, 22, 23, 24, 23, 32, 13, 31, 14, 13, 32, 31, 12, 32, 13, 33, 24, 32, 33, 32, 12, 20, 22, 24, 22, 20, 21, 26, 25, 24, 24, 27, 26, 33, 27, 24, 34, 27, 33, 28, 27, 34, 19, 5, 4, 29, 28, 34, 33, 29, 34, 12, 30, 29, 10, 9, 12, 10, 12, 11, 28, 29, 26, 8, 29, 30, 12, 29, 33, 30, 12, 9, 8, 30, 9, 3, 19, 4, 7, 6, 5, 24, 19, 20, 25, 19, 24, 7, 19, 25, 7, 5, 19, 7, 25, 26, 26, 29, 7, 28, 26, 27, 8, 7, 29], "vertices": [45.46, 6.64, 40.97, 14.99, 30.64, 23.26, 18.53, 29.12, 10.32, 31.04, -5.45, 29.95, -14.92, 24.03, -15.21, 11.42, -13.61, -15.48, -11.71, -39.77, -2.82, -42.13, 1.82, -40.96, 4.69, -34.33, 17.7, -38.48, 25.81, -33.83, 27.51, -24.04, 37.48, -22.32, 42.19, -17.6, 45.62, -6.08, 24.38, 15.34, 27.56, 6.86, 27.8, -2.79, 22.07, -8.09, 15.98, -11.24, 8.57, -9.52, 3.09, -7.31, 2.09, -8.92, 0.89, -12.34, -1.81, -14.65, -6.57, -15.84, -9.85, -16.91, 20.22, -23, 12.14, -21.74, 3.24, -18.56, 0.26, -16.21], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 73, "height": 61}}, "h": {"h": {"x": 29.45, "y": 3.28, "rotation": -90.46, "width": 77, "height": 64}}, "s": {"s": {"type": "mesh", "uvs": [0.25023, 0, 0.39242, 0.25778, 0.47972, 0.34727, 0.59946, 0.35153, 0.68677, 0.20238, 0.82646, 0.18959, 0.92374, 0.35579, 0.97862, 0.6413, 0.97613, 0.79471, 0.83145, 0.91403, 0.67429, 0.89699, 0.55955, 0.97369, 0.30012, 0.95238, 0.12052, 0.70522, 0, 0.36857, 0, 0.20664, 0.13798, 0.05323], "triangles": [12, 2, 11, 11, 3, 10, 11, 2, 3, 12, 13, 1, 16, 1, 14, 12, 1, 2, 1, 16, 0, 8, 9, 7, 6, 7, 9, 6, 3, 4, 4, 5, 6, 6, 10, 3, 6, 9, 10, 1, 13, 14, 14, 15, 16], "vertices": [2, 2, -2.24, 6.89, 0.9998, 3, -7.6, 15.54, 0.0002, 2, 2, 6.25, 6.43, 0.82573, 3, -2.21, 8.97, 0.17427, 2, 2, 10.32, 7.34, 0.3483, 3, 1.22, 6.58, 0.6517, 2, 2, 13.95, 10.65, 0.01641, 3, 6.11, 6.14, 0.98359, 1, 3, 9.92, 9.46, 1, 1, 3, 15.66, 9.38, 1, 1, 3, 19.36, 5.12, 1, 1, 3, 21.14, -1.87, 1, 1, 3, 20.78, -5.53, 1, 1, 3, 14.67, -7.98, 1, 2, 2, 25.2, 3.28, 0.0039, 3, 8.27, -7.13, 0.9961, 2, 2, 23.06, -1.3, 0.11061, 3, 3.45, -8.64, 0.88939, 2, 2, 15, -8.26, 0.90989, 3, -7.13, -7.4, 0.09011, 1, 2, 5.57, -9.04, 1, 1, 2, -3.58, -6.59, 1, 1, 2, -6.25, -3.77, 1, 1, 2, -4.69, 2.8, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 41, "height": 24}}, "w": {"w": {"type": "mesh", "uvs": [0.93581, 0.96301, 0.75617, 0.99026, 0.54827, 0.9598, 0.38074, 0.88928, 0.2011, 0.77387, 0.07394, 0.61358, 0, 0.36674, 0.0477, 0.15035, 0.18698, 0.00449, 0.40698, 0, 0.54222, 0.09105, 0.5402, 0.25454, 0.41506, 0.32827, 0.34239, 0.32186, 0.30203, 0.26897, 0.30203, 0.21126, 0.25358, 0.25294, 0.24753, 0.3988, 0.33836, 0.56389, 0.47561, 0.71456, 0.63708, 0.7931, 0.8248, 0.79791, 0.92774, 0.80112, 0.96811, 0.88607, 0.97618, 0.94057], "triangles": [0, 1, 23, 23, 1, 21, 2, 20, 1, 1, 20, 21, 0, 23, 24, 22, 23, 21, 2, 3, 20, 3, 19, 20, 3, 4, 19, 4, 18, 19, 4, 5, 18, 5, 17, 18, 5, 6, 17, 17, 6, 16, 6, 7, 16, 15, 11, 12, 12, 13, 14, 12, 14, 15, 11, 15, 10, 16, 7, 15, 7, 8, 15, 15, 9, 10, 15, 8, 9], "vertices": [1, 7, -13.31, -1.58, 1, 1, 7, -5.29, 4.18, 1, 1, 7, 5.77, 7.03, 1, 2, 7, 15.99, 6.48, 0.94839, 8, -7.47, 4.9, 0.05161, 2, 7, 28.09, 3.45, 0.00525, 8, 4.54, 8.28, 0.99475, 2, 8, 17.4, 7.58, 0.99022, 9, -8.07, 5.6, 0.00978, 1, 9, 9.05, 7.74, 1, 2, 9, 23.4, 3.58, 0.20598, 10, -0.97, 7.26, 0.79402, 1, 10, 11.3, 9.38, 1, 2, 10, 20.19, 1.5, 0.27862, 11, -3.32, 5.63, 0.72138, 1, 11, 5.97, 7.94, 1, 2, 11, 14.93, 1.35, 0.55343, 12, 3.17, 8.26, 0.44657, 2, 11, 15.05, -7.06, 0.03433, 12, 9.53, 2.75, 0.96567, 2, 10, 2.71, -12.12, 0.00282, 12, 9.95, -1.17, 0.99718, 2, 10, 3.57, -8.01, 0.06703, 12, 6.91, -4.08, 0.93297, 4, 9, 17.78, -9.62, 0.01071, 10, 6.24, -5.14, 0.44809, 11, 5.03, -7.37, 8e-05, 12, 3.08, -4.93, 0.54112, 3, 9, 15.25, -6.71, 0.28641, 10, 2.4, -5.43, 0.63154, 12, 6.42, -6.87, 0.08205, 3, 8, 23.9, -8.51, 0.02529, 9, 5.43, -5.31, 0.96908, 10, -4.61, -12.45, 0.00563, 2, 8, 11.9, -6.02, 0.94747, 9, -6.26, -8.96, 0.05253, 2, 7, 16.34, -6.45, 0.43286, 8, -0.74, -6.15, 0.56714, 2, 7, 6.19, -5.27, 0.99962, 8, -10.14, -10.17, 0.00038, 1, 7, -3.15, -9.24, 1, 1, 7, -8.28, -11.38, 1, 1, 7, -12.69, -7.06, 1, 1, 7, -14.65, -3.88, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 54, "height": 68}}, "xj": {"xj": {"x": 8.14, "y": -0.37, "rotation": -86.1, "width": 37, "height": 40}}, "yan": {"yan": {"x": 7.07, "y": -0.67, "rotation": -16.05, "width": 26, "height": 14}}}}], "animations": {"animation": {"bones": {"bone2": {"rotate": [{}, {"time": 0.4333, "angle": -11.92}, {"time": 0.8667}]}, "bone3": {"rotate": [{}, {"time": 0.4333, "angle": -0.54}, {"time": 0.8667}]}, "bone4": {"rotate": [{}, {"time": 0.4333, "angle": -11.92}, {"time": 0.7667, "angle": 4.75}, {"time": 0.8667}]}, "bone5": {"rotate": [{}, {"time": 0.3333, "angle": -5.81}, {"time": 0.8667}]}, "bone7": {"rotate": [{}, {"time": 0.2667, "angle": -12.68}, {"time": 0.8667}]}, "bone8": {"rotate": [{}, {"time": 0.3, "angle": -23.82}, {"time": 0.8667}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -12.68}, {"time": 0.8667}]}, "bone10": {"rotate": [{"angle": 6.01}, {"time": 0.4333, "angle": -15.91}, {"time": 0.8667, "angle": 6.01}]}, "bone11": {"rotate": [{"angle": 37.07}, {"time": 0.5, "angle": -12.68}, {"time": 0.8667, "angle": 37.07}]}, "bone12": {"rotate": [{"angle": 3.69}, {"time": 0.5667, "angle": -12.68}, {"time": 0.8667, "angle": 3.69}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.4, "offset": 38, "vertices": [-0.82142, 2.03413, -0.82142, 2.03413, -0.82142, 2.03413, -0.82142, 2.03413, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82143, 0.65951, -0.82142, 2.03413, -0.82142, 2.03413, -0.82142, 2.03413, -0.82142, 2.03413]}, {"time": 0.8667}]}}}}}}