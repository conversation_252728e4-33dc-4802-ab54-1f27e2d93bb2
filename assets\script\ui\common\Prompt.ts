
import { _decorator, Component, Node, Label, Vec3 } from 'cc';
import {dialogBase} from "../../framework/dialogBase";
import {uiManager} from "../../framework/uiManager";
import {Constants} from "../../game/Constants";
import {AdCtl} from "../../channel/AdCtl";
import {clientEvent} from "../../framework/clientEvent";
import TyqEventMgr from "../../tyqSDK/tyq-event-mgr";
import {Public} from "../../game/Public";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Prompt
 * DateTime = Thu Feb 24 2022 17:42:50 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Prompt.ts
 * FileBasenameNoExtension = Prompt
 * URL = db://assets/script/ui/common/Prompt.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Prompt')
export class Prompt extends dialogBase {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;

    // onLoad(){
    //     this.node.position = new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0)
    // }

    show(){
        super.show(null)
        console.log(this.node.getSiblingIndex()+"-------------")
        AdCtl.instance.ShowBannerAd()
    }

    onEnable(){

    }

    OnClickClose(){
        clientEvent.dispatchEvent(Constants.Events.resetLevelInsertTimeout)
        this.close()
        AdCtl.instance.ShowBannerAd("game")
        AdCtl.instance.ShowCustomLevelUI()
        AdCtl.instance.ShowMainCanvas()
    }

    public OnClickGetResByAD() {
        //看广告激励广告
        AdCtl.instance.HideBannerAD()
        AdCtl.instance.ShowRewardVideoAd(()=>{
            AdCtl.instance.ShowBannerAd("game")
            TyqEventMgr.ins.sendEvent("观看激励视频-获取提示关卡"+Public.CurLevelNum);
            this.close()
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoSuccess, cb:()=>{
                    uiManager.instance.showDialog(Constants.Dialogs.promptRes)
                }})
        }, ()=>{
            AdCtl.instance.ShowBannerAd("game")
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoFail, cb:()=>{

                }})
        });

    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
