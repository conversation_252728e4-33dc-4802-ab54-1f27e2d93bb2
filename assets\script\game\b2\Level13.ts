import {_decorator, Collider2D, Contact2DType, Node, tween, Vec3, find, UITransform, sp, EventTouch, v2} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
import {uiManager} from "../../framework/uiManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level13')
export class Level13 extends LevelDialogBase {

    //门播放动画
    @property(Node)
    Door:Node = null

    //地板也移动
    @property(Node)
    GroundNode:Node = null

    //背景的图片跳跃
    @property(Node)
    BgNode:Node = null

    //地板的图片跳跃
    @property(Node)
    GroundPicNode:Node = null

    //底部拿来顶的碰撞体
    @property(Node)
    StoneNode:Node = null

    @property(Node)
    WordLabel:Node = null

    @property(Node)
    DoorAniNode:Node = null

    isCloseDoor:boolean = false
    isCameraFlow:boolean = false //是否相机跟随
    cameraOriPos = null

    moveObjs=[]
    isLoadOps:boolean = false
    jumpCnt:number=0
    jumpGroundCnt:number=0
    isReverseDirection:boolean = false
    isStoneFly:boolean = false
    isTouchWord:boolean = false
    isCatchDoor:boolean = false

    doorStat:string = ""

    start(){
        super.start()
        // this.StoneNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onStoneNodeContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);

    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = false
        this.Key.active = false
        this.isBorderOutFail = false
        this.isCameraFlow = true
        this.WordLabel.active = false

        //控制移动的物体
        this.moveObjs.push(this.DoorAniNode)
        this.moveObjs.push(this.doorNode)
        this.moveObjs.push(this.GroundNode)
        this.moveObjs.push(this.WordLabel)
        // this.moveObjs.push(this.BgNode)
        this.moveObjs.push(Public.MainCamera.node)
        console.log("-----moveObjs---", find("Canvas"))
        // this.showKey()

    }

    onEnable() {
        super.onEnable();
        //先控制相机的位置，要还原相机的位置，把门挂到相机里面
        let p = Public.MainCamera.node.position
        this.cameraOriPos = new Vec3(p.x, p.y, p.z)

    }

    onDisable() {
        super.onDisable();
        Public.MainCamera.node.setPosition(this.cameraOriPos)
    }

    onStoneNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)

        })
    }

    opsLeftStart(){
        super.opsLeftStart()
        this.moveLeft = true;
        let s = this.DoorAniNode.scale
        this.DoorAniNode.setScale(new Vec3(-Math.abs(s.x), s.y, s.z))
        this.DoorAniNode.getChildByName("DoorAni").getChildByName("node").getChildByName("mbs").setScale(new Vec3(-Math.abs(s.x), s.y, s.z))
    }

    opsRightStart(){
        super.opsRightStart()
        this.moveRight = true;
        let s = this.DoorAniNode.scale
        this.DoorAniNode.setScale(new Vec3(Math.abs(s.x), s.y, s.z))
        this.DoorAniNode.getChildByName("DoorAni").getChildByName("node").getChildByName("mbs").setScale(new Vec3(Math.abs(s.x), s.y, s.z))
    }

    roleRun(){
        super.roleRun()
        if(this.doorStat!="run"){
            console.log("---this.doorStat----", this.doorStat)
            this.doorStat = "run";
            this.DoorAniNode.getChildByName("DoorAni").getComponent(sp.Skeleton).setAnimation(0,"run", true)
        }
    }
    roleStandby(){
        super.roleStandby()
        if(this.doorStat!="standBy"){
            this.doorStat = "standBy";
            this.DoorAniNode.getChildByName("DoorAni").getComponent(sp.Skeleton).setAnimation(0,"standBy", true)
        }
    }

    roleJump(){
        super.roleJump()
        if(this.isCloseDoor==true){
            this.DoorAniNode.getChildByName("DoorAni").getComponent(sp.Skeleton).setAnimation(0,"jump", false)
        }
    }
    opsUp(){
        super.opsUp()
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        if(this.isLoadOps==false){
            if(uiManager.instance.getDialog(Constants.Dialogs.ops)!=undefined){
                this.moveObjs.push(uiManager.instance.getDialog(Constants.Dialogs.ops))
                // uiManager.instance.getDialog(Constants.Dialogs.ops).parent = Public.MainCamera.node
                this.isLoadOps = true
            }
        }

        let speed = Constants.MoveXSpeed
        if(this.isCloseDoor==false){
            let doorNodePos = this.doorNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let roleNodePos = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(doorNodePos, roleNodePos)
            if(dis<330){
                this.doorNode.active = false
                this.DoorAniNode.active = true
                this.DoorAniNode.getChildByName("DoorAni").getComponent(sp.Skeleton).setAnimation(0,"run", true)
                this.isCloseDoor = true
            }
        }else if(this.isEnd==false){

            if(this.isReverseDirection==true){
                let doorNodePos = this.DoorAniNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let roleNodePos = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let dis = Public.GetDistance2D(doorNodePos, roleNodePos)
                if(dis<100){
                    this.doorNode.active = true
                    this.DoorAniNode.active = false
                    this.isCatchDoor = true
                }
            }

            if(this.isCatchDoor==true){
                return
            }

            //判断门是否碰到石头
            if(this.isStoneFly == false){
                let doorNodePos = this.doorNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let stoneNodePos = this.StoneNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let disX = Public.GetDistance2D(doorNodePos, stoneNodePos)
                if(disX<200 ){
                    //石头飞走
                    this.isStoneFly = true
                    audioManager.instance.playSound(Constants.Sounds.drop)
                    Public.Bezier2DShowPoint(this.StoneNode, new Vec3(500,500), 0.5, -200, 200, 1, ()=>{
                        this.StoneNode.active=false
                    })
                    tween(this.StoneNode).by(1, {angle:-360}).start()
                }
            }

            //跟随相机位置
            for(let i=0; i<this.moveObjs.length; i++){
                let n = this.moveObjs[i]
                //如果门被挡住那么不移动
                let symbol = 1
                if(n.name=="DoorNode" || n.name=="DoorAniNode"){
                    if(this.isReverseDirection==true){
                        symbol = -1
                    }
                }
                if(this.moveLeft){
                    n.setPosition(new Vec3(n.position.x - (deltaTime*speed * symbol), n.position.y, n.position.z))
                }else if(this.moveRight){
                    n.setPosition(new Vec3(n.position.x + (deltaTime*speed * symbol), n.position.y, n.position.z))
                }
            }

            //判断主角的位置，是否卡到边，卡到边就跳跃
            let roleP = this.roleNode.position
            let bgP = this.BgNode.position
            let dis = Public.GetDistance2D(roleP, bgP)
            if(dis/43!=this.jumpCnt){
                this.jumpCnt = dis/43
                if(roleP.x > bgP.x){
                    this.BgNode.setPosition(new Vec3(bgP.x+43, bgP.y, bgP.z))
                }
                if(roleP.x < bgP.x){
                    this.BgNode.setPosition(new Vec3(bgP.x-43, bgP.y, bgP.z))
                }
            }

            //判断主角的位置，是否卡到便，卡到边就移动
            let groundP = this.GroundPicNode.position
            let dis2 = Public.GetDistance2D(roleP, groundP)
            if(dis2/20!=this.jumpGroundCnt){
                this.jumpGroundCnt = dis/20
                if(roleP.x > groundP.x){
                    this.GroundPicNode.setPosition(new Vec3(groundP.x+1500, groundP.y, groundP.z))
                }
                if(roleP.x < groundP.x){
                    this.GroundPicNode.setPosition(new Vec3(groundP.x-1500, groundP.y, groundP.z))
                }
            }



        }
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        let cPos = Public.MainCamera.node.position
        const waterNode = this.WordLabel.getComponent(UITransform)!;
        let wordPos = waterNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let realPos = new Vec3(wordPos.x-cPos.x, wordPos.y - cPos.y, wordPos.z - cPos.z)
        let dis = Public.GetDistance2D(realPos, pos)
        console.log("----------move left1----------",realPos, pos, dis)
        if(dis<100 && this.isCloseDoor==true){
            this.WordLabel.active=true
            this.isTouchWord = true
            console.log("self, touch water begin-------")
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.isTouchWord==true){
            let pos = this.WordLabel.position
            console.log("event---touch water move", event.getDeltaX(), event.getDeltaY())
            let ePos = event.getUIDelta()
            this.WordLabel.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }

    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.isTouchWord==true){
            // console.log("event---touch water end", this.waterPosition)
            this.isTouchWord = false
            //判断和鱼缸的距离，进入鱼缸
            let WordLabelPos = this.WordLabel.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let doorPos = this.DoorAniNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(doorPos, WordLabelPos)
            if(dis<300){
                this.WordLabel.active=false
                audioManager.instance.playSound(Constants.Sounds.prompt)
                this.isReverseDirection = true
            }
        }
    }


}
