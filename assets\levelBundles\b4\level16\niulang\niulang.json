{"skeleton": {"hash": "3TZB0Yu/BPU", "spine": "3.8-from-4.0.09", "x": -36.25, "y": 0.53, "width": 96.14, "height": 153.18, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/牛郎织女"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 37.39, "rotation": 80.37, "x": 1.28, "y": 54.6}, {"name": "bone2", "parent": "bone", "length": 25.02, "rotation": -9.29, "x": 40.64, "y": 0.04}, {"name": "bone3", "parent": "bone", "length": 13.54, "rotation": 120.1, "x": -4.35, "y": 4.41}, {"name": "bone4", "parent": "bone", "length": 13.98, "rotation": -115.02, "x": -4.14, "y": -8.94}, {"name": "bone5", "parent": "bone", "length": 18.12, "rotation": -167, "x": -6.22, "y": 3.63}, {"name": "bone6", "parent": "bone5", "length": 20.97, "rotation": -9.75, "x": 21.34, "y": 0.29}, {"name": "bone7", "parent": "bone", "length": 20.5, "rotation": -162.29, "x": -4.49, "y": -0.98}, {"name": "bone8", "parent": "bone7", "length": 18.94, "rotation": -10.15, "x": 22.96, "y": -0.21}, {"name": "bone9", "parent": "bone", "length": 17.6, "rotation": -99.14, "x": 30.68, "y": 2.33}, {"name": "bone10", "parent": "bone9", "length": 22.74, "rotation": 27.36, "x": 20.25, "y": 1.75}, {"name": "bone11", "parent": "bone", "length": 22.13, "rotation": -93.9, "x": 39.18, "y": 3.45}, {"name": "bone12", "parent": "bone11", "length": 20.89, "rotation": 21.09, "x": 23.31, "y": 0.62}, {"name": "bone13", "parent": "bone", "length": 19.19, "rotation": 111.79, "x": 29.21, "y": 11.93}, {"name": "bone14", "parent": "bone2", "length": 23.88, "rotation": 73.3, "x": 32.41, "y": 12.02}, {"name": "bone15", "parent": "bone14", "length": 10.87, "rotation": 72.16, "x": 12.1, "y": 7.28}, {"name": "bone16", "parent": "bone15", "length": 7.22, "rotation": -20.94, "x": 11.88, "y": -0.54}, {"name": "bone17", "parent": "bone14", "length": 12.24, "rotation": 79.02, "x": 7.81, "y": 10.77}, {"name": "bone18", "parent": "bone2", "length": 11.17, "rotation": 20.58, "x": 14.08, "y": -16.32}, {"name": "bone19", "parent": "bone18", "length": 5.08, "rotation": 97.5, "x": 8.51, "y": 3.15}, {"name": "bone20", "parent": "bone6", "length": 9.71, "rotation": 86.64, "x": 19.85, "y": 1.94}, {"name": "bone21", "parent": "bone8", "length": 9.56, "rotation": 84.52, "x": 19.35, "y": 1.46}, {"name": "bone22", "parent": "bone2", "x": 44.02, "y": -22.19}], "slots": [{"name": "mao", "bone": "bone13", "attachment": "mao"}, {"name": "s2", "bone": "bone11", "attachment": "s2"}, {"name": "zlj2", "bone": "bone7", "attachment": "zlj2"}, {"name": "zlj1", "bone": "bone5", "attachment": "zlj1"}, {"name": "jin2", "bone": "bone17", "attachment": "jin2"}, {"name": "jin1", "bone": "bone15", "attachment": "jin1"}, {"name": "HEAD", "bone": "bone2", "attachment": "HEAD"}, {"name": "BQku1", "bone": "bone18", "attachment": "BQku2"}, {"name": "2LEI", "bone": "bone19", "attachment": "2LEI"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "s1", "bone": "bone9", "attachment": "s1"}, {"name": "x", "bone": "bone22", "attachment": "x"}], "skins": [{"name": "default", "attachments": {"2LEI": {"2LEI": {"x": 2.03, "y": 0.59, "rotation": 170.84, "width": 12, "height": 9}}, "body": {"body": {"type": "mesh", "uvs": [0.82157, 0.1242, 0.62965, 0.02435, 0.44637, 0.0036, 0.41179, 0.05288, 0.42562, 0.11382, 0.33052, 0.20589, 0.25099, 0.32649, 0.21987, 0.4458, 0.2337, 0.50934, 0.18183, 0.58066, 0.18702, 0.63253, 0.06944, 0.71034, 0, 0.7661, 0.05907, 0.85946, 0.25791, 0.94505, 0.49824, 0.99433, 0.70399, 1, 0.88554, 0.95931, 0.99793, 0.89318, 0.98582, 0.83742, 0.9011, 0.69996, 0.90629, 0.6053, 0.93741, 0.39393, 0.91839, 0.22016, 0.27866, 0.65737, 0.36329, 0.70119, 0.48821, 0.73293, 0.63932, 0.74502, 0.8005, 0.72386, 0.79647, 0.5939, 0.65544, 0.60902, 0.51238, 0.58937, 0.39552, 0.55764, 0.3109, 0.5123], "triangles": [15, 27, 16, 17, 27, 28, 17, 16, 27, 15, 26, 27, 17, 19, 18, 19, 28, 20, 19, 17, 28, 28, 27, 30, 27, 26, 30, 30, 29, 28, 28, 29, 20, 20, 29, 21, 14, 26, 15, 14, 13, 25, 11, 25, 13, 14, 25, 26, 25, 11, 24, 24, 11, 10, 13, 12, 11, 26, 25, 31, 25, 24, 32, 33, 24, 9, 24, 10, 9, 26, 31, 30, 25, 32, 31, 24, 33, 32, 33, 9, 8, 29, 30, 22, 21, 29, 22, 22, 30, 31, 22, 31, 5, 0, 23, 5, 0, 5, 4, 0, 4, 1, 23, 22, 5, 32, 6, 5, 31, 32, 5, 33, 7, 6, 32, 33, 6, 33, 8, 7, 1, 3, 2, 1, 4, 3], "vertices": [2, 1, 42.42, -9.87, 0.99965, 4, -18.84, 42.59, 0.00035, 1, 1, 47.77, 1.55, 1, 1, 1, 47.59, 11.55, 1, 1, 1, 43.78, 12.8, 1, 1, 1, 39.58, 11.33, 1, 2, 1, 32.18, 15.29, 0.99678, 3, -8.91, -37.06, 0.00322, 2, 1, 22.9, 18.07, 0.95881, 3, -1.85, -30.43, 0.04119, 2, 1, 14.15, 18.29, 0.84342, 3, 2.73, -22.97, 0.15658, 2, 1, 9.77, 16.78, 0.69512, 3, 3.63, -18.42, 0.30488, 2, 1, 4.24, 18.69, 0.42969, 3, 8.05, -14.59, 0.57031, 2, 1, 0.6, 17.79, 0.24566, 3, 9.09, -10.99, 0.75434, 2, 1, -5.99, 23.11, 0.01474, 3, 17, -7.96, 0.98526, 2, 1, -10.57, 26.13, 5e-05, 3, 21.92, -5.51, 0.99995, 2, 3, 21.28, 1.9, 0.99968, 4, -22.61, -24.37, 0.00032, 2, 3, 13.37, 11.43, 0.89746, 4, -10.28, -23.34, 0.10254, 2, 3, 2.46, 19.29, 0.48525, 4, 2.42, -18.88, 0.51475, 2, 3, -7.81, 23.56, 0.14696, 4, 11.79, -12.89, 0.85304, 2, 3, -18.02, 24.24, 0.0107, 4, 18.19, -4.91, 0.9893, 2, 1, -10.57, -28.53, 0, 4, 20.47, 2.46, 1, 2, 1, -6.73, -27.21, 0.00129, 4, 17.65, 5.39, 0.99871, 2, 1, 2.27, -21.04, 0.17918, 4, 8.26, 10.93, 0.82082, 2, 1, 9.03, -20.18, 0.48981, 4, 4.62, 16.69, 0.51019, 2, 1, 24.32, -19.29, 0.90473, 4, -2.65, 30.17, 0.09527, 2, 1, 36.48, -16.18, 0.98989, 4, -10.61, 39.88, 0.01011, 3, 1, -0.34, 12.61, 0.25254, 3, 5.08, -7.59, 0.67776, 4, -21.13, -5.66, 0.0697, 3, 1, -2.68, 7.57, 0.21579, 3, 1.9, -3.03, 0.59111, 4, -15.58, -5.66, 0.19309, 3, 1, -3.81, 0.54, 0.1867, 3, -3.62, 1.47, 0.44637, 4, -8.73, -3.7, 0.36693, 3, 1, -3.3, -7.65, 0.16842, 3, -10.96, 5.14, 0.27739, 4, -1.52, 0.22, 0.55419, 3, 1, -0.34, -15.98, 0.16719, 3, -19.65, 6.76, 0.11048, 4, 4.77, 6.42, 0.72233, 3, 1, 8.85, -14.2, 0.51038, 3, -22.71, -2.09, 0.06165, 4, -0.73, 14, 0.42797, 3, 1, 6.5, -6.87, 0.47346, 3, -15.2, -3.73, 0.17186, 4, -6.37, 8.77, 0.35468, 3, 1, 6.6, 0.98, 0.51251, 3, -8.46, -7.76, 0.24572, 4, -13.53, 5.54, 0.24176, 3, 1, 7.8, 7.59, 0.5835, 3, -3.34, -12.11, 0.2757, 4, -20.02, 3.84, 0.1408, 3, 1, 10.25, 12.64, 0.68876, 3, -0.2, -16.76, 0.24001, 4, -25.64, 3.92, 0.07124], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46], "width": 54, "height": 72}}, "BQku1": {"BQku1": {"x": 7.04, "y": 1.1, "rotation": -91.66, "width": 12, "height": 26}, "BQku2": {"x": 7.04, "y": 1.1, "rotation": -91.66, "width": 12, "height": 26}, "BQXIAO": {"x": 6.57, "y": 2.11, "rotation": -91.66, "width": 12, "height": 25}}, "HEAD": {"HEAD": {"x": 26.29, "y": 6.68, "rotation": -71.08, "width": 61, "height": 64}}, "jin1": {"jin1": {"type": "mesh", "uvs": [0.79266, 0.01351, 0.57925, 0.23221, 0.44688, 0.29097, 0.36313, 0.33014, 0.04976, 0.31382, 0.04976, 0.77735, 0.09298, 0.94709, 0.26588, 0.9732, 0.54413, 0.85242, 0.74674, 0.65657, 0.8629, 0.42481, 0.90882, 0.13755, 0.88181, 0], "triangles": [5, 4, 3, 7, 5, 3, 7, 6, 5, 3, 8, 7, 0, 11, 1, 11, 0, 12, 10, 1, 11, 9, 1, 10, 9, 8, 2, 9, 2, 1, 3, 2, 8], "vertices": [1, 15, -5.48, -4.68, 1, 2, 15, 2.61, -4.15, 0.9849, 16, -7.36, -6.67, 0.0151, 2, 15, 6.54, -5.3, 0.75866, 16, -3.29, -6.35, 0.24134, 2, 15, 9.05, -5.99, 0.38424, 16, -0.69, -6.09, 0.61576, 1, 16, 7.95, -8.91, 1, 1, 16, 10.94, 1.8, 1, 2, 15, 24.16, 1.25, 0.00027, 16, 10.83, 6.06, 0.99973, 2, 15, 20.5, 4.74, 0.0526, 16, 6.17, 8.01, 0.9474, 2, 15, 12.29, 7.21, 0.71966, 16, -2.38, 7.39, 0.28034, 1, 15, 4.77, 6.93, 1, 1, 15, -1.24, 4.46, 1, 1, 15, -6.42, -0.28, 1, 1, 15, -7.75, -3.4, 1], "hull": 13, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 0, 24], "width": 29, "height": 24}}, "jin2": {"jin2": {"x": 7.54, "y": -0.5, "rotation": 136.61, "width": 20, "height": 23}}, "mao": {"mao": {"x": 10.64, "y": 3.52, "rotation": 167.84, "width": 42, "height": 58}}, "s1": {"s1": {"type": "mesh", "uvs": [0.01753, 0.45774, 0.27758, 0.68642, 0.57933, 0.90951, 0.71631, 0.99039, 0.79373, 0.9876, 0.83939, 0.78123, 0.83741, 0.48284, 0.89299, 0.38524, 0.9724, 0.38245, 1, 0.27369, 0.97835, 0.03665, 0.86321, 0.00318, 0.79969, 0.02549, 0.74013, 0.11194, 0.61507, 0.08406, 0.40265, 0.10079, 0.20612, 0.03665, 0.08502, 0.05896, 0.02547, 0.1733, 0, 0.27369, 0.64628, 0.33696, 0.63095, 0.50564, 0.63606, 0.74609, 0.47767, 0.62048, 0.22219, 0.4518], "triangles": [20, 14, 13, 11, 9, 7, 9, 11, 10, 8, 7, 9, 12, 11, 7, 13, 12, 7, 20, 13, 7, 6, 20, 7, 20, 15, 14, 21, 20, 6, 20, 23, 15, 20, 21, 23, 22, 21, 6, 23, 21, 22, 22, 6, 5, 2, 23, 22, 1, 23, 2, 5, 3, 22, 3, 2, 22, 5, 4, 3, 24, 16, 15, 18, 17, 16, 24, 18, 16, 19, 18, 24, 0, 19, 24, 23, 24, 15, 1, 24, 23, 0, 24, 1], "vertices": [1, 9, -0.34, -9.36, 1, 2, 9, 17.28, -13.51, 0.86271, 10, -9.65, -12.2, 0.13729, 2, 9, 37.15, -16.66, 0.11486, 10, 6.55, -24.12, 0.88514, 2, 9, 45.89, -17.27, 0.02985, 10, 14.04, -28.69, 0.97015, 2, 9, 50.18, -15.69, 0.01902, 10, 18.57, -29.25, 0.98098, 2, 9, 49.94, -6.62, 0.00841, 10, 22.53, -21.09, 0.99159, 1, 10, 24.28, -8.68, 1, 1, 10, 28.14, -5.11, 1, 1, 10, 32.79, -5.7, 1, 1, 10, 35.08, -1.42, 1, 1, 10, 35.31, 8.61, 1, 1, 10, 28.8, 11.02, 1, 1, 10, 24.95, 10.65, 1, 1, 10, 20.94, 7.58, 1, 1, 10, 13.82, 9.84, 1, 2, 9, 16.35, 12.15, 0.30511, 10, 1.32, 11.02, 0.69489, 2, 9, 4.51, 10.97, 0.92741, 10, -9.74, 15.42, 0.07259, 2, 9, -1.96, 7.78, 0.99499, 10, -16.95, 15.56, 0.00501, 1, 9, -3.74, 2.11, 1, 1, 9, -3.81, -2.37, 1, 2, 9, 33.15, 7.38, 0.14067, 10, 14.05, -0.93, 0.85933, 2, 9, 34.58, 0.38, 0.13023, 10, 12.1, -7.8, 0.86977, 2, 9, 38.11, -9.08, 0.1047, 10, 10.89, -17.83, 0.8953, 2, 9, 27.56, -7.09, 0.44573, 10, 2.43, -11.22, 0.55427, 2, 9, 11.01, -5.23, 0.9005, 10, -11.41, -1.96, 0.0995], "hull": 20, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38], "width": 59, "height": 42}}, "s2": {"s2": {"type": "mesh", "uvs": [0.00354, 0.42853, 0.27818, 0.69206, 0.57907, 0.90235, 0.76486, 0.97689, 0.81332, 0.93163, 0.84967, 0.5004, 0.9567, 0.38594, 0.97689, 0.24751, 0.98699, 0.06916, 0.89006, 0, 0.80121, 0.0106, 0.69822, 0.1224, 0.46194, 0.14104, 0.36905, 0.14636, 0.19134, 0.03988, 0.05806, 0.06118, 0, 0.17032, 0, 0.42586], "triangles": [3, 2, 4, 4, 2, 5, 5, 12, 11, 5, 2, 12, 5, 11, 7, 6, 5, 7, 10, 7, 11, 9, 7, 10, 7, 9, 8, 2, 1, 12, 1, 13, 12, 13, 1, 0, 14, 16, 15, 13, 0, 14, 16, 14, 0, 0, 17, 16], "vertices": [1, 11, -0.55, -8.31, 1, 2, 11, 17.65, -15.86, 0.97537, 12, -11.21, -13.33, 0.02463, 2, 11, 36.78, -20.77, 0.40983, 12, 4.87, -24.8, 0.59017, 2, 11, 48.02, -21.44, 0.27357, 12, 15.12, -29.47, 0.72643, 2, 11, 50.29, -18.85, 0.2637, 12, 18.17, -27.87, 0.7363, 2, 11, 47.9, 0.09, 0.04278, 12, 22.75, -9.34, 0.95722, 2, 11, 52.76, 6.44, 0.00093, 12, 29.57, -5.16, 0.99907, 1, 12, 31.53, 0.72, 1, 1, 12, 33.15, 8.42, 1, 1, 12, 27.98, 12.18, 1, 1, 12, 22.81, 12.39, 1, 1, 12, 16.24, 8.3, 1, 2, 11, 22.34, 10.21, 0.1988, 12, 2.54, 9.3, 0.8012, 2, 11, 17.15, 8.72, 0.67645, 12, -2.83, 9.77, 0.32355, 2, 11, 6.04, 10.86, 0.99665, 12, -12.43, 15.77, 0.00335, 1, 11, -1.26, 8.14, 1, 1, 11, -3.41, 2.69, 1, 1, 11, -0.78, -8.25, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 58, "height": 44}}, "x": {"x": {"x": 5.02, "y": -3.11, "rotation": -71.08, "width": 24, "height": 22}}, "zlj1": {"zlj1": {"type": "mesh", "uvs": [0.0204, 0.01725, 0.35383, 0, 0.54354, 0.0204, 0.78499, 0.10247, 0.81949, 0.41809, 0.80224, 0.7053, 0.96895, 0.81892, 0.9747, 0.95464, 0.79649, 0.97989, 0.18712, 0.97989, 0.01466, 0.91361, 0.00316, 0.8063, 0, 0.67058, 0.06065, 0.42124], "triangles": [5, 8, 9, 6, 8, 5, 6, 7, 8, 5, 11, 12, 5, 13, 4, 11, 9, 10, 13, 5, 12, 5, 9, 11, 13, 0, 1, 13, 1, 2, 4, 13, 3, 13, 2, 3], "vertices": [1, 5, -1.42, -11.27, 1, 1, 5, -1.75, -1.9, 1, 1, 5, -0.4, 3.34, 1, 2, 5, 4.18, 9.84, 0.99972, 6, -18.53, 6.5, 0.00028, 3, 5, 20.3, 9.86, 0.37914, 6, -2.64, 9.25, 0.61598, 20, 5.98, 22.88, 0.00488, 2, 6, 11.97, 10.4, 0.44516, 20, 7.99, 8.36, 0.55484, 2, 6, 17.21, 15.68, 0.02318, 20, 13.57, 3.44, 0.97682, 1, 20, 14.9, -3.35, 1, 2, 6, 25.9, 11.8, 0.00011, 20, 10.2, -5.47, 0.99989, 2, 6, 27.8, -5.16, 0.99499, 20, -6.62, -8.35, 0.00501, 1, 6, 24.98, -10.33, 1, 2, 5, 38.73, -14.12, 0.00135, 6, 19.57, -11.26, 0.99865, 2, 5, 31.81, -13.8, 0.03979, 6, 12.71, -12.12, 0.96021, 2, 5, 19.22, -11.36, 0.63753, 6, -0.12, -11.84, 0.36247], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 28, "height": 51}}, "zlj2": {"zlj2": {"type": "mesh", "uvs": [0.03648, 0.0426, 0.33955, 0, 0.65153, 0.05402, 0.76741, 0.0997, 0.81495, 0.40801, 0.79415, 0.71306, 0.93677, 0.79462, 0.97243, 0.89087, 0.91597, 0.96427, 0.51485, 0.98385, 0.08105, 0.95285, 0, 0.87618, 0.04243, 0.76363, 0, 0.74079, 0.06917, 0.43248, 0, 0.08828], "triangles": [6, 9, 5, 8, 6, 7, 5, 9, 12, 6, 8, 9, 5, 14, 4, 12, 13, 14, 12, 14, 5, 9, 10, 12, 11, 12, 10, 1, 15, 0, 14, 15, 1, 14, 1, 2, 3, 4, 2, 4, 14, 2], "vertices": [1, 7, 0.23, -8.85, 1, 1, 7, -0.73, -0.14, 1, 1, 7, 3.22, 8.12, 1, 1, 7, 5.99, 11.01, 1, 3, 7, 21.74, 10.12, 0.59795, 8, -3.02, 9.95, 0.39985, 21, 6.31, 23.08, 0.00219, 2, 8, 12.55, 9.93, 0.43087, 21, 7.78, 7.58, 0.56913, 2, 8, 16.56, 14.07, 0.04695, 21, 12.29, 3.99, 0.95305, 1, 21, 13.92, -0.75, 1, 1, 21, 12.85, -4.67, 1, 2, 8, 26.63, 2.62, 0.06258, 21, 1.84, -7.13, 0.93742, 2, 8, 25.49, -9.58, 0.96817, 21, -10.41, -7.17, 0.03183, 2, 8, 21.67, -11.99, 0.99939, 21, -13.17, -3.59, 0.00061, 2, 7, 36.66, -13.85, 0.0005, 8, 15.89, -11.01, 0.9995, 2, 7, 35.34, -14.86, 0.00196, 8, 14.76, -12.24, 0.99804, 2, 7, 20.04, -10.73, 0.55291, 8, -1.02, -10.87, 0.44709, 1, 7, 2.39, -10.18, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 28, "height": 51}}}}], "animations": {"end": {"slots": {"2LEI": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffec"}, {"time": 0.4333, "color": "ffffff00"}], "attachment": [{"name": null}]}, "BQku1": {"attachment": [{"name": "BQXIAO"}]}, "x": {}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.6, "angle": 1.02}, {"time": 1, "angle": -0.26}, {"time": 1.3333}]}, "bone13": {"rotate": [{}, {"time": 0.3667, "angle": -5.98}, {"time": 0.6667, "angle": -7.98}, {"time": 1.0333, "angle": -2.41}, {"time": 1.3333}]}, "bone15": {"rotate": [{}, {"time": 0.3667, "angle": -6.49}, {"time": 1.3333}]}, "bone16": {"rotate": [{}, {"time": 0.5, "angle": -10.51}, {"time": 0.8333, "angle": 2.84}, {"time": 1.1333, "angle": -12.53}, {"time": 1.3333}]}, "bone17": {"rotate": [{}, {"time": 0.5667, "angle": -5.28}, {"time": 1, "angle": -14.3}, {"time": 1.3333}]}, "bone19": {"rotate": [{}, {"time": 0.0333, "angle": 13.41}, {"time": 0.4333, "angle": 5.6}, {"time": 1.3333}], "translate": [{"time": 0.1}, {"time": 0.4333, "x": -3.8, "y": 3.53}, {"time": 1.3333}]}, "bone22": {"translate": [{}, {"time": 0.4667, "x": 32.94, "y": 0.65}, {"time": 0.9333, "x": 52.53, "y": 7.37}], "scale": [{"x": 0, "y": 0}, {"time": 0.4667}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.2667, "offset": 36, "vertices": [1.25594, 1.08681, 0.31022, -1.63174, 1.25594, 1.08681, 0.31022, -1.63174, 0.31022, -1.63174, -1.516, 0.6785, 0.31022, -1.63174, -1.516, 0.6785, -0.01204, -1.65052, -1.34711, 0.95362, -0.01204, -1.65052, -1.34711, 0.95362, 2.19628, -1.27339, -2.30048, -1.07368, -6e-05, 2.53871, -2.30048, -1.07368, -6e-05, 2.53871, -2.30048, -1.07368]}, {"time": 0.6, "offset": 36, "vertices": [-1.74948, -0.98847, 0.02236, 2.00919, -1.74948, -0.98847, 0.02236, 2.00919, 0.02236, 2.00919, 1.63559, -1.16723, 0.21328, -1.12182, -1.04225, 0.46647, -0.00828, -1.13473, -0.92614, 0.65562, -0.00828, -1.13473, -0.92614, 0.65562, 1.50994, -0.87545, -1.58158, -0.73815, -4e-05, 1.74536, -1.58158, -0.73815, -4e-05, 1.74536, -1.58158, -0.73815]}, {"time": 0.9667, "offset": 36, "vertices": [0.30006, 1.58605, 1.22156, -1.05527, 0.30006, 1.58605, 1.22156, -1.05527, 1.22156, -1.05527, -1.56414, -0.39876, 0.14542, -0.76488, -0.71062, 0.31805, -0.00564, -0.77368, -0.63146, 0.44701, -0.00564, -0.77368, -0.63146, 0.44701, 1.0295, -0.5969, -1.07835, -0.50329, -3e-05, 1.19002, -1.07835, -0.50329, -3e-05, 1.19002, -1.07835, -0.50329]}, {"time": 1.3333}]}, "s1": {"s1": [{}, {"time": 0.4, "offset": 6, "vertices": [-4.79088, -0.02789, -4.26776, 2.17719, -4.77973, -1.94424, -5.13856, 0.47004, -4.77973, -1.94424, -5.13856, 0.47004, -4.77973, -1.94424, -5.13856, 0.47004]}, {"time": 1.3333}]}}}}, "run": {"slots": {"2LEI": {"attachment": [{"name": null}]}, "BQku1": {"attachment": [{"name": "BQXIAO"}]}, "x": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.1333, "angle": -3.75}, {"time": 0.3333}, {"time": 0.5, "angle": -4.23}, {"time": 0.6667}], "translate": [{}, {"time": 0.1333, "y": 2.77}, {"time": 0.3333}, {"time": 0.5, "y": 3.39}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.1667, "angle": 2.72}, {"time": 0.3333}, {"time": 0.5333, "angle": 1.94}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.1333, "angle": -34.99}, {"time": 0.3333, "angle": 21.23}, {"time": 0.5, "angle": 47.84}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.1667, "angle": -43.44}, {"time": 0.3333, "angle": -75.6}, {"time": 0.5333, "angle": -21.6}, {"time": 0.6667}]}, "bone7": {"rotate": [{}, {"time": 0.1333, "angle": 48.29}, {"time": 0.3333}, {"time": 0.5, "angle": -36.77}, {"time": 0.6667}]}, "bone8": {"rotate": [{}, {"time": 0.1667, "angle": -9.04}, {"time": 0.3333}, {"time": 0.5, "angle": -31.09}, {"time": 0.6667}]}, "bone9": {"rotate": [{}, {"time": 0.1333, "angle": -2.86}, {"time": 0.6667}]}, "bone10": {"rotate": [{}, {"time": 0.5333, "angle": -5.92}, {"time": 0.6667}]}, "bone11": {"rotate": [{}, {"time": 0.2, "angle": -3.51}, {"time": 0.6667}]}, "bone12": {"rotate": [{}, {"time": 0.2333, "angle": 5.25}, {"time": 0.5333, "angle": -5.12}, {"time": 0.6667}]}, "bone13": {"rotate": [{}, {"time": 0.1667, "angle": -10.01}, {"time": 0.3333, "angle": -6.59}, {"time": 0.5, "angle": -16.44}, {"time": 0.6667}]}, "bone15": {"rotate": [{}, {"time": 0.2, "angle": -20.25}, {"time": 0.3333, "angle": 4.54}, {"time": 0.5, "angle": -18.62}, {"time": 0.6667}]}, "bone16": {"rotate": [{}, {"time": 0.2667, "angle": -23.18}, {"time": 0.3667, "angle": 6}, {"time": 0.5333, "angle": -28.45}, {"time": 0.6667}]}, "bone17": {"rotate": [{}, {"time": 0.2667, "angle": -7.33}, {"time": 0.5333, "angle": -13.3}, {"time": 0.6667}]}, "bone21": {"rotate": [{}, {"time": 0.2, "angle": 16.93}, {"time": 0.3333, "angle": -5.11}, {"time": 0.6667}]}, "bone20": {"rotate": [{}, {"time": 0.1667, "angle": -34.97}, {"time": 0.5333, "angle": 1.82}, {"time": 0.6667}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.1333, "offset": 36, "vertices": [0.59915, 0.92447, 0.49923, -0.98188, 0, 0, 0, 0, 1.46737, -1.0038, -1.6625, -0.6299, 1.42024, -3.12865, -3.37877, 0.62377, 3.2878, -1.90627, -3.44384, -1.60727, 3.2878, -1.90627, -3.44384, -1.60727, 3.21027, -1.86124, -3.36262, -1.56937, -1e-05, 3.71082, -3.36262, -1.56937, -1e-05, 3.71082, -3.36262, -1.56937, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2e-05, 1.48057, 1.28086, -0.74264, -1.34165, -0.62616, -2e-05, 1.48057, 1.28086, -0.74264, -1.34165, -0.62616, -2e-05, 1.48057, 1.28086, -0.74264, -1.34165, -0.62616]}, {"time": 0.3333, "offset": 36, "vertices": [0.37445, -2.40239, -2.26615, 0.88113, -2e-05, -2.98018, -2.57817, 1.49481, -2.32339, 1.27378, 2.37361, 1.17766, 1.74883, -2.45471, -3.01378, -0.03113, -1.22981, -0.91522, -0.04753, 1.53222, 2.37426, -3.00484, -3.8227, -0.22965, 0.83324, -2.4178, -2.45996, 0.69894, 1.67372, 1.93358, -2.45996, 0.69894, 1.67372, 1.93358, -2.45996, 0.69894, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0.92536, 0.80054, -0.46415, -0.83853, -0.39135, -1e-05, 0.92536, 0.80054, -0.46415, -0.83853, -0.39135, -1e-05, 0.92536, 0.80054, -0.46415, -0.83853, -0.39135]}, {"time": 0.5, "offset": 36, "vertices": [0.18723, 0.2889, 0.15601, -0.30684, 0, 0, 0, 0, 2.03738, -1.22909, -2.1733, -0.96866, 2.02265, -1.8931, -2.70963, -0.57688, 1.02744, -0.59571, -1.0762, -0.50227, 2.90826, -1.68621, -3.0463, -1.42173, 2.88404, -1.67214, -3.02092, -1.40989, 2e-05, 3.33375, -3.02092, -1.40989, 2e-05, 3.33375, -3.02092, -1.40989, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1e-05, 0.46268, 0.40027, -0.23208, -0.41927, -0.19568, -1e-05, 0.46268, 0.40027, -0.23208, -0.41927, -0.19568, -1e-05, 0.46268, 0.40027, -0.23208, -0.41927, -0.19568]}, {"time": 0.6667}]}, "s1": {"s1": [{}, {"time": 0.2, "offset": 2, "vertices": [-2.29547, -0.75582, -2.40156, 0.2921, -5.67142, 1.91805, -4.33131, 4.14747, -9.6817, 1.15218, -8.28676, 5.11779, -8.07808, 0.37067, -7.15917, 3.78073, -6.59998, 0.15431, -5.90088, 2.97347, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.31973, 0.9757, -0.78029, 1.44418, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.66688, 0.08114, -1.47454, 0.78166, -2.11573, 0.72878, -1.60583, 1.55891, -4.76828, 3.28809, -2.91994, 5.00261, -1.73922, 1.70441, -0.8505, 2.2819, 0.3348, 1.25975, 0.83822, 0.99828]}, {"time": 0.3667, "offset": 14, "vertices": [0.38067, 0.14784, 0.4081, -0.01469, -7.24539, 1.34909, -6.1219, 4.1035, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.72194, 0.0839, 1.61483, -0.60368]}, {"time": 0.5667, "offset": 6, "vertices": [-3.8314, 1.07611, -3.02822, 2.58219, -7.3083, -1.07277, -7.08558, 2.08757, -3.8314, 1.07611, -3.02822, 2.58219, -1.83794, -0.44158, -1.85391, 0.36901]}, {"time": 0.6667}]}}}}, "standBy": {"slots": {"2LEI": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffec"}, {"time": 0.4333, "color": "ffffff00"}]}, "BQku1": {"attachment": [{"name": "BQku1"}, {"time": 0.1, "name": "BQku2"}, {"time": 0.4, "name": "BQku1"}]}, "x": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.6, "angle": 1.02}, {"time": 1, "angle": -0.26}, {"time": 1.3333}]}, "bone13": {"rotate": [{}, {"time": 0.3667, "angle": -5.98}, {"time": 0.6667, "angle": -7.98}, {"time": 1.0333, "angle": -2.41}, {"time": 1.3333}]}, "bone15": {"rotate": [{}, {"time": 0.3667, "angle": -6.49}, {"time": 1.3333}]}, "bone16": {"rotate": [{}, {"time": 0.5, "angle": -10.51}, {"time": 0.8333, "angle": 2.84}, {"time": 1.1333, "angle": -12.53}, {"time": 1.3333}]}, "bone17": {"rotate": [{}, {"time": 0.5667, "angle": -5.28}, {"time": 1, "angle": -14.3}, {"time": 1.3333}]}, "bone19": {"rotate": [{}, {"time": 0.0333, "angle": 13.41}, {"time": 0.4333, "angle": 5.6}, {"time": 1.3333}], "translate": [{"time": 0.1}, {"time": 0.4333, "x": -3.8, "y": 3.53}, {"time": 1.3333}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.2667, "offset": 36, "vertices": [1.25594, 1.08681, 0.31022, -1.63174, 1.25594, 1.08681, 0.31022, -1.63174, 0.31022, -1.63174, -1.516, 0.6785, 0.31022, -1.63174, -1.516, 0.6785, -0.01204, -1.65052, -1.34711, 0.95362, -0.01204, -1.65052, -1.34711, 0.95362, 2.19628, -1.27339, -2.30048, -1.07368, -6e-05, 2.53871, -2.30048, -1.07368, -6e-05, 2.53871, -2.30048, -1.07368]}, {"time": 0.6, "offset": 36, "vertices": [-1.74948, -0.98847, 0.02236, 2.00919, -1.74948, -0.98847, 0.02236, 2.00919, 0.02236, 2.00919, 1.63559, -1.16723, 0.21328, -1.12182, -1.04225, 0.46647, -0.00828, -1.13473, -0.92614, 0.65562, -0.00828, -1.13473, -0.92614, 0.65562, 1.50994, -0.87545, -1.58158, -0.73815, -4e-05, 1.74536, -1.58158, -0.73815, -4e-05, 1.74536, -1.58158, -0.73815]}, {"time": 0.9667, "offset": 36, "vertices": [0.30006, 1.58605, 1.22156, -1.05527, 0.30006, 1.58605, 1.22156, -1.05527, 1.22156, -1.05527, -1.56414, -0.39876, 0.14542, -0.76488, -0.71062, 0.31805, -0.00564, -0.77368, -0.63146, 0.44701, -0.00564, -0.77368, -0.63146, 0.44701, 1.0295, -0.5969, -1.07835, -0.50329, -3e-05, 1.19002, -1.07835, -0.50329, -3e-05, 1.19002, -1.07835, -0.50329]}, {"time": 1.3333}]}, "s1": {"s1": [{}, {"time": 0.4, "offset": 6, "vertices": [-4.79088, -0.02789, -4.26776, 2.17719, -4.77973, -1.94424, -5.13856, 0.47004, -4.77973, -1.94424, -5.13856, 0.47004, -4.77973, -1.94424, -5.13856, 0.47004]}, {"time": 1.3333}]}}}}}}