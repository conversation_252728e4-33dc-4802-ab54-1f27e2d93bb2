import {_decorator, Component,} from 'cc';
import {WECHAT} from 'cc/env';
import {Wechat} from "./wechat/wechat";
import {Constants} from "../game/Constants";
import {WXCustomAd} from "./wechat/WXCustomAd";
import {configuration} from "../framework/configuration";
import {Public} from "../game/Public";
import {uiManager} from "../framework/uiManager";
import {clientEvent} from "../framework/clientEvent";
import {tyqSDK} from "../tyqSDK/tyq-sdk";
import {gameStorage} from "../framework/gameStorage";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = AdCtl
 * DateTime = Wed Mar 16 2022 15:05:11 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = AdCtl.ts
 * FileBasenameNoExtension = AdCtl
 * URL = db://assets/script/channel/AdCtl.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('AdCtl')
export class AdCtl extends Component {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;
    private static _instance: AdCtl;

    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new AdCtl();
        return this._instance;
    }


    //重新开始的次数
    public restartCnt:number=0;
    //需要看视频的触发次数
    private _successVideoCnt :number= 0 ;
    public get successVideoCnt(){
        return this._successVideoCnt
    }

    public isNeedShowAdToContinue(){
        if(this._successVideoCnt==0) return false
        return this.restartCnt>=this._successVideoCnt
    }

    InitParam(){
        // console.warn("-------WECHAT----", WECHAT)
        // console.warn("-------MINIGAME----", MINIGAME)
        // console.warn("-------DEV----", DEV)
        if(WECHAT ) {
            Wechat.showShareMenu()
            Wechat.getSystemInfoSync()
            Wechat.interstitialAdUnitId = Constants.AdWechatIds.interstitialAdUnitId
            Wechat.rewardedVideoAdUnitId = Constants.AdWechatIds.video
        }
        Wechat.bannerAdInterval = tyqSDK.getSwitchValue("tyq_ad_banner_switch", 3000);
        this._successVideoCnt = tyqSDK.getSwitchValue("tyq_ad_success_video_cnt", 0);
    }

    InitCreateRewardVideoAd(){
        return new Promise<void>(resolve => {
            if(WECHAT){
                Wechat.createRewardedVideoAd(()=>{resolve()})
            }else{
                resolve()
            }
        })
    }

    InitCreateInterstitialAd(){
        return new Promise<void>(resolve => {
            if(WECHAT){
                Wechat.createInterstitialAd(()=>{resolve()})
            }else{
                resolve()
            }
        })
    }
    InitCreateBannerAd(){
        AdCtl.instance.CreateBannerAd(()=>{
            console.warn("@@@@  banner加载完毕")
        })
        // return new Promise<void>(resolve => {
        //     if(WECHAT){
        //         AdCtl.instance.CreateBannerAd(()=>{resolve()})
        //     }else{
        //         resolve()
        //     }
        // })
    }
    InitCreateCustomLevelUI(){
        return new Promise<void>(resolve => {
            if(WECHAT){
                this.CreateCustomLevelUI(()=>{resolve()})
            }else{
                resolve()
            }
        })
    }
    InitCreateCustomAdMainUI(){
        return new Promise<void>(resolve => {
            if(WECHAT){
                this.CreateCustomAdMainUI(()=>{resolve()})
            }else{
                resolve()
            }
        })
    }
    InitCreateMainCanvas(){
        return new Promise<void>(resolve => {
            if(WECHAT){
                this.CreateMainCanvas(()=>{resolve()})
            }else{
                resolve()
            }
        })
    }


    InitAdPage(){
        if(WECHAT ){
            uiManager.instance.showDialog(Constants.Dialogs.initAd);
        }else{
            clientEvent.dispatchEvent(Constants.Events.finishLoading)
        }
    }

    IsPlayAd(){
        //前一段时间不出现插屏和banner广告
        let playTime = gameStorage.getInt(Constants.CacheDataKey.playTime)
        if(playTime==undefined){
            playTime = 0
        }
        let res = playTime >= 60

        //通关过的玩家就出现
        if(Public.GetTotalBreakLevel()>=7){
            res = true
        }
        return res;
    }

    ShowRewardVideoAd(cb:Function, failCb?:Function){
        if(WECHAT){
            if(Wechat.showRewardVideoAd(cb, failCb)){
                this.InitCreateRewardVideoAd()
            }
        }else{
            cb && cb()
        }
    }

    async CreateBannerAd(cb:Function, idx:number=-1){
        if(WECHAT){
            let show = false
            for(let i =0 ; i< Constants.AdWechatIds.banners1.length; i++){
                let banner = Constants.AdWechatIds.banners1[i]
                if(i==idx){
                    show = true
                }
                await Wechat.createBannerAd(i, banner, Constants.AdReloadTime,  show)
            }
            cb && cb()
        }
    }

    ShowBannerAd(which:string="UI"){
        // if(this.IsPlayAd()==false) return
        if(WECHAT){
           Wechat.showBannerAdNext(which)
        }
    }

    HideBannerAD(){
        if(WECHAT){
            Wechat.hideBannerAd()
        }
    }

//#region #####关卡的广告
    CreateCustomLevelUI(cb, show:boolean=false){
        if(WECHAT){
            // let posMode = {
            //     whichMode: IPosModeVal.centerMode,
            //     posCenterMode: {
            //         // center:true,//水平居中 PosCenterMode
            //         horizontalCenter:true,
            //         horizontalCenterUpDownMode:PosCenterSideMode.down,
            //         offsetX:35, //x偏移正负
            //         offsetY:0 //y偏移正负
            //     },
            //     posSideMode: null,
            //     winWidth: Constants.AdWechatIds.heng3.w, //窗口的宽
            //     winHeight: Constants.AdWechatIds.heng3.h, //窗口的高
            // } as IPosMode
            WXCustomAd.createCustomAd(Constants.AdFlags.levelBottom, Constants.AdWechatIds.heng3, Constants.AdReloadTime, show).then(()=>{
                cb && cb()
            })
        }
    }

    ShowCustomLevelUI(){
        // if(this.IsPlayAd()==false) return
        if(WECHAT){
            // this.CreateCustomLevelUI(null, true)
        }
    }
    HideCustomLevelUI(){
        AdCtl.instance.HideAd(Constants.AdFlags.levelBottom)
    }
//#endregion

//#region #####主菜单的广告
    CreateCustomAdMainUI(cb, show:boolean=false){
        if(WECHAT){
            // let posMode = {
            //     whichMode: IPosModeVal.centerMode,
            //     posCenterMode: {
            //         // center:true,//水平居中 PosCenterMode
            //         horizontalCenter:true,
            //         horizontalCenterUpDownMode:PosCenterSideMode.down,
            //         offsetX:-Constants.AdWechatIds.heng4.w/2-25, //x偏移正负
            //         offsetY:0 //y偏移正负
            //     },
            //     posSideMode: null,
            //     winWidth: Constants.AdWechatIds.heng4.w, //窗口的宽
            //     winHeight: Constants.AdWechatIds.heng4.h, //窗口的高
            // } as IPosMode
            WXCustomAd.createCustomAd(Constants.AdFlags.mainUIBottomLeft, Constants.AdWechatIds.heng4, Constants.AdReloadTime,show).then(()=>{
                // let posMode2 = {
                //     whichMode: IPosModeVal.centerMode,
                //     posCenterMode: {
                //         // center:true,//水平居中 PosCenterMode
                //         horizontalCenter:true,
                //         horizontalCenterUpDownMode:PosCenterSideMode.down,
                //         offsetX:Constants.AdWechatIds.heng4Copy.w/2+25, //x偏移正负
                //         offsetY:0 //y偏移正负
                //     },
                //     posSideMode: null,
                //     winWidth: Constants.AdWechatIds.heng4Copy.w, //窗口的宽
                //     winHeight: Constants.AdWechatIds.heng4Copy.h, //窗口的高
                // } as IPosMode
                WXCustomAd.createCustomAd(Constants.AdFlags.mainUIBottomRight, Constants.AdWechatIds.heng4Copy, Constants.AdReloadTime,show).then(()=>{
                    cb && cb()
                })
            })
        }
    }

    ShowCustomAdMainUI(){
        // if(this.IsPlayAd()==false) return
        // if(uiManager.instance.getDialog(Constants.Dialogs.mainUI).active==false) return
        if(WECHAT){
           // this.CreateCustomAdMainUI(null, true)
        }
    }

    HideCustomAdMainUI(){
        AdCtl.instance.HideAd(Constants.AdFlags.mainUIBottomLeft)
        AdCtl.instance.HideAd(Constants.AdFlags.mainUIBottomRight)
    }
//#endregion

//#region #####首页的图片
    CreateMainCanvas(cb, show:boolean=false){
        if(WECHAT){
            // let posMode = {
            //     whichMode: IPosModeVal.centerMode,
            //     posCenterMode: {
            //         // center:true,//水平居中 PosCenterMode
            //         verticalCenter:true,
            //         verticalCenterLeftRightMode:PosCenterSideMode.right,
            //         offsetX:-35, //x偏移正负
            //         offsetY:0 //y偏移正负
            //     },
            //     posSideMode: null,
            //     winWidth: Constants.AdWechatIds.shu3copy.w, //窗口的宽
            //     winHeight: Constants.AdWechatIds.shu3copy.h, //窗口的高
            // } as IPosMode
            WXCustomAd.createCustomAd(Constants.AdFlags.mainCanvasRight, Constants.AdWechatIds.shu3copy, Constants.AdReloadTime, show).then(()=>{
                // let posMode2 = {
                //     whichMode: IPosModeVal.centerMode,
                //     posCenterMode: {
                //         // center:true,//水平居中 PosCenterMode
                //         verticalCenter:true,
                //         verticalCenterLeftRightMode:PosCenterSideMode.left,
                //         offsetX:35, //x偏移正负
                //         offsetY:0 //y偏移正负
                //     },
                //     posSideMode: null,
                //     winWidth: Constants.AdWechatIds.shu3.w, //窗口的宽
                //     winHeight: Constants.AdWechatIds.shu3.h, //窗口的高
                // } as IPosMode
                WXCustomAd.createCustomAd(Constants.AdFlags.mainCanvasLeft, Constants.AdWechatIds.shu3, Constants.AdReloadTime, show).then(()=>{
                    cb && cb()
                })
            })
        }
    }
    ShowMainCanvas(){
        // if(this.IsPlayAd()==false) return
        // if(Public.isShowMainCanvas==false) return
        if(WECHAT){
            // this.CreateMainCanvas(null, true)
        }
    }
    HideMainCanvas(){
        AdCtl.instance.HideAd(Constants.AdFlags.mainCanvasRight)
        AdCtl.instance.HideAd(Constants.AdFlags.mainCanvasLeft)
    }
//#endregion


//#region #####首页的广告
    CreateInit(cb, show:boolean=false){
        if(WECHAT){
            // let posMode = {
            //     whichMode: IPosModeVal.centerMode,
            //     posCenterMode: {
            //         // center:true,//水平居中 PosCenterMode
            //         horizontalCenter:true,
            //         horizontalCenterUpDownMode:PosCenterSideMode.up,
            //         offsetX:0, //x偏移正负
            //         offsetY:0, //y偏移正负
            //     },
            //     posSideMode: null,
            //     winWidth: Constants.AdWechatIds.initCenter.w, //窗口的宽
            //     winHeight: Constants.AdWechatIds.initCenter.h, //窗口的高
            // } as IPosMode
            WXCustomAd.createCustomAd(Constants.AdFlags.mainCanvasCenter, Constants.AdWechatIds.initCenter.id, Constants.AdReloadTime,show).then(()=>{
                cb && cb()
            })
        }
    }
    ShowInit(){
        if(WECHAT){
            this.CreateInit(null, true)
        }
    }
    HideInit(){
        // AdCtl.instance.HideAd(Constants.AdFlags.mainCanvasRight)
        // AdCtl.instance.HideAd(Constants.AdFlags.mainCanvasLeft)
        AdCtl.instance.HideAd(Constants.AdFlags.mainCanvasCenter)
    }
//#endregion

    ShowInterstitialAd(cb?:Function){
        // if(this.IsPlayAd()==false) return
        if(WECHAT){
            Wechat.showInterstitialAd(cb)
        }
    }

    HideAllCustomADTmp(){
        if(WECHAT){
            WXCustomAd.hideCustomAdAll()
        }
    }

    ShowAllCustomADTmp(){
        if(WECHAT){
            WXCustomAd.showCustomAdAll()
        }
    }

    HideAllCustomAD(){
        if(WECHAT){
            WXCustomAd.hideAllAd()
            Wechat.hideBannerAd()
        }
    }

    HideAd(flag){
        if(WECHAT){
            WXCustomAd.hideCustomAd(flag)
        }
    }

    //分享
    Share(){

    }


    start() {
        // [3]
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
