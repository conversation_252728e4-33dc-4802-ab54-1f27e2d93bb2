
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = S12
 * DateTime = Thu Mar 10 2022 16:22:40 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = S12.ts
 * FileBasenameNoExtension = S12
 * URL = db://assets/script/game/common/S12.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('S12')
export class S12 extends Component {
    // [1]
    // dummy = '';

    // [2]
    @property(Node)
    S12Nodes:Node[] = [null, null];

    start () {
        // [3]
    }

    Init(param){
        console.log("param---", param)
        for(let i=0; i<this.S12Nodes.length; i++){
            this.S12Nodes[i].active = param.num == i;
        }
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
