{"skeleton": {"hash": "cvdrqBFwYXM", "spine": "3.8-from-4.0.09", "x": -63.31, "y": 1.94, "width": 119, "height": 150.56, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/牛郎织女/zhinv"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -5.54, "y": 49.31}, {"name": "bone2", "parent": "bone", "length": 27.12, "rotation": 105.26, "x": -0.99, "y": 4.96}, {"name": "bone3", "parent": "bone", "length": 25.34, "rotation": -104.04, "x": 0.59, "y": -8.33}, {"name": "bone4", "parent": "bone3", "length": 16.18, "rotation": 73.07, "x": 4.38, "y": 12.74}, {"name": "bone5", "parent": "bone4", "length": 15.72, "rotation": 44.82, "x": 19.31, "y": 3.26}, {"name": "bone6", "parent": "bone4", "length": 14.16, "rotation": -52.61, "x": 3.33, "y": -5.17}, {"name": "bone7", "parent": "bone2", "length": 21.21, "rotation": 75.28, "x": 21.09, "y": 1.23}, {"name": "bone8", "parent": "bone7", "length": 12.95, "rotation": -12.02, "x": 23.19, "y": -0.22}, {"name": "bone9", "parent": "bone2", "length": 15.26, "rotation": 75.49, "x": 29.1, "y": 5.01}, {"name": "bone10", "parent": "bone9", "length": 12.91, "rotation": -18.62, "x": 17.44, "y": -0.42}, {"name": "bone11", "parent": "bone2", "length": 15.96, "rotation": 9.72, "x": 29.3, "y": -1.83}, {"name": "bone12", "parent": "bone2", "length": 17.74, "rotation": -133.25, "x": 11.42, "y": 5.72}, {"name": "bone13", "parent": "bone12", "length": 13.29, "rotation": 25.43, "x": 21.06, "y": 1.77}, {"name": "bone14", "parent": "bone13", "length": 10.82, "rotation": 10.99, "x": 15.83, "y": 0.91}, {"name": "bone15", "parent": "bone2", "length": 13.48, "rotation": -125.02, "x": 22.98, "y": -2.36}, {"name": "bone16", "parent": "bone11", "length": 11.1, "rotation": -6.22, "x": 16.5, "y": 6.31}, {"name": "bone17", "parent": "bone16", "length": 2.58, "rotation": -126.73, "x": 5.77, "y": -1.86}, {"name": "bone18", "parent": "bone11", "length": 27.51, "rotation": -42.43, "x": 18.55, "y": -4.38}, {"name": "bone19", "parent": "bone18", "length": 19.39, "rotation": -36.42, "x": 32.02, "y": 1.2}, {"name": "bone20", "parent": "bone18", "length": 9.82, "rotation": 34.98, "x": -4.98, "y": 25.52}, {"name": "bone21", "parent": "bone18", "length": 11.79, "rotation": 46.17, "x": 11.58, "y": 27.89}], "slots": [{"name": "LH", "bone": "bone9", "attachment": "LH"}, {"name": "BPDY", "bone": "root", "attachment": "BPDY"}, {"name": "PD2", "bone": "root", "attachment": "PD2"}, {"name": "RH", "bone": "bone7", "attachment": "RH"}, {"name": "PD", "bone": "bone15", "attachment": "PD"}, {"name": "头1", "bone": "bone11", "attachment": "头1"}, {"name": "BQ2", "bone": "root"}, {"name": "BQ1", "bone": "bone16", "attachment": "BQ3"}, {"name": "BQ3", "bone": "root"}, {"name": "YL", "bone": "bone17", "attachment": "YL"}, {"name": "x3", "bone": "bone20", "attachment": "x2"}, {"name": "x2", "bone": "bone20"}, {"name": "x", "bone": "bone21", "attachment": "x"}], "skins": [{"name": "default", "attachments": {"BPDY": {"BPDY": {"type": "mesh", "uvs": [0.22706, 0.0148, 0.08883, 0.09791, 0.03354, 0.15425, 0.00935, 0.32047, 0.02317, 0.40076, 0.06119, 0.45287, 0.02145, 0.60641, 0.01717, 0.68365, 0.01108, 0.79376, 0.02226, 0.88112, 0.03181, 0.95575, 0.12857, 0.99519, 0.2478, 0.99237, 0.31691, 0.97124, 0.48452, 0.99237, 0.64003, 0.93321, 0.78172, 0.90786, 0.87675, 0.80644, 0.98215, 0.73178, 1, 0.64445, 0.95451, 0.60782, 0.80591, 0.60782, 0.64867, 0.5712, 0.54672, 0.51204, 0.54327, 0.38104, 0.4897, 0.30497, 0.44305, 0.24158, 0.36702, 0.09227, 0.34283, 0.07255, 0.34283, 0.01339, 0.28063, 0, 0.28674, 0.64947, 0.2704, 0.75122, 0.25702, 0.8445, 0.24514, 0.88932, 0.12032, 0.62403, 0.11883, 0.74759, 0.11883, 0.87357, 0.50666, 0.61919, 0.471, 0.75364, 0.56881, 0.65067, 0.71437, 0.68034, 0.84982, 0.68693, 0.13619, 0.51059, 0.32218, 0.46939, 0.41921, 0.4183, 0.21301, 0.49905], "triangles": [15, 39, 41, 15, 41, 16, 41, 39, 40, 17, 41, 42, 17, 16, 41, 17, 42, 18, 18, 42, 19, 19, 42, 20, 41, 21, 42, 42, 21, 20, 41, 22, 21, 39, 38, 40, 39, 31, 38, 40, 22, 41, 40, 38, 22, 31, 44, 38, 38, 44, 23, 38, 23, 22, 15, 14, 39, 14, 13, 39, 10, 37, 11, 11, 34, 12, 11, 37, 34, 12, 34, 13, 34, 33, 13, 13, 33, 39, 39, 33, 32, 10, 9, 37, 34, 37, 33, 9, 8, 37, 8, 36, 37, 37, 36, 33, 33, 36, 32, 8, 7, 36, 32, 31, 39, 32, 36, 31, 36, 35, 31, 36, 7, 35, 31, 35, 46, 35, 43, 46, 7, 6, 35, 35, 6, 43, 6, 5, 43, 31, 46, 44, 23, 44, 45, 45, 24, 23, 43, 5, 46, 46, 5, 44, 45, 44, 26, 26, 44, 5, 1, 26, 4, 1, 28, 27, 1, 0, 28, 0, 30, 28, 26, 5, 4, 2, 1, 3, 45, 25, 24, 45, 26, 25, 26, 1, 27, 1, 4, 3, 30, 29, 28], "vertices": [1, 2, 37.53, -6.35, 1, 1, 2, 32.88, 5.66, 1, 1, 2, 28.97, 11.03, 1, 2, 2, 14.7, 16.8, 0.97338, 3, -17.1, -26.6, 0.02662, 2, 2, 7.3, 17.75, 0.88533, 3, -10.18, -23.81, 0.11467, 2, 2, 1.92, 16.26, 0.70356, 3, -6.22, -19.88, 0.29644, 2, 2, -10.92, 22.85, 0.14834, 3, 8.21, -19.34, 0.85166, 2, 2, -17.69, 25.03, 0.08785, 3, 15.18, -17.93, 0.91215, 2, 2, -27.35, 28.14, 0.00162, 3, 25.12, -15.92, 0.99838, 3, 2, -35.32, 29.44, 0.00075, 3, 32.71, -13.16, 0.99901, 6, 11.36, -33.68, 0.00025, 2, 3, 39.2, -10.79, 0.99954, 6, 18.27, -33.73, 0.00046, 2, 3, 40.96, -2.87, 0.96456, 6, 22.68, -26.93, 0.03544, 2, 3, 38.54, 5.74, 0.78973, 6, 23.43, -18.01, 0.21027, 2, 3, 35.39, 10.29, 0.53525, 6, 22.08, -12.65, 0.46475, 4, 3, 34.23, 22.96, 0.04642, 6, 25.42, -0.37, 0.92627, 4, 18.47, -25.59, 0.02441, 5, -20.93, -19.87, 0.00291, 3, 6, 21.31, 11.83, 0.54241, 4, 25.67, -14.92, 0.28754, 5, -8.3, -17.38, 0.17005, 3, 6, 20.18, 22.65, 0.17499, 4, 33.58, -7.45, 0.23161, 5, 2.57, -17.66, 0.5934, 3, 6, 11.71, 30.78, 0.01814, 4, 34.89, 4.22, 0.02365, 5, 11.73, -10.31, 0.95821, 1, 5, 21.05, -5.54, 1, 1, 5, 24.27, 1.94, 1, 1, 5, 21.77, 6.03, 1, 3, 2, -26.53, -33.88, 2e-05, 4, 20.94, 17.15, 0.04215, 5, 10.95, 8.7, 0.95783, 4, 2, -20.17, -23.39, 0.02784, 3, -6.35, 25.51, 0.00096, 4, 9.09, 13.97, 0.5908, 5, 0.3, 14.8, 0.3804, 4, 2, -12.91, -17.44, 0.23418, 3, -9.77, 16.77, 0.06212, 4, -0.26, 14.71, 0.6474, 5, -5.82, 21.92, 0.05629, 4, 2, -1.22, -20.36, 0.7052, 3, -21.4, 13.6, 0.02413, 4, -6.69, 24.91, 0.2694, 5, -3.18, 33.68, 0.00127, 3, 2, 6.59, -18.33, 0.87214, 3, -27.22, 8, 0.00166, 4, -13.73, 28.84, 0.1262, 2, 2, 13.14, -16.49, 0.956, 4, -19.73, 32.04, 0.044, 2, 2, 27.89, -14.6, 0.9997, 4, -31.69, 40.89, 0.0003, 2, 2, 30.12, -13.33, 1, 4, -34.18, 41.51, 0, 1, 2, 35.37, -14.76, 1, 1, 2, 37.79, -10.58, 1, 5, 2, -19.98, 4.7, 0.12103, 3, 7.22, 0.92, 0.51669, 6, -7.59, -11.58, 0.00095, 4, -10.48, -6.17, 0.33242, 5, -27.78, 14.31, 0.0289, 5, 2, -28.69, 8.34, 0.07693, 3, 16.6, 2, 0.56494, 6, 1.57, -13.85, 0.12892, 4, -6.71, -14.82, 0.21089, 5, -31.21, 5.52, 0.01834, 5, 2, -36.7, 11.57, 0.04721, 3, 25.17, 3.11, 0.65859, 6, 9.99, -15.8, 0.15354, 4, -3.16, -22.7, 0.12941, 5, -34.24, -2.57, 0.01125, 5, 2, -40.45, 13.51, 0.03297, 3, 29.39, 3.24, 0.7115, 6, 13.98, -17.15, 0.1573, 4, -1.8, -26.69, 0.09038, 5, -36.09, -6.36, 0.00786, 5, 2, -14.44, 16.12, 0.13747, 3, 7.98, -11.76, 0.72724, 6, -11.32, -23.72, 0.00228, 4, -22.39, -10.58, 0.12237, 5, -39.33, 19.57, 0.01064, 5, 2, -25.38, 19.22, 0.05246, 3, 19.03, -9.11, 0.80165, 6, -0.03, -25.1, 0.0438, 4, -16.63, -20.39, 0.09392, 5, -42.16, 8.56, 0.00817, 5, 2, -36.56, 22.27, 0.01503, 3, 30.28, -6.3, 0.87251, 6, 11.48, -26.4, 0.06927, 4, -10.67, -30.32, 0.03974, 5, -44.94, -2.69, 0.00346, 5, 2, -21.63, -11.95, 0.05591, 3, 0.52, 16.25, 0.20771, 6, -8.52, 5.12, 0.02287, 4, 2.23, 4.71, 0.47009, 5, -11.1, 13.07, 0.24341, 5, 2, -32.86, -6.11, 0.0461, 3, 13.17, 16.65, 0.25985, 6, 3.48, 1.08, 0.20966, 4, 6.3, -7.27, 0.33403, 5, -16.66, 1.7, 0.15036, 5, 2, -25.65, -15.68, 0.03639, 3, 2.2, 21.47, 0.11731, 6, -5.11, 9.43, 0.0905, 4, 7.72, 4.62, 0.47758, 5, -7.26, 9.14, 0.27822, 5, 2, -31.16, -25.5, 0.01694, 3, 2.2, 32.72, 0.05457, 6, -1.18, 19.97, 0.0469, 4, 18.48, 7.9, 0.23981, 5, 2.68, 3.87, 0.64179, 4, 2, -34.42, -35.14, 1e-05, 6, 0.56, 30, 0.00588, 4, 27.51, 12.61, 0.03114, 5, 12.4, 0.85, 0.96297, 5, 2, -4.68, 12.23, 0.52926, 3, -2.43, -13.13, 0.35812, 6, -21.55, -21.37, 0.00029, 4, -26.74, -1.02, 0.10367, 5, -35.68, 29.42, 0.00867, 5, 2, -4.7, -2.23, 0.5453, 3, -9.49, -0.52, 0.21108, 6, -23.76, -7.08, 0.00016, 4, -16.72, 9.41, 0.23067, 5, -21.23, 29.76, 0.01279, 5, 2, -2.08, -10.49, 0.64146, 3, -15.82, 5.4, 0.12047, 6, -27.62, 0.67, 8e-05, 4, -12.9, 17.18, 0.23077, 5, -13.03, 32.58, 0.00722, 5, 2, -5.17, 6.39, 0.52248, 3, -4.86, -7.8, 0.30622, 6, -21.96, -15.53, 0.00026, 4, -22.34, 2.86, 0.16014, 5, -29.83, 29.07, 0.01091], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 12, 14, 14, 16, 16, 18, 18, 20], "width": 75, "height": 92}}, "BQ1": {"BQ1": {"x": 5.93, "y": -0.28, "rotation": -108.76, "width": 11, "height": 21}, "BQ2": {"x": 5.93, "y": -0.28, "rotation": -108.76, "width": 11, "height": 21}, "BQ3": {"x": 5.93, "y": -0.28, "rotation": -108.76, "width": 11, "height": 21}}, "LH": {"LH": {"type": "mesh", "uvs": [0.2639, 0.12541, 0.18206, 0.03099, 0.09184, 0, 0.02994, 0.04883, 0.02155, 0.12646, 0, 0.21774, 0.02889, 0.27124, 0.08659, 0.28803, 0.12331, 0.35518, 0.16947, 0.39085, 0.17251, 0.57292, 0.18342, 0.73477, 0.23434, 0.88026, 0.30527, 0.99483, 0.37074, 0.99847, 0.45803, 0.89117, 0.56896, 0.77478, 0.69809, 0.65112, 0.89995, 0.48562, 0.98724, 0.42197, 0.99088, 0.29467, 0.91086, 0.1801, 0.72355, 0.18374, 0.56351, 0.1801, 0.41438, 0.13645], "triangles": [15, 14, 12, 14, 13, 12, 16, 15, 11, 15, 12, 11, 17, 16, 10, 17, 10, 23, 23, 10, 24, 0, 24, 9, 16, 11, 10, 17, 22, 18, 17, 23, 22, 24, 10, 9, 19, 18, 20, 20, 18, 22, 21, 20, 22, 9, 8, 0, 8, 7, 0, 4, 0, 7, 0, 4, 1, 1, 3, 2, 3, 1, 4, 6, 5, 7, 4, 7, 5], "vertices": [1, 10, 13.46, -4.99, 1, 1, 10, 19.76, -8.81, 1, 1, 10, 25.39, -8.92, 1, 1, 10, 27.98, -5.05, 1, 1, 10, 27.05, -0.54, 1, 1, 10, 26.6, 4.97, 1, 1, 10, 24.01, 7.45, 1, 2, 9, 39.19, 0.01, 0.00013, 10, 20.47, 7.35, 0.99987, 2, 9, 37.07, 4, 0.00931, 10, 17.19, 10.46, 0.99069, 2, 9, 34.37, 6.14, 0.04368, 10, 13.95, 11.62, 0.95632, 2, 9, 34.33, 16.88, 0.25316, 10, 10.48, 21.79, 0.74684, 2, 9, 33.82, 26.44, 0.37944, 10, 6.94, 30.68, 0.62056, 2, 9, 30.92, 35.06, 0.44251, 10, 1.45, 37.93, 0.55749, 2, 9, 26.83, 41.87, 0.46297, 10, -4.61, 43.08, 0.53703, 2, 9, 22.97, 42.14, 0.46727, 10, -8.35, 42.1, 0.53273, 2, 9, 17.73, 35.87, 0.50272, 10, -11.31, 34.49, 0.49728, 2, 9, 11.1, 29.09, 0.63398, 10, -15.43, 25.95, 0.36602, 2, 9, 3.39, 21.89, 0.85643, 10, -20.44, 16.66, 0.14357, 2, 9, -8.65, 12.29, 0.99912, 10, -28.78, 3.71, 0.00088, 1, 9, -13.85, 8.6, 1, 1, 9, -14.16, 1.09, 1, 1, 9, -9.53, -5.73, 1, 1, 9, 1.53, -5.66, 1, 2, 9, 10.97, -6, 0.87294, 10, -4.36, -7.35, 0.12706, 2, 9, 19.73, -8.69, 0.05292, 10, 4.81, -7.1, 0.94708], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 59, "height": 59}}, "PD": {"PD": {"type": "mesh", "uvs": [0.12355, 0.52083, 0.2614, 0.52905, 0.40778, 0.35843, 0.56411, 0.26387, 0.68633, 0.24948, 0.78724, 0.34199, 0.78866, 0.47149, 0.72755, 0.5825, 0.64512, 0.67089, 0.5229, 0.73256, 0.36515, 0.70584, 0.25429, 0.64622, 0.16902, 0.64006, 0, 0.68322, 0.00132, 0.75106, 0.15481, 0.86001, 0.33104, 0.9669, 0.48026, 1, 0.62949, 0.96896, 0.81708, 0.84356, 0.93788, 0.64211, 0.99615, 0.48999, 1, 0.28443, 0.93931, 0.10764, 0.72471, 0, 0.51863, 0, 0.36941, 0.13026, 0.16334, 0.35432, 0.1136, 0.44683], "triangles": [4, 25, 24, 5, 4, 24, 3, 25, 4, 26, 25, 3, 23, 5, 24, 5, 23, 22, 2, 26, 3, 27, 26, 2, 21, 6, 5, 22, 21, 5, 0, 28, 27, 1, 27, 2, 0, 27, 1, 20, 6, 21, 14, 13, 12, 20, 7, 6, 19, 7, 20, 8, 7, 19, 15, 14, 12, 16, 11, 10, 15, 12, 11, 16, 15, 11, 18, 9, 8, 18, 8, 19, 17, 10, 9, 17, 9, 18, 16, 10, 17], "vertices": [-9.2, 10.8, 1.46, 14.14, 9.39, 27.15, 19.52, 36.41, 28.56, 40.52, 38, 38.41, 40.56, 31.62, 38.01, 24.1, 33.4, 17.18, 25.25, 10.58, 12.72, 7.67, 3.14, 7.78, -3.48, 5.76, -15.54, -1.14, -14.16, -4.68, -0.39, -6.22, 15.06, -7.02, 27.07, -4.68, 37.85, 1.04, 49.78, 12.79, 55.17, 26.72, 56.73, 36.33, 53.13, 47.27, 45.16, 54.92, 26.76, 54.72, 11.05, 49.07, 2.14, 38.12, -9.32, 20.67, -11.36, 14.43], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 81, "height": 56}}, "PD2": {"PD2": {"type": "mesh", "uvs": [0, 0.36304, 0.16666, 0.76414, 0.31872, 0.96468, 0.47281, 1, 0.71003, 1, 0.82763, 0.93603, 0.85601, 0.80998, 1, 0.82716, 1, 0.60943, 0.83776, 0.39742, 0.65529, 0.38596, 0.52958, 0.40315, 0.45051, 0.46618, 0.34102, 0.40888, 0.24573, 0.19687, 0.13219, 0, 0.01257, 0], "triangles": [11, 10, 4, 5, 4, 6, 6, 10, 9, 10, 6, 4, 6, 8, 7, 6, 9, 8, 3, 11, 4, 2, 12, 3, 3, 12, 11, 2, 13, 12, 2, 1, 13, 14, 1, 0, 0, 16, 15, 1, 14, 13, 14, 0, 15], "vertices": [1, 12, -7, -4.43, 1, 1, 12, 6.9, -7.49, 1, 2, 12, 17.79, -6.92, 0.93253, 13, -6.68, -6.44, 0.06747, 2, 12, 27.02, -2.94, 0.05691, 13, 3.36, -6.81, 0.94309, 2, 13, 18.76, -6.12, 0.28622, 14, 1.54, -7.45, 0.71378, 2, 13, 26.33, -4.3, 0.00332, 14, 9.31, -7.12, 0.99668, 1, 14, 11.56, -4.52, 1, 1, 14, 20.76, -6.28, 1, 1, 14, 21.5, -1.33, 1, 1, 14, 11.78, 5.04, 1, 2, 13, 14.58, 7.83, 0.25699, 14, 0.09, 7.04, 0.74301, 2, 13, 6.43, 7.07, 0.92606, 14, -8.05, 7.84, 0.07394, 3, 12, 19.97, 7.22, 0.10085, 13, 1.36, 5.39, 0.89726, 14, -13.35, 7.16, 0.00189, 2, 12, 13.07, 5.05, 0.89517, 13, -5.81, 6.39, 0.10483, 1, 12, 5.31, 6.45, 1, 1, 12, -3.33, 6.98, 1, 1, 12, -10.19, 3.33, 1], "hull": 17, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32], "width": 65, "height": 23}}, "RH": {"RH": {"type": "mesh", "uvs": [0.27538, 0.11376, 0.20323, 0.0234, 0.08113, 0, 0, 0.10811, 0, 0.30388, 0.10518, 0.31518, 0.15883, 0.38859, 0.17733, 0.6333, 0.22543, 0.86296, 0.31238, 1, 0.40488, 1, 0.65462, 0.74437, 0.88772, 0.55236, 1, 0.41871, 1, 0.23235, 0.92472, 0.16458, 0.73232, 0.17211, 0.53807, 0.17964], "triangles": [11, 10, 8, 10, 9, 8, 8, 7, 11, 7, 17, 11, 12, 17, 16, 12, 11, 17, 17, 6, 0, 17, 7, 6, 13, 16, 15, 13, 12, 16, 15, 14, 13, 6, 5, 0, 3, 0, 5, 0, 3, 1, 1, 3, 2, 5, 4, 3], "vertices": [1, 8, 12.99, -9.33, 1, 1, 8, 18.12, -13.55, 1, 1, 8, 25.32, -13.45, 1, 1, 8, 28.71, -6.47, 1, 1, 8, 26.49, 4.46, 1, 1, 8, 20.38, 3.88, 1, 2, 7, 40.86, 3.55, 0.00067, 8, 16.5, 7.36, 0.99933, 2, 7, 39.92, 17.51, 0.10427, 8, 12.67, 20.82, 0.89573, 2, 7, 37.25, 30.62, 0.21763, 8, 7.33, 33.09, 0.78237, 2, 7, 32.28, 38.48, 0.25226, 8, 0.83, 39.74, 0.74774, 2, 7, 26.92, 38.53, 0.26833, 8, -4.42, 38.67, 0.73167, 2, 7, 12.3, 24.1, 0.64672, 8, -15.72, 21.51, 0.35328, 2, 7, -1.32, 13.28, 0.98684, 8, -26.79, 8.1, 0.01316, 1, 7, -7.91, 5.72, 1, 1, 7, -8.01, -4.9, 1, 1, 7, -3.68, -8.8, 1, 1, 7, 7.49, -8.48, 1, 2, 7, 18.76, -8.15, 0.69903, 8, -2.69, -8.69, 0.30097], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 58, "height": 57}}, "x": {"x": {"x": 6.4, "y": -2.66, "rotation": -118.71, "width": 24, "height": 22}}, "x3": {"x2": {"x": 4.36, "y": -0.09, "rotation": -82.14, "width": 18, "height": 14}}, "YL": {"YL": {"x": 1.65, "y": -0.37, "rotation": 3.96, "width": 9, "height": 8}}, "头1": {"头1": {"type": "mesh", "uvs": [0.09068, 0.6246, 0.12692, 0.63093, 0.139, 0.66363, 0.08706, 0.71321, 0.04478, 0.767, 0.04961, 0.85244, 0.16074, 0.95687, 0.26704, 0.9959, 0.39992, 0.99273, 0.48689, 0.98007, 0.61493, 0.92839, 0.69465, 0.99168, 0.75988, 1, 0.80095, 0.99379, 0.94469, 0.89463, 0.9604, 0.80497, 0.91933, 0.7881, 0.82028, 0.76595, 0.89879, 0.68578, 0.93141, 0.58768, 0.91812, 0.48326, 0.84927, 0.41891, 0.95436, 0.38977, 0.98214, 0.33703, 0.98093, 0.26636, 0.97852, 0.15771, 0.91812, 0.07016, 0.81303, 0.03008, 0.72847, 0.03008, 0.67049, 0.08704, 0.61493, 0.14506, 0.61251, 0.06067, 0.534, 0.01637, 0.47601, 0, 0.38421, 0, 0.29362, 0.04696, 0.24288, 0.1208, 0.26946, 0.20096, 0.30328, 0.25792, 0.35643, 0.30011, 0.25617, 0.32332, 0.20181, 0.37712, 0.17524, 0.42247, 0.11484, 0.40665, 0.05807, 0.41931, 0.00613, 0.44357, 0, 0.49736, 0.03029, 0.50369, 0.0013, 0.57542, 0.04841, 0.60074], "triangles": [37, 36, 35, 34, 37, 35, 33, 38, 37, 33, 39, 38, 34, 33, 37, 33, 30, 39, 32, 30, 33, 30, 32, 31, 24, 27, 25, 25, 27, 26, 23, 21, 24, 27, 24, 28, 23, 22, 21, 47, 45, 44, 46, 45, 47, 47, 0, 49, 48, 47, 49, 47, 44, 43, 43, 42, 47, 47, 1, 0, 42, 1, 47, 2, 1, 42, 19, 18, 21, 19, 21, 20, 24, 21, 28, 17, 21, 18, 21, 29, 28, 21, 30, 29, 30, 21, 39, 5, 4, 3, 14, 16, 15, 21, 17, 39, 42, 39, 2, 7, 6, 2, 3, 2, 6, 5, 3, 6, 41, 40, 39, 39, 42, 41, 2, 39, 17, 17, 10, 2, 10, 9, 2, 11, 10, 17, 17, 12, 11, 9, 8, 2, 17, 14, 13, 14, 17, 16, 8, 7, 2, 13, 12, 17], "vertices": [2, 11, 33.44, 12.23, 0.30187, 18, -0.22, 22.31, 0.69813, 2, 11, 32.09, 10.39, 0.3788, 18, 0.03, 20.03, 0.6212, 2, 11, 29.67, 10.69, 0.56926, 18, -1.96, 18.62, 0.43074, 2, 11, 27.83, 15.09, 0.82292, 18, -6.29, 20.63, 0.17708, 2, 11, 25.48, 19.08, 0.91905, 18, -10.72, 21.99, 0.08095, 2, 11, 19.85, 21.37, 0.9751, 18, -16.41, 19.88, 0.0249, 1, 11, 10.22, 18.26, 1, 1, 11, 4.93, 13.45, 1, 1, 11, 1.65, 5.89, 1, 1, 11, 0.19, 0.62, 1, 2, 11, 0.17, -8.12, 0.7009, 18, -11.04, -15.17, 0.2991, 2, 11, -5.99, -10.71, 0.57089, 18, -13.84, -21.23, 0.42911, 2, 11, -8.24, -14.12, 0.54176, 18, -13.2, -25.27, 0.45824, 2, 11, -8.91, -16.62, 0.52569, 18, -12.01, -27.57, 0.47431, 2, 11, -6.29, -27.67, 0.44444, 18, -2.62, -33.96, 0.55556, 2, 11, -0.93, -31.24, 0.42246, 18, 3.74, -32.97, 0.57754, 2, 11, 1.23, -29.44, 0.41345, 18, 4.12, -30.19, 0.58655, 2, 11, 5.25, -24.53, 0.29341, 18, 3.78, -23.86, 0.70659, 3, 11, 8.35, -31.35, 0.09482, 18, 10.67, -26.79, 0.90073, 19, -0.56, -35.19, 0.00445, 3, 11, 13.81, -36.12, 0.03359, 18, 17.92, -26.63, 0.93428, 19, 5.18, -30.76, 0.03213, 3, 11, 20.88, -38.51, 0.00801, 18, 24.75, -23.62, 0.88092, 19, 8.88, -24.29, 0.11106, 3, 11, 26.82, -36.57, 0.00061, 18, 27.82, -18.18, 0.63636, 19, 8.13, -18.08, 0.36303, 2, 18, 31.75, -23.77, 0.32226, 19, 14.61, -20.25, 0.67774, 2, 18, 35.84, -24.29, 0.25318, 19, 18.21, -18.24, 0.74682, 2, 18, 40.61, -22.72, 0.1512, 19, 21.11, -14.14, 0.8488, 2, 18, 47.92, -20.26, 0.03354, 19, 25.53, -7.82, 0.96646, 2, 18, 52.73, -14.82, 0.00114, 19, 26.17, -0.59, 0.99886, 1, 19, 22.59, 5.55, 1, 1, 19, 18.35, 8.64, 1, 1, 19, 13.06, 7.49, 1, 1, 19, 7.85, 6.19, 1, 1, 19, 11.26, 11.12, 1, 1, 19, 9.19, 16.53, 1, 1, 19, 6.97, 19.59, 1, 1, 19, 2.37, 22.94, 1, 2, 18, 42.68, 22.61, 0.00312, 19, -4.13, 23.56, 0.99688, 2, 18, 36.74, 24.04, 0.02322, 19, -9.77, 21.18, 0.97678, 2, 18, 31.8, 20.76, 0.08743, 19, -11.79, 15.61, 0.91257, 3, 11, 51.48, -10.71, 0.00045, 18, 28.57, 17.54, 0.20391, 19, -12.48, 11.11, 0.79564, 3, 11, 47.37, -12.43, 0.00625, 18, 26.7, 13.5, 0.52038, 19, -11.58, 6.75, 0.47336, 3, 11, 48.5, -6.1, 0.03502, 18, 23.27, 18.94, 0.82619, 19, -17.58, 9.08, 0.13879, 3, 11, 46.46, -1.43, 0.06914, 18, 18.61, 21.01, 0.86726, 19, -22.55, 7.98, 0.0636, 3, 11, 44.24, 1.42, 0.11974, 18, 15.05, 21.61, 0.85777, 19, -25.78, 6.35, 0.02249, 3, 11, 46.84, 4.34, 0.15536, 18, 14.99, 25.52, 0.83976, 19, -28.14, 9.47, 0.00488, 3, 11, 47.51, 7.91, 0.16914, 18, 13.08, 28.61, 0.82976, 19, -31.51, 10.81, 0.0011, 3, 11, 47.31, 11.56, 0.17818, 18, 10.47, 31.16, 0.8218, 19, -35.13, 11.32, 2e-05, 2, 11, 44.01, 13.52, 0.18645, 18, 6.71, 30.38, 0.81355, 3, 11, 42.8, 12, 0.19556, 18, 6.85, 28.45, 0.80442, 19, -36.44, 6.99, 1e-05, 2, 11, 38.95, 15.78, 0.23242, 18, 1.45, 28.64, 0.76758, 2, 11, 36.08, 13.9, 0.25359, 18, 0.61, 25.31, 0.74641], "hull": 50, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 0, 98], "width": 62, "height": 71}}}}], "animations": {"end": {"slots": {"x": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}]}, "x3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2333, "color": "fffffff3"}, {"time": 0.9333, "color": "ffffff00"}]}, "YL": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00"}], "attachment": [{"name": null}]}}, "bones": {"bone2": {"translate": [{}, {"time": 0.7, "y": -0.52}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.3, "angle": -3.53}, {"time": 0.7333, "angle": 3.97}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.4667, "angle": -9.7}, {"time": 0.8667, "angle": 12.58}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.3, "angle": -9.51}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 1.66}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": 1.28}, {"time": 1}]}, "bone11": {"translate": [{}, {"time": 0.7, "x": -0.83, "y": -0.05}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.4, "angle": -11.5}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.4667, "angle": -5.61}, {"time": 1}]}, "bone14": {"rotate": [{}, {"time": 0.6333, "angle": -11.96}, {"time": 1}]}, "bone17": {"translate": [{"time": 0.1333}, {"time": 0.7667, "x": -9.05, "y": -4.75}, {"time": 1}]}, "bone20": {"translate": [{"time": 0.2}, {"time": 0.9, "x": 16.31, "y": 23.15}]}, "bone21": {"translate": [{}, {"time": 0.4667, "x": 22.47, "y": 19.19}, {"time": 0.8333, "x": 38.14, "y": 28.7}]}}, "deform": {"default": {"LH": {"LH": [{}, {"time": 0.5667, "offset": 26, "vertices": [-3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679]}, {"time": 1}]}, "PD": {"PD": [{"offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}, {"time": 0.5, "offset": 2, "vertices": [0.54596, -3.2758, 5.50302, -9.70118, 5.822, -19.37675, 5.77749, -22.60917, 4.57749, -21.90439, 2.22465, -20.0991, 2.22465, -14.12178, 2.26916, -10.88937, 6.19937, -5.54634, 6.04637, 1.03256, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, 1.04607, 1.302, 1.95018, -1.21379, 1.36504, -8.37357, 2.26916, -16.86668, 2.22465, -20.0991, 2.22465, -20.0991, 5.4561, -25.30543, 5.2811, -27.69098, 5.60249, -24.99472, 5.822, -19.37675, 5.50302, -9.70118]}, {"time": 1, "offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}]}, "RH": {"RH": [{}, {"time": 0.3667, "offset": 16, "vertices": [1.93671, 0, 1.89426, 0.40324, 4.70344, 0.55336, 4.48515, 1.52053, 4.70344, 0.55336, 4.48515, 1.52053, 4.70344, 0.55336, 4.48515, 1.52053]}, {"time": 0.6333, "offset": 20, "vertices": [4.38308, 1.70372, 3.93229, 2.57897, 4.38308, 1.70372, 3.93229, 2.57897, 4.38308, 1.70372, 3.93229, 2.57897]}, {"time": 1}]}}}}, "run": {"slots": {"BQ1": {"attachment": [{"name": "BQ1"}]}, "x": {"attachment": [{"name": null}]}, "x3": {"attachment": [{"name": null}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.2333, "angle": -2.5}, {"time": 0.4333}, {"time": 0.7333, "angle": -3.36}, {"time": 1}], "translate": [{}, {"time": 0.2333, "y": -2.34}, {"time": 0.4333, "y": -0.81}, {"time": 0.7333, "y": -2.65}, {"time": 1}]}, "bone2": {"translate": [{}, {"time": 0.3, "y": -0.62}, {"time": 0.4667}, {"time": 0.7333, "y": -0.9}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.3333, "angle": 7.24}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.3, "angle": -3.53}, {"time": 0.7333, "angle": 3.97}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.3333, "angle": -9.7}, {"time": 0.6333, "angle": -16.01}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.3, "angle": -9.51}, {"time": 0.6333, "angle": -31.13}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.4333, "angle": 5.36}, {"time": 1}]}, "bone8": {"rotate": [{}, {"time": 0.7, "angle": -8.48}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.5, "angle": -0.33}, {"time": 1}]}, "bone10": {"rotate": [{}, {"time": 0.5, "angle": -3.9}, {"time": 1}]}, "bone11": {"translate": [{}, {"time": 0.3, "x": -0.27}, {"time": 0.4667, "x": -0.02}, {"time": 0.7333, "x": -0.81}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.4, "angle": -11.5}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.3333, "angle": -12.63}, {"time": 0.4667, "angle": -5.61}, {"time": 1}]}, "bone14": {"rotate": [{}, {"time": 0.4333, "angle": -23.78}, {"time": 0.6667, "angle": 3.17}, {"time": 0.7333, "angle": -11.96}, {"time": 1}]}, "bone15": {"rotate": [{}, {"time": 0.3333, "angle": 5.9}, {"time": 1}]}, "bone19": {"rotate": [{}, {"time": 0.3667, "angle": -5.75}, {"time": 0.5667, "angle": 0.49}, {"time": 0.8, "angle": -7.24}, {"time": 1}]}}, "deform": {"default": {"BPDY": {"BPDY": [{}, {"time": 0.2, "offset": 18, "vertices": [-0.26239, 0.18469, 0.313, -0.07067, 0, 0, 0, 0, -2.32914, -0.76357, 1.85192, 1.60579, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.98521, 0.28728, 1.18227, -1.62044, -0.06994, -2.00467, 3.40359, 0.28986, 1.84782, -2.87299, -0.31938, -3.40095, -0.31938, -3.40095, -0.31938, -3.40095, -0.31938, -3.40095, -1.99041, 0.2488, 1.18227, -1.62044, -0.06994, -2.00467, -1.99041, 0.2488, 1.9315, 0.54121, 1.18227, -1.62044, -0.06994, -2.00467, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.21864, 1.22269, 0.66837, -1.04692, 0.48686, -1.14269, -0.76703, -0.97694, -1.19686, -0.28809, -1.59621, 2.79336, 2.55322, -1.95753, 2.15338, -2.39036, -1.03397, -3.04659, -2.64382, -1.83332, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.98732, -2.21399, -1.757, 1.67019, -1.45794, 1.93673, 0.99534, 2.21041, 2.12935, 1.15856, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15959, -2.87239, -1.2592, 2.5866, -0.76634, 2.77287, 2.04526, 2.02313, 2.8476, 0.409, -1.99041, 0.2488, 1.9315, 0.54121, 1.98521, 0.28728, 1.18227, -1.62044, -0.06994, -2.00467, -3.35881, 0.622, 3.40359, 0.28986, 1.84782, -2.87299, -0.31938, -3.40095]}, {"time": 0.4667, "offset": 19, "vertices": [0.87379, 0.34117, -0.80448, 0, 0, 0, 0, -2.02908, -0.42116, 1.67562, 1.16552, 1.53192, -0.40715, -1.56724, -0.23752, -1.56507, -0.25135, -1.56724, -0.23752, -1.56507, -0.25135, -1.56724, -0.23752, -1.56507, -0.25135, -1.56724, -0.23752, -1.56507, -0.25135, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.9926, 0.14364, 0.59114, -0.81022, -0.03497, -1.00234, 1.70179, 0.14493, 0.92391, -1.43649, -0.15969, -1.70048, -0.15969, -1.70048, -2.00954, -4.05669, -2.00954, -4.05669, -0.9952, 0.1244, 0.59114, -0.81022, -0.03497, -1.00234, -0.9952, 0.1244, 0.96575, 0.27061, 0.59114, -0.81022, -0.03497, -1.00234, 0.27763, -1.81266, -1.02593, 1.51996, 1.21006, 1.37788, 1.83076, 0.10555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02593, 1.69327, 0.62173, -1.57521, 0.35385, -1.65609, -1.28103, -1.10759, -1.68765, -0.09442, -1.26016, 2.20529, 2.0157, -1.54541, 1.70003, -1.88713, -0.81629, -2.4052, -2.08723, -1.44736, 1.53192, -0.40715, -1.56724, -0.23752, -1.56507, -0.25135, -0.69971, 1.42232, 0.16926, 1.57606, 0.77946, -1.74788, -1.3871, 1.31857, -1.151, 1.529, 0.78579, 1.74506, 1.68107, 0.91465, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.12599, -2.26768, -0.99411, 2.04205, -0.605, 2.18911, 1.61468, 1.59721, 2.2481, 0.32289, -0.9952, 0.1244, 0.96575, 0.27061, 0.9926, 0.14364, 0.59114, -0.81022, -0.03497, -1.00234, -3.72665, 2.4979, 4.46168, -1.01982, 0.62061, -4.41673, -2.00954, -4.05669]}, {"time": 0.6333, "offset": 18, "vertices": [0.24516, 1.5629, 0.36934, -1.53829, 0.72067, 3.11751, 0.68131, -3.12634, -1.20102, 1.60138, 1.72737, -1.0115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55526, -3.62532, -2.05187, 3.03991, 2.42012, 2.75577, 3.66151, 0.2111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27049, 2.16385, 0.57509, -2.10349, 0.22084, -2.16948, -1.79503, -1.23824, -2.17843, 0.09924, -0.92412, 1.61721, 1.47818, -1.1333, 1.24669, -1.38389, -0.59861, -1.76381, -1.53063, -1.0614, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57161, -1.28178, -1.01721, 0.96695, -0.84407, 1.12126, 0.57625, 1.27971, 1.23278, 0.67075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0924, -1.66297, -0.72901, 1.49751, -0.44367, 1.60535, 1.1841, 1.17128, 1.64861, 0.23679]}, {"time": 0.8333, "offset": 18, "vertices": [0.51029, 2.44449, 0.53824, -2.44183, 1.65451, 3.37124, 0.01678, -3.75533, 0.24407, 3.38585, 1.23452, -3.23724, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47954, -3.13096, -1.77207, 2.62538, 2.0901, 2.37998, 3.16221, 0.18231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.53216, 2.96349, 0.71593, -2.92996, 0.00019, -2.99222, -2.58603, -1.53275, -2.89728, 0.5911, 0.28417, 3.74784, 1.35245, -3.56595, 0.2285, -3.64055, -3.02126, -2.17746, -3.41819, 0.60147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.49366, -1.10699, -0.8785, 0.83509, -0.72897, 0.96836, 0.49767, 1.1052, 1.06468, 0.57928, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0798, -1.4362, -0.6296, 1.2933, -0.38317, 1.38643, 1.02263, 1.01156, 1.4238, 0.2045]}, {"time": 1}]}, "LH": {"LH": [{}, {"time": 0.5667, "offset": 26, "vertices": [-3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679]}, {"time": 1}]}, "PD": {"PD": [{"offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}, {"time": 0.3333, "offset": 2, "vertices": [0.54596, -3.2758, 4.3722, -8.59906, 4.05546, -16.6054, 4.21288, -18.16389, 3.01288, -17.45911, 0.66004, -15.65382, 0.66004, -12.99723, 0.70455, -9.76482, 3.02446, -2.03232, 3.44656, 2.41717, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, 0.60182, 5.14836, 1.50593, 2.63257, -0.19957, -7.24902, 0.70455, -12.4214, 0.66004, -15.65382, 0.66004, -15.65382, 4.07004, -19.36222, 3.99226, -20.42247, 4.1351, -19.22413, 4.69115, -15.89268, 4.3722, -8.59906]}, {"time": 0.5, "offset": 2, "vertices": [0.54596, -3.2758, 4.82453, -9.03991, 4.18992, -16.21163, 4.44831, -16.93316, 3.24831, -16.22838, 0.89547, -14.42309, 0.89547, -10.43821, 0.93998, -7.20579, 2.82229, -2.68997, 3.53194, 0.69482, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.28311, 3.53371, 0.621, 1.01792, 0.03587, -4.69, 0.93998, -11.19067, 0.89547, -14.42309, 0.89547, -14.42309, 4.23405, -18.73066, 4.11739, -20.32103, 4.33165, -18.52352, 5.14347, -15.14255, 4.82453, -9.03991]}, {"time": 0.7, "offset": 2, "vertices": [0.54596, -3.2758, 4.28175, -13.79669, 4.02856, -16.68415, 4.16581, -23.69583, 2.96579, -17.70525, 0.61295, -15.89996, 0.61295, -10.76981, 0.65746, -7.5374, 1.72045, -6.32846, 2.08504, -1.66602, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.56563, 1.04363, 0.33848, -1.47217, -0.24666, -7.76083, 0.65746, -12.66755, 0.61295, -15.89996, 0.61295, -15.89996, 4.03723, -19.48854, 1.44152, -20.15638, 4.09581, -24.65005, 4.60071, -21.3285, 4.28173, -8.51089]}, {"time": 1, "offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}]}, "RH": {"RH": [{}, {"time": 0.3667, "offset": 16, "vertices": [-2.65018, -2.11358, -2.15203, -2.61906, -5.73281, -2.00847, -5.18898, -3.15808, -5.73281, -2.00847, -5.18898, -3.15808, -5.73281, -2.00847, -5.18898, -3.15808, -1.64807, -4.60629, -0.65288, -4.84849]}, {"time": 0.4667, "offset": 16, "vertices": [-2.65018, -2.11358, -2.15203, -2.61906, -7.80548, -4.29821, -6.73949, -5.82919, -7.80548, -4.29821, -6.73949, -5.82919, -7.80548, -4.29821, -6.73949, -5.82919, -1.64807, -4.60629, -0.65288, -4.84849]}, {"time": 0.6333, "offset": 16, "vertices": [-2.65018, -2.11358, -2.15203, -2.61906, -5.73281, -2.00847, -5.18898, -3.15808, -5.73281, -2.00847, -5.18898, -3.15808, -5.73281, -2.00847, -5.18898, -3.15808, -1.64807, -4.60629, -0.65288, -4.84849]}, {"time": 1}]}}}}, "standBy": {"slots": {"BQ1": {"attachment": [{"name": "BQ1"}, {"time": 0.0333, "name": "BQ2"}, {"time": 0.1, "name": "BQ1"}]}, "x": {"attachment": [{"name": null}]}, "x3": {"attachment": [{"name": null}]}, "YL": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00"}]}}, "bones": {"bone2": {"translate": [{}, {"time": 0.7, "y": -0.52}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.3, "angle": -3.53}, {"time": 0.7333, "angle": 3.97}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.4667, "angle": -9.7}, {"time": 0.8667, "angle": 12.58}, {"time": 1}]}, "bone6": {"rotate": [{}, {"time": 0.3, "angle": -9.51}, {"time": 1}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 1.66}, {"time": 1}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": 1.28}, {"time": 1}]}, "bone11": {"translate": [{}, {"time": 0.7, "x": -0.83, "y": -0.05}, {"time": 1}]}, "bone12": {"rotate": [{}, {"time": 0.4, "angle": -11.5}, {"time": 1}]}, "bone13": {"rotate": [{}, {"time": 0.4667, "angle": -5.61}, {"time": 1}]}, "bone14": {"rotate": [{}, {"time": 0.6333, "angle": -11.96}, {"time": 1}]}, "bone17": {"translate": [{"time": 0.1333}, {"time": 0.7667, "x": -9.05, "y": -4.75}, {"time": 1}]}}, "deform": {"default": {"LH": {"LH": [{}, {"time": 0.5667, "offset": 26, "vertices": [-3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679, -3.4443, -1.23255, -2.87037, -2.2679]}, {"time": 1}]}, "PD": {"PD": [{"offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}, {"time": 0.5, "offset": 2, "vertices": [0.54596, -3.2758, 5.50302, -9.70118, 5.822, -19.37675, 5.77749, -22.60917, 4.57749, -21.90439, 2.22465, -20.0991, 2.22465, -14.12178, 2.26916, -10.88937, 6.19937, -5.54634, 6.04637, 1.03256, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, 1.04607, 1.302, 1.95018, -1.21379, 1.36504, -8.37357, 2.26916, -16.86668, 2.22465, -20.0991, 2.22465, -20.0991, 5.4561, -25.30543, 5.2811, -27.69098, 5.60249, -24.99472, 5.822, -19.37675, 5.50302, -9.70118]}, {"time": 1, "offset": 2, "vertices": [0.54596, -3.2758, 3.46754, -7.71736, 3.78652, -17.39294, 3.74201, -20.62535, 2.54201, -19.92057, 0.18917, -18.11528, 0.18917, -18.11528, 0.23368, -14.88287, 0.0677, -11.78619, -0.0853, -5.20729, -1.53537, 0.5843, 0, 0, 0, 0, 0, 0, 0, 0, -0.98941, -2.6915, -0.0853, -5.20729, -0.67044, -12.36707, 0.23368, -14.88287, 0.18917, -18.11528, 0.18917, -18.11528, 3.74201, -20.62535, 3.74201, -20.62535, 3.74201, -20.62535, 3.78652, -17.39294, 3.46754, -7.71736]}]}, "RH": {"RH": [{}, {"time": 0.3667, "offset": 16, "vertices": [1.93671, 0, 1.89426, 0.40324, 4.70344, 0.55336, 4.48515, 1.52053, 4.70344, 0.55336, 4.48515, 1.52053, 4.70344, 0.55336, 4.48515, 1.52053]}, {"time": 0.6333, "offset": 20, "vertices": [4.38308, 1.70372, 3.93229, 2.57897, 4.38308, 1.70372, 3.93229, 2.57897, 4.38308, 1.70372, 3.93229, 2.57897]}, {"time": 1}]}}}}}}