
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, EventTouch, UITransform, ITweenOption, RigidBody2D, Sprite, Sprite<PERSON>rame} from 'cc';
import {Constants} from "../Constants";
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level8')
export class Level8 extends LevelDialogBase {

    @property(Node)
    EndNode:Node = null

    @property(Node)
    Water:Node = null

    @property(Node)
    PipelineNode:Node = null

    @property(Node)
    StoneNode:Node = null

    @property(Node)
    StoneNodeSpine:Node = null

    @property(Node)
    LevelInfoNode:Node = null

    //底部管道节点
    @property(Node)
    PipeNode:Node = null
    @property(Node)
    Bubbles:Node[] = []
    @property(Node)
    StartNode:Node = null

    @property(SpriteFrame)
    StoneSpriteFrames:SpriteFrame[] = []

    touchPath=[]
    isTouchLevelInfo:boolean = false
    stoneStat:number = 0
    isShowBubbles:boolean = false

    calTime:number = 0
    bubbleCnt:number = 0
    intervalTime:number = 0.3
    start(){
        super.start()
        //水需要判断
        this.Water.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onWaterContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        this.PipeNode.active = false

        Public.MoveWater(this.Water, this.Water.getChildByName("Wave"))
    }

    update(deltaTime: number) {
        super.update(deltaTime);

        this.calTime+= deltaTime
        if(this.isShowBubbles==true && this.calTime>this.intervalTime ){
            this.calTime = 0
            let bNode = this.Bubbles[this.bubbleCnt%5]
            bNode.active = true
            bNode.position = new Vec3(this.StartNode.position.x, this.StartNode.position.y, this.StartNode.position.z)
            console.log("-------isShowBubbles-----",  bNode.position)

            // tween(bNode).delay(0.1).call(()=>{
            // }).start()
            let bPos = bNode.position
            bNode.setScale(new Vec3(0.1,0.1,1))
            tween(bNode).call(()=>{
                let dx = Public.RanInt(-30,30)
                let dy = Public.RanInt(-10,30)
                let endX = Public.RanInt(-60,60)
                let endY = 100-this.bubbleCnt*5
                Public.Bezier2DShowPoint(bNode, new Vec3(endX,endY,0), 0.5, dx, dy, 1, ()=>{
                    bNode.active = false
                })
            }).to(1,{scale:new Vec3(0.3,0.3,1)}).start()
            this.bubbleCnt+=1
        }
    }

    onRoleBeginContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {

        if(otherCollider.node.name=="StoneNode" && this.isJumping==true){
            audioManager.instance.playSound(Constants.Sounds.out)
            //石头裂开
            this.stoneStat += 1
            if(this.stoneStat<=2){
                this.StoneNode.getComponent(Sprite).spriteFrame = this.StoneSpriteFrames[this.stoneStat]
            }
            //触发震动
            Public.ShakeCamera()
            if(this.stoneStat==3){
                //播放碎裂动画，
                tween(this.StoneNode).delay(0.3).call(()=>{
                    audioManager.instance.playSound(Constants.Sounds.crush)
                    this.StoneNode.active = false
                    this.StoneNodeSpine.active = true
                    this.showKey()
                }).start()

            }
        }
        // console.log("-------otherCollider1---------", otherCollider.node.name)
        if(otherCollider.node.name!="Water"){
            // console.log("-------otherCollider2---------", otherCollider.node.name)
            this.isInWater = false
            this.roleRun()
            this.roleStandby()
        }
        super.onRoleBeginContact (selfCollider, otherCollider, contact)
    }


    onWaterContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        // console.log("-------onWaterContact1---------", otherCollider.node.name)
        Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
            if(this.isInWater == false){
                console.log("-------onWaterContact2---------", otherCollider.node.name)
                this.isInWater = true
                this.roleRun()
                this.roleStandby()
            }
        })
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        //判断关卡牌子的手指的滑动行为
        if(this.LevelInfoNode.active==true && Public.IsPointInNodeArea2D(this.LevelInfoNode, pos)==true){
            this.isTouchLevelInfo = true
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.LevelInfoNode.active==true){
            if(this.isTouchLevelInfo==true){
                let pos = event.getLocation()
                // console.log("-----------isTouchPic move---", pos)
                if(this.touchPath.length<50){
                    this.touchPath.push(pos)
                }
            }
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.LevelInfoNode.active==true){
            if(this.isTouchLevelInfo==true){
                this.isTouchLevelInfo=false
                //便利所有点，如果距离和第一个点大于，100并且在格子内，那么就ok
                for(let i=0; i<this.touchPath.length; i++){
                    let dis = Public.GetDistance2D(this.touchPath[0], this.touchPath[i])
                    // console.log("-----------isTouchPic dis---", dis, i, this.touchPicPath.length)
                    //拉动的距离够长的时候，让牌子直接旋转飞走到右上角消失
                    if(dis>100){
                        this.PipelineNode.active=true
                        audioManager.instance.playSound(Constants.Sounds.drop)
                        Public.Bezier2DShow(this.LevelInfoNode, this.EndNode, 0.5, -200, 200, 1, ()=>{
                            this.LevelInfoNode.active=false
                            //开始漏水
                            let wPos = this.Water.position
                            this.isShowBubbles= true
                            this.scheduleOnce(()=>{
                                this.isShowBubbles=false
                            },2)
                            audioManager.instance.playSound(Constants.Sounds.waterFlow)
                            tween(this.Water).to(5,{position:new Vec3(wPos.x,wPos.y-300,wPos.z)}).call(()=>{
                                this.Water.active = false
                                this.isInWater = false

                                this.roleStandby()
                            }).start()
                        })
                        tween(this.LevelInfoNode).by(1, {angle:-360}).start()
                        break
                    }
                }
                // console.log("-----------isTouchPic end---", this.touchPicPath)
                this.touchPath=[]
            }
        }
    }


}
