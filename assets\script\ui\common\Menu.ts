
import { _decorator, Component, Node, Label } from 'cc';
import {uiManager} from "../../framework/uiManager";
import {dialogBase} from "../../framework/dialogBase";
import {Constants} from "../../game/Constants";
import {audioManager} from "../../framework/audioManager";
import {configuration} from "../../framework/configuration";
import {Public} from "../../game/Public";
import {clientEvent} from "../../framework/clientEvent";
import TyqEventMgr from "../../tyqSDK/tyq-event-mgr";
import {gameStorage} from "../../framework/gameStorage";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Menu
 * DateTime = Fri Feb 25 2022 17:36:06 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Menu.ts
 * FileBasenameNoExtension = Menu
 * URL = db://assets/script/ui/common/Menu.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Menu')
export class Menu extends dialogBase {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;
    @property(Label)
    MusicLabel:Label = null
    @property(Label)
    SoundLabel:Label = null

    start () {
        // [3]
    }

    show(){

        let stat = gameStorage.getString('music');
        console.log("----------show----", stat)
        if(stat=='true'){
            this.MusicLabel.string = '关闭音乐'
        }else{
            this.MusicLabel.string = '开启音乐'
        }

        let stat2 = gameStorage.getString('sound');
        if(stat2=='true'){
            this.SoundLabel.string = '关闭音效'
        }else{
            this.SoundLabel.string = '开启音效'
        }
    }

    OnClickCloseMusic(){
        TyqEventMgr.ins.sendEvent("点击关闭音乐");
        let stat = gameStorage.getString('music');
        console.log("----------stat----", stat)
        if(stat=='true'){
            audioManager.instance.closeMusic()
            this.MusicLabel.string = '开启音乐'
        }else{
            audioManager.instance.openMusic()
            this.MusicLabel.string = '关闭音乐'
        }
    }

    OnClickCloseSound(){
        TyqEventMgr.ins.sendEvent("点击关闭音效");
        let stat = gameStorage.getString('sound');
        if(stat=='true'){
            audioManager.instance.closeSound()
            this.SoundLabel.string = '开启音效'
        }else{
            audioManager.instance.openSound()
            this.SoundLabel.string = '关闭音效'
        }
    }

    OnClickToMainUI(){
        this.close()
        uiManager.instance.destroyDialog(Constants.Dialogs.ops)
        uiManager.instance.destroyDialog(Public.GetRightLevelPath())
        uiManager.instance.showDialog(Constants.Dialogs.mainUI)
    }

    OnClickBack(){
        this.close()
        clientEvent.dispatchEvent(Constants.Events.resetLevelInsertTimeout)
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
