import {_decorator, Collider2D, Contact2DType, Node, tween, Vec3, EventTouch, UITransform, sp} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
import {uiManager} from "../../framework/uiManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level11')
export class Level11 extends LevelDialogBase {

    //整体摆动
    @property(Node)
    FishNetNode:Node = null

    //底部拿来顶的碰撞体
    @property(Node)
    FishNetBottom:Node = null

    //鱼的节点
    @property(Node)
    FishNodes:Node[] = [null, null, null]

    //门下降用的
    @property(Node)
    DoorAll:Node = null

    //鱼那边顶部的线，上升时需要收缩
    @property(Node)
    TopLine:Node = null

    @property(Node)
    FishEndPos:Node = null
    @property(Node)
    FishEndPos2:Node = null
    @property(Node)
    Fish:Node = null

    isFlying:boolean = false
    isTouchFish:boolean = false
    oriPos=null
    //是否替换了位置
    isChgPos:boolean = false

    calTime:number = 0
    isDropFish:boolean = false

    start(){
        super.start()
        this.FishNetBottom.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onFishNetBottomContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        // super.superShow()
        this.levelLabel.string = `第${Public.CurLevelNum}关`
        uiManager.instance.showDialog(Constants.Dialogs.ops, {offsetVec:new Vec3(-380, -130, 0)})

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        let p =this.FishNodes[0].position
        this.oriPos = new Vec3(p.x, p.y, p.z)
        this.Fish.active = false
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        this.calTime += deltaTime
        if(this.calTime>0.5){
            this.calTime=0
            for(let i=0; i<this.FishNodes.length; i++){
                let fishNode = this.FishNodes[i]
                let s = fishNode.getScale()
                let fishWp = this.FishNodes[0].getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let roleWp = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                if(fishWp.x > roleWp.x){
                    fishNode.setScale(new Vec3(Math.abs(s.x), s.y, s.z))
                }else{
                    fishNode.setScale(new Vec3(-Math.abs(s.x), s.y, s.z))
                }
            }
        }
    }

    //鱼飞起来，
    flyFish(){
        if(this.isFlying==false){
            this.isFlying = true
            for(let i=0; i<this.FishNodes.length; i++){
                let fishNode = this.FishNodes[i]
                let pos = this.FishNodes[i].position
                let randInt = Public.RanInt(50,80)

                //如果替换了位置，掉落时，鱼会掉出来
                let end = new Vec3(pos.x, pos.y, pos.z)
                if(i==0 && this.isChgPos==true && this.isDropFish==false){
                    this.isDropFish=true
                    end = this.FishEndPos2.position
                    tween(this.FishNodes[i])
                        .to(0.15, {position: new Vec3(pos.x, pos.y+randInt, pos.z)},{easing:"quadIn"})
                        .delay(0.2)
                        .to(0.6, {position: end},{easing:"smooth"}).call(()=>{
                        this.FishNodes[0].active = false
                        this.Fish.active = true
                        //漏网成功，现在需要拉动2个框
                        let dpos = this.doorNode.position
                        let fpos = this.FishNetNode.position
                        tween(this.doorNode).to(0.5,{position: new Vec3(dpos.x, dpos.y-80, dpos.z)}).start()
                        tween(this.FishNetNode).to(0.5,{position: new Vec3(fpos.x, fpos.y+30, fpos.z)}).start()
                        tween(this.TopLine).to(0.5,{scale: new Vec3(1,0.85,1)}).start()
                    }).start()
                }else{
                    //正常的情况
                    tween(this.FishNodes[i])
                        .to(0.15, {position: new Vec3(pos.x, pos.y+randInt, pos.z)},{easing:"quadIn"})
                        .call(()=>{
                            fishNode.getComponent(sp.Skeleton).setAnimation(0, "scare", false)
                        }).delay(0.3).call(()=>{
                            fishNode.getComponent(sp.Skeleton).setAnimation(0, "standBy", true)
                        })
                        .to(0.6, {position: end},{easing:"backInOut"}).start()
                }

            }
            this.scheduleOnce(()=>{
                this.isFlying = false
            },0.9)
        }
        //让网扭曲
        tween(this.FishNetNode.getChildByName("FishNet"))
            .to(0.3,{scale:new Vec3(1,0.8,1)})
            .to(0.3,{scale:new Vec3(1,1,1)})
            .start()
    }

    onFishNetBottomContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.FishNetNode.position.y
            tween(this.FishNetNode)
                .call(()=>{
                    this.flyFish()
                })
                .to(0.05,{position:new Vec3(this.FishNetNode.position.x,y+10,this.FishNetNode.position.z)})
                .to(0.1,{position:new Vec3(this.FishNetNode.position.x,y,this.FishNetNode.position.z)})
                .call(()=>{
                    //左右晃动一次
                    tween(this.FishNetNode).by(0.2, {angle:1}).by(0.2, {angle:-3}).by(0.2, {angle:2}).by(0.2, {angle:-1}).to(0.2, {angle:0}).start()
                    tween(this.doorNode).by(0.2, {angle:-2}).by(0.2, {angle:3}).by(0.2, {angle:1}).by(0.2, {angle:-1}).to(0.2, {angle:0}).start()
                    if(this.isLocked==true){
                        this.showKey()
                    }
                }).start()


        })
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        //判断手指点击的位置是否是小鱼的位置
        let pos = event.getUILocation()
        const waterNode = this.FishNodes[0].getComponent(UITransform)!;
        let wordPos = waterNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let dis = Public.GetDistance2D(wordPos, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(dis<60){
            this.isTouchFish = true
            // console.log("self, touch fish-------", this.isTouchFish)
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.isTouchFish == true){
            let pos = this.FishNodes[0].position
            // console.log("event---touch move", event.getDeltaX(), event.getDeltaY())
            let ePos = event.getUIDelta()
            this.FishNodes[0].setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.isTouchFish == true){
            let fishPos = this.FishNodes[0].getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let ePos = this.FishEndPos.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(fishPos, ePos)
            // console.log("----------move left1----------",wordPos, pos, leftDis)
            if(dis<60){
                this.FishNodes[0].setPosition(this.FishEndPos.position)
                this.isChgPos=true
                // console.log("self, end-------", this.isTouchFish)
            }else{
                this.FishNodes[0].setPosition(this.oriPos)
            }
            this.isTouchFish = false
        }
    }


}
