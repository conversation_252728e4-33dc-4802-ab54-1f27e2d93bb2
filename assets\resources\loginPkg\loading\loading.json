{"skeleton": {"hash": "/f0a31tvo44", "spine": "3.8-from-4.0.09", "x": -605.93, "y": -26.05, "width": 1297, "height": 558, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/加载页"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 26.71, "rotation": 93.37, "x": -466.73, "y": 28.55}, {"name": "bone2", "parent": "root", "length": 17.02, "rotation": -169.38, "x": -524.25, "y": -6.48}, {"name": "bone3", "parent": "root", "length": 23.71, "rotation": -14.04, "x": -416.02, "y": -10.14}, {"name": "bone4", "parent": "root", "length": 14.13, "rotation": 28.89, "x": -387.12, "y": -8.69}, {"name": "bone5", "parent": "root", "length": 12.8, "rotation": 29.98, "x": -377.31, "y": 0.69}, {"name": "bone6", "parent": "root", "x": 292.18, "y": 453.47}, {"name": "bone7", "parent": "bone6", "x": 31.02, "y": -10.61}, {"name": "bone8", "parent": "bone6", "x": -29.04, "y": -1.7}, {"name": "bone9", "parent": "root", "length": 35.22, "rotation": 84.47, "x": 379.82, "y": 182.64}, {"name": "bone10", "parent": "bone9", "length": 29.11, "rotation": -18.55, "x": 43.32, "y": -1.49}, {"name": "bone11", "parent": "bone10", "length": 51.23, "rotation": -18.24, "x": 32.61, "y": -0.91}, {"name": "bone12", "parent": "bone", "length": 38.98, "rotation": -11.2, "x": 45.24, "y": 47.66}, {"name": "bone13", "parent": "bone12", "length": 30.89, "rotation": -28.77, "x": 49.81, "y": -8.52}, {"name": "bone14", "parent": "bone13", "length": 20.84, "rotation": -88.7, "x": 29.61, "y": -1.84}, {"name": "bone15", "parent": "bone14", "length": 14.06, "rotation": -5.6, "x": 30.64, "y": 0.87}, {"name": "bone16", "parent": "bone12", "length": 25.8, "rotation": 53.39, "x": 7.73, "y": 21.8}, {"name": "bone17", "parent": "bone12", "length": 29.83, "rotation": -44.89, "x": 11.04, "y": -25.66}, {"name": "bone18", "parent": "root", "x": -551.33, "y": 273.54}, {"name": "bone19", "parent": "root", "length": 8.02, "rotation": 105.12, "x": -394.93, "y": 316.72}, {"name": "bone20", "parent": "bone19", "length": 11.93, "rotation": -30.38, "x": 7.38, "y": -1.56}, {"name": "bone21", "parent": "bone20", "length": 10.81, "rotation": -30.53, "x": 12.67, "y": -1.1}, {"name": "bone22", "parent": "bone21", "length": 9.3, "rotation": 9.95, "x": 12.46, "y": -1.61}, {"name": "bone23", "parent": "bone19", "length": 9.62, "rotation": 28.99, "x": 6.44, "y": 2.16}, {"name": "bone24", "parent": "bone23", "length": 8.41, "rotation": 40.17, "x": 10.49, "y": 1.49}, {"name": "bone25", "parent": "bone24", "length": 7.31, "rotation": -17.92, "x": 10.48, "y": 0.42}, {"name": "bone26", "parent": "root", "length": 3.78, "rotation": 111.04, "x": -435.31, "y": 348.5}, {"name": "bone27", "parent": "bone26", "length": 6.29, "rotation": -34.77, "x": 3.02, "y": -1.6}, {"name": "bone28", "parent": "bone27", "length": 6.11, "rotation": -55.41, "x": 7.05, "y": -0.79}, {"name": "bone29", "parent": "bone26", "length": 5.48, "rotation": 39.22, "x": 2.68, "y": 1.3}, {"name": "bone30", "parent": "bone29", "length": 4.97, "rotation": 64.74, "x": 5.85, "y": 0.99}, {"name": "bone31", "parent": "root", "length": 10.88, "rotation": 170.07, "x": -543.48, "y": -0.72}, {"name": "bone32", "parent": "root", "x": -299.75, "y": 331.01}, {"name": "bone33", "parent": "root", "x": -265.86, "y": 467.65}, {"name": "bone34", "parent": "bone33", "x": -24.22, "y": -102.37}, {"name": "bone35", "parent": "root", "x": -313.74, "y": 369.13}, {"name": "bone36", "parent": "root", "x": -60.02, "y": 483.61}, {"name": "bone37", "parent": "root", "x": 636.06, "y": 383.63}, {"name": "bone38", "parent": "root", "length": 32.83, "rotation": 90, "x": -5.68, "y": 188.65}, {"name": "bone39", "parent": "root", "length": 59.39, "rotation": 98.13, "x": -147.7, "y": 71.84}, {"name": "bone40", "parent": "root", "length": 6.27, "rotation": -6.84, "x": 68.44, "y": 500.41}, {"name": "bone41", "parent": "root", "length": 6.08, "rotation": 177.27, "x": 44.69, "y": 502.61}, {"name": "bone42", "parent": "root", "length": 7.35, "rotation": 34.82, "x": 459.5, "y": 338.69}, {"name": "bone43", "parent": "bone42", "length": 9.68, "rotation": -22.3, "x": 9.94, "y": -1.8}, {"name": "bone44", "parent": "bone43", "length": 10.29, "rotation": -31.89, "x": 14.01, "y": -2.31}, {"name": "bone45", "parent": "bone42", "length": 8.44, "rotation": 49.82, "x": 7.71, "y": 3.27}, {"name": "bone46", "parent": "bone45", "length": 9.63, "rotation": 31.22, "x": 10.53, "y": 0.2}, {"name": "bone47", "parent": "root", "length": 3.3, "rotation": 75.43, "x": 512.06, "y": 354.14}, {"name": "bone48", "parent": "bone47", "length": 6.39, "rotation": -43.48, "x": 3.01, "y": -1.19}, {"name": "bone49", "parent": "bone48", "length": 6.96, "rotation": -49.55, "x": 6.96, "y": -0.43}, {"name": "bone50", "parent": "bone47", "length": 5.16, "rotation": 23.1, "x": 3.18, "y": 1.49}, {"name": "bone51", "parent": "bone50", "length": 5.96, "rotation": 38.2, "x": 5.88, "y": 0.47}, {"name": "bone52", "parent": "root", "length": 3.64, "rotation": 59.26, "x": 469.61, "y": 388.38}, {"name": "bone53", "parent": "bone52", "length": 4.91, "rotation": -59.26, "x": 4.62, "y": -1.48}, {"name": "bone54", "parent": "bone53", "length": 7.84, "rotation": -23.52, "x": 5.92, "y": -0.51}, {"name": "bone55", "parent": "bone52", "length": 5.02, "rotation": 36.54, "x": 3.16, "y": 1.97}, {"name": "bone56", "parent": "bone55", "length": 6.94, "rotation": 46.63, "x": 6.19, "y": 0.73}, {"name": "bone57", "parent": "root", "x": -185.5, "y": 431.32}, {"name": "bone58", "parent": "bone11", "length": 27.5, "rotation": -52.37, "x": 55.66, "y": -11}, {"name": "bone59", "parent": "bone58", "length": 28.6, "rotation": -41.59, "x": 32.09, "y": -0.98}, {"name": "bone60", "parent": "bone11", "x": 28.94, "y": -17.7}, {"name": "bone61", "parent": "bone60", "length": 23.93, "rotation": -98.02, "x": 8.14, "y": -9.61}, {"name": "bone62", "parent": "bone61", "length": 26.14, "rotation": -64.03, "x": 29.47, "y": -4.59}, {"name": "bone63", "parent": "bone11", "x": 30.49, "y": 21.98}, {"name": "bone64", "parent": "bone63", "length": 28.73, "rotation": 142.22, "x": -3.52, "y": 13.21}, {"name": "bone65", "parent": "bone64", "length": 23.3, "rotation": 46.1, "x": 35.91, "y": 1.94}, {"name": "bone66", "parent": "bone11", "length": 42.06, "rotation": 65.6, "x": 49.96, "y": 9.94}, {"name": "bone67", "parent": "bone66", "length": 34.67, "rotation": 63.01, "x": 48.27, "y": 4.18}], "slots": [{"name": "yun4", "bone": "bone18", "attachment": "yun4"}, {"name": "yun3", "bone": "bone35", "attachment": "yun3"}, {"name": "yun2", "bone": "bone36", "attachment": "yun2"}, {"name": "yun1", "bone": "bone37", "attachment": "yun1"}, {"name": "bli2", "bone": "bone33", "attachment": "bli2"}, {"name": "bli1", "bone": "bone32", "attachment": "bli1"}, {"name": "bli3", "bone": "bone57", "attachment": "bli1"}, {"name": "ssdad", "bone": "bone39", "attachment": "ssdad"}, {"name": "tx1", "bone": "bone41", "attachment": "tx1"}, {"name": "tx2", "bone": "bone40", "attachment": "tx2"}, {"name": "haiou5", "bone": "bone19", "attachment": "haiou5"}, {"name": "haiou4", "bone": "bone26", "attachment": "haiou4"}, {"name": "haiou3", "bone": "bone52", "attachment": "haiou3"}, {"name": "haiou2", "bone": "bone47", "attachment": "haiou2"}, {"name": "haiou1", "bone": "bone42", "attachment": "haiou1"}, {"name": "yeshu", "bone": "bone9", "attachment": "yeshu"}, {"name": "ty", "bone": "bone6", "attachment": "ty"}, {"name": "xx2", "bone": "bone8", "attachment": "xx2"}, {"name": "xxz", "bone": "bone7", "attachment": "xxz"}, {"name": "bt", "bone": "bone38", "attachment": "bt"}, {"name": "cb", "bone": "bone39", "attachment": "cb"}, {"name": "zh", "bone": "bone16", "attachment": "zh"}, {"name": "yh", "bone": "bone17", "attachment": "yh"}, {"name": "naicha", "bone": "bone12", "attachment": "naicha"}, {"name": "sd", "bone": "bone15", "attachment": "sd"}, {"name": "xc", "bone": "bone", "attachment": "xc"}, {"name": "bb5", "bone": "bone5", "attachment": "bb5"}, {"name": "bb4", "bone": "bone4", "attachment": "bb4"}, {"name": "bb3", "bone": "bone3", "attachment": "bb3"}, {"name": "bb2", "bone": "bone2", "attachment": "bb2"}, {"name": "bb1", "bone": "bone31", "attachment": "bb1"}], "skins": [{"name": "default", "attachments": {"bb1": {"bb1": {"x": 7.11, "y": 2.59, "rotation": -170.07, "width": 20, "height": 16}}, "bb2": {"bb2": {"x": -0.45, "y": 4.74, "rotation": 169.38, "width": 56, "height": 20}}, "bb3": {"bb3": {"x": 7.95, "y": 1.56, "rotation": 14.04, "width": 50, "height": 19}}, "bb4": {"bb4": {"x": 13.25, "y": 0.84, "rotation": -28.89, "width": 28, "height": 27}}, "bb5": {"bb5": {"x": -152.9, "y": 99.38, "scaleX": -1, "rotation": -29.98, "width": 32, "height": 28}}, "bli1": {"bli1": {"x": 1.08, "y": -2.87, "width": 29, "height": 37}}, "bli2": {"bli2": {"type": "mesh", "uvs": [0.50873, 0.10616, 0.70755, 0.2282, 0.94705, 0.17356, 1, 0.09887, 0.92897, 0.02237, 0.78889, 0, 0.68496, 0], "triangles": [4, 3, 5, 0, 5, 3, 6, 5, 0, 2, 1, 3, 1, 0, 3], "vertices": [-11.62, -1.39, -1.28, -17.13, 11.17, -10.08, 13.93, -0.45, 10.23, 9.42, 2.95, 12.31, -2.45, 12.31], "hull": 7, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 0, 12], "width": 52, "height": 129}}, "bli3": {"bli1": {"x": 1.08, "y": -2.87, "width": 29, "height": 37}}, "bt": {"bt": {"x": 37.3, "y": -11.76, "rotation": -90, "width": 580, "height": 328}}, "cb": {"cb": {"x": 29.92, "y": -169.7, "rotation": -98.13, "width": 730, "height": 161}}, "haiou1": {"haiou1": {"type": "mesh", "uvs": [0.15975, 0.01873, 0.05277, 0.05993, 0.05591, 0.27345, 0.07794, 0.3596, 0.15031, 0.46074, 0.14402, 0.678, 0.01501, 0.79038, 0.04333, 0.89526, 0.12828, 0.9552, 0.20065, 0.9477, 0.35798, 0.82034, 0.50587, 0.8166, 0.64117, 0.9065, 0.78905, 0.98142, 0.97155, 0.98142, 1, 0.77914, 0.90862, 0.77914, 0.77647, 0.71546, 0.5688, 0.63305, 0.45237, 0.61058, 0.32966, 0.65553, 0.34225, 0.5394, 0.30449, 0.34462, 0.22582, 0.18729, 0.21324, 0.05619], "triangles": [1, 23, 2, 0, 23, 1, 24, 23, 0, 3, 2, 23, 3, 23, 22, 4, 3, 22, 4, 22, 21, 21, 5, 4, 20, 5, 21, 11, 19, 18, 10, 20, 19, 10, 19, 11, 7, 6, 5, 12, 18, 17, 11, 18, 12, 10, 9, 5, 10, 5, 20, 8, 7, 5, 9, 8, 5, 13, 17, 16, 12, 17, 13, 14, 16, 15, 13, 16, 14], "vertices": [1, 46, 17.71, -4.48, 1, 1, 46, 18.48, 1.08, 1, 1, 46, 10.34, 4.85, 1, 2, 45, 13.36, 8.28, 0.0026, 46, 6.61, 5.44, 0.9974, 2, 45, 9.47, 4.28, 0.34708, 46, 1.21, 4.04, 0.65292, 2, 42, 5.08, 5.95, 0.20212, 45, 0.35, 3.74, 0.79788, 2, 42, -2.91, 5.76, 0.93287, 45, -4.95, 9.72, 0.06713, 2, 42, -4.26, 1.33, 0.99315, 45, -9.2, 7.9, 0.00685, 2, 42, -2.21, -3.16, 0.99959, 43, -10.73, -5.87, 0.00041, 2, 42, 0.94, -4.97, 0.97072, 43, -7.13, -6.35, 0.02928, 2, 42, 10.45, -5.07, 0.04029, 43, 1.71, -2.83, 0.95971, 2, 43, 8.96, -4.28, 0.6511, 44, -3.24, -4.34, 0.3489, 2, 43, 14.75, -9.44, 0.00057, 44, 4.4, -5.66, 0.99943, 1, 44, 12.42, -6.18, 1, 1, 44, 21.03, -3.15, 1, 1, 44, 19.55, 5.33, 1, 1, 44, 15.24, 3.82, 1, 1, 44, 8.12, 4.15, 1, 2, 43, 13.71, 2.56, 0.50503, 44, -2.82, 3.97, 0.49497, 3, 43, 8.23, 4.74, 0.99777, 44, -8.63, 2.94, 9e-05, 45, 4.61, -11.35, 0.00214, 3, 42, 13.24, 1.42, 0.00374, 43, 1.83, 4.23, 0.56869, 45, 2.16, -5.42, 0.42757, 3, 43, 3.5, 8.86, 0.05975, 45, 7.07, -5.59, 0.83001, 46, -5.95, -3.15, 0.11024, 1, 46, 2.23, -5.03, 1, 1, 46, 9.89, -4.37, 1, 1, 46, 15.12, -6.2, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 50, "height": 42}}, "haiou2": {"haiou2": {"type": "mesh", "uvs": [0.03092, 0.11176, 0.03499, 0.25844, 0.10833, 0.38067, 0.17964, 0.47846, 0.25501, 0.55723, 0.25705, 0.74737, 0.2652, 0.88862, 0.35484, 0.96739, 0.4567, 0.93751, 0.55652, 0.75824, 0.63801, 0.69848, 0.79895, 0.79083, 0.94359, 0.79626, 0.98841, 0.65502, 0.95581, 0.52464, 0.83766, 0.45401, 0.66857, 0.40784, 0.56467, 0.40784, 0.44651, 0.53278, 0.43225, 0.42142, 0.3691, 0.26659, 0.24279, 0.1851, 0.16945, 0.03842, 0.12871, 0, 0.01666, 0.02484], "triangles": [4, 3, 20, 3, 21, 20, 3, 2, 21, 2, 1, 21, 21, 0, 22, 22, 0, 23, 0, 21, 1, 0, 24, 23, 5, 4, 18, 4, 19, 18, 19, 4, 20, 12, 11, 13, 11, 14, 13, 11, 15, 14, 11, 10, 15, 10, 16, 15, 9, 8, 5, 5, 18, 9, 9, 18, 10, 18, 17, 10, 10, 17, 16, 8, 7, 5, 5, 7, 6], "vertices": [1, 51, 9.5, 0.33, 1, 1, 51, 7.31, 2.5, 1, 1, 51, 4.05, 2.96, 1, 2, 50, 4.91, 3.64, 0.1446, 51, 1.19, 3.09, 0.8554, 2, 50, 2.96, 1.79, 0.85693, 51, -1.48, 2.84, 0.14307, 2, 47, 1.34, 3.24, 0.29901, 50, -1, 2.33, 0.70099, 2, 47, -1.47, 2.27, 0.86011, 50, -3.96, 2.54, 0.13989, 3, 47, -2.44, -0.58, 0.96109, 48, -4.38, -3.3, 0.03841, 50, -5.97, 0.31, 0.0005, 2, 47, -1.11, -3.18, 0.6298, 48, -1.63, -4.28, 0.3702, 3, 47, 3.23, -4.94, 0.00301, 48, 2.73, -2.57, 0.96327, 49, -1.11, -4.6, 0.03372, 2, 48, 5.33, -2.71, 0.45398, 49, 0.68, -2.72, 0.54602, 1, 49, 5.57, -3.2, 1, 1, 49, 9.46, -2.08, 1, 1, 49, 9.76, 1.12, 1, 1, 49, 8.06, 3.46, 1, 1, 49, 4.46, 3.87, 1, 2, 48, 9.29, 2.02, 0.33199, 49, -0.35, 3.36, 0.66801, 4, 47, 10.41, -3.31, 0.00169, 48, 6.82, 3.56, 0.91631, 49, -3.12, 2.48, 0.0773, 50, 4.78, -7.25, 0.0047, 3, 47, 7.04, -0.76, 0.09826, 48, 2.62, 3.08, 0.49686, 50, 2.67, -3.59, 0.40487, 4, 47, 9.2, 0.21, 0.03208, 48, 3.52, 5.28, 0.07752, 50, 5.04, -3.54, 0.85062, 51, -3.14, -2.63, 0.03978, 4, 47, 11.9, 2.74, 0.00019, 48, 3.74, 8.97, 0.00022, 50, 8.52, -2.27, 0.30651, 51, 0.38, -3.79, 0.69309, 2, 50, 10.74, 0.97, 0.00055, 51, 4.13, -2.61, 0.99945, 1, 51, 7.73, -3.45, 1, 1, 51, 9.12, -3.25, 1, 1, 51, 11.04, -0.72, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 28, "height": 21}}, "haiou3": {"haiou3": {"type": "mesh", "uvs": [0.0026, 0.20359, 0.03615, 0.33303, 0.14688, 0.43395, 0.24251, 0.52171, 0.24419, 0.67309, 0.21734, 0.83105, 0.24586, 0.89468, 0.36498, 0.93417, 0.46564, 0.8508, 0.55288, 0.78279, 0.64516, 0.86396, 0.7777, 0.93636, 0.94043, 0.94075, 0.9958, 0.85957, 0.9958, 0.74988, 0.89849, 0.706, 0.71898, 0.62263, 0.58308, 0.55242, 0.43712, 0.57656, 0.43712, 0.44492, 0.37001, 0.33303, 0.22573, 0.18604, 0.17204, 0.10706, 0.13513, 0, 0.02944, 0.03685], "triangles": [24, 22, 0, 22, 24, 23, 1, 22, 21, 1, 0, 22, 2, 1, 21, 2, 21, 20, 3, 2, 20, 3, 20, 19, 3, 19, 18, 4, 3, 18, 9, 18, 17, 9, 17, 16, 8, 18, 9, 4, 18, 8, 7, 6, 5, 15, 14, 13, 10, 9, 16, 5, 4, 7, 8, 7, 4, 11, 16, 15, 12, 11, 15, 10, 16, 11, 13, 12, 15], "vertices": [1, 56, 11.27, 1.77, 1, 1, 56, 8.32, 3.74, 1, 3, 52, 4.64, 10.38, 0, 55, 6.2, 5.87, 0.0185, 56, 3.73, 3.53, 0.98149, 3, 52, 4.34, 6.42, 2e-05, 55, 3.6, 2.86, 0.65161, 56, -0.23, 3.35, 0.34837, 3, 52, 0.99, 4.36, 0.18883, 55, -0.32, 3.2, 0.80967, 56, -2.68, 6.44, 0.0015, 2, 52, -3.01, 3.04, 0.85395, 55, -4.32, 4.53, 0.14605, 2, 52, -3.94, 1.36, 0.9411, 55, -6.06, 3.73, 0.0589, 2, 52, -2.75, -2.64, 0.95256, 53, -2.77, -6.93, 0.04744, 3, 52, 0.86, -4.48, 0.57222, 54, -3.13, -6, 0.0114, 53, 0.65, -4.76, 0.41638, 3, 52, 3.9, -6.12, 0.0673, 54, -1.12, -3.2, 0.34984, 53, 3.62, -2.99, 0.58285, 2, 54, 2.6, -3.88, 0.98778, 53, 6.76, -5.1, 0.01222, 1, 54, 7.48, -3.81, 1, 1, 54, 12.6, -1.7, 1, 1, 54, 13.49, 0.98, 1, 1, 54, 12.35, 3.6, 1, 1, 54, 8.86, 3.33, 1, 2, 54, 2.4, 2.88, 0.98385, 53, 9.27, 1.17, 0.01615, 2, 54, -2.57, 2.71, 0.06132, 53, 4.65, 3, 0.93868, 3, 52, 6.5, 0, 0.13609, 55, 1.51, -3.58, 0.33909, 53, -0.32, 2.37, 0.52483, 4, 52, 9.44, 1.75, 0.02393, 55, 4.91, -3.92, 0.87493, 56, -4.26, -2.26, 0.04995, 53, -0.32, 5.79, 0.05119, 3, 55, 8.04, -1.95, 0.29563, 56, -0.68, -3.18, 0.70383, 53, -2.6, 8.7, 0.00053, 1, 56, 5.54, -3.22, 1, 1, 56, 8.24, -3.73, 1, 1, 56, 10.93, -5.17, 1, 1, 56, 13.19, -2.22, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 34, "height": 26}}, "haiou4": {"haiou4": {"type": "mesh", "uvs": [0.05348, 0.71081, 0.014, 0.78962, 0.02835, 0.88876, 0.1091, 0.95993, 0.22574, 0.94214, 0.35493, 0.85317, 0.42312, 0.78962, 0.48772, 0.84046, 0.55949, 0.93197, 0.60256, 0.97518, 0.68689, 0.91163, 0.70304, 0.83791, 0.69407, 0.68539, 0.70125, 0.60405, 0.76405, 0.49982, 0.88069, 0.43627, 0.95067, 0.33459, 0.98476, 0.23545, 0.97758, 0.10326, 0.94528, 0.03463, 0.84839, 0.04734, 0.84121, 0.16682, 0.77302, 0.21003, 0.65639, 0.33459, 0.57385, 0.45152, 0.55949, 0.6498, 0.50566, 0.57863, 0.40338, 0.57863, 0.32443, 0.63455, 0.23471, 0.70064, 0.15217, 0.72352], "triangles": [20, 18, 17, 18, 20, 19, 17, 21, 20, 16, 21, 17, 15, 21, 16, 22, 21, 15, 14, 23, 22, 15, 14, 22, 24, 23, 14, 13, 24, 14, 13, 25, 24, 12, 25, 13, 6, 27, 26, 6, 26, 25, 28, 27, 6, 7, 6, 25, 5, 28, 6, 29, 28, 5, 30, 2, 1, 7, 25, 8, 11, 25, 12, 25, 11, 8, 10, 8, 11, 4, 30, 29, 4, 29, 5, 0, 30, 1, 3, 30, 4, 3, 2, 30, 9, 8, 10], "vertices": [1, 30, 9.45, -5.78, 1, 1, 30, 11.64, -5.01, 1, 1, 30, 12.6, -2.78, 1, 1, 30, 11.33, 0.2, 1, 1, 30, 7.84, 2.12, 1, 2, 29, 4.52, 4.95, 0.02423, 30, 3.01, 2.89, 0.97577, 2, 29, 3.26, 2.48, 0.58377, 30, 0.24, 2.97, 0.41623, 3, 26, 1.71, 3.67, 0.04909, 29, 0.75, 2.45, 0.93319, 30, -0.86, 5.23, 0.01772, 2, 26, -1.21, 2.18, 0.81337, 29, -2.46, 3.15, 0.18663, 2, 26, -2.71, 1.19, 0.9715, 29, -4.25, 3.32, 0.0285, 2, 26, -2.31, -2.04, 0.87941, 27, -4.13, -3.4, 0.12059, 2, 26, -0.86, -3.19, 0.68088, 27, -2.28, -3.52, 0.31912, 3, 26, 2.67, -4.22, 0.02401, 27, 1.2, -2.35, 0.97503, 28, -2.04, -5.7, 0.00097, 2, 27, 3.15, -2.12, 0.91486, 28, -1.11, -3.96, 0.08514, 2, 27, 6.09, -3.61, 0.13077, 28, 1.77, -2.38, 0.86923, 1, 28, 6.02, -2.37, 1, 1, 28, 9.11, -0.93, 1, 1, 28, 11.04, 0.88, 1, 1, 28, 11.94, 3.93, 1, 1, 28, 11.5, 5.86, 1, 1, 28, 8.32, 6.75, 1, 1, 28, 7.07, 4.15, 1, 1, 28, 4.53, 4.01, 1, 2, 27, 9.07, 0.89, 0.19876, 28, -0.24, 2.63, 0.80124, 3, 26, 9.37, -2.42, 0.00765, 27, 5.68, 2.95, 0.99215, 28, -3.86, 1, 0.0002, 3, 26, 5.11, -0.25, 0.61689, 27, 0.94, 2.3, 0.25663, 29, 0.9, -2.73, 0.12648, 2, 26, 7.36, 0.84, 0.27325, 29, 3.34, -3.31, 0.72675, 3, 26, 8.61, 4.09, 0.01346, 29, 6.36, -1.58, 0.92772, 30, -2.11, -1.56, 0.05882, 2, 29, 8.02, 0.91, 0.04498, 30, 0.86, -2, 0.95502, 1, 30, 4.26, -2.45, 1, 1, 30, 6.88, -3.61, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60], "width": 34, "height": 24}}, "haiou5": {"haiou5": {"type": "mesh", "uvs": [0, 0.537, 0.01582, 0.60054, 0.1104, 0.67236, 0.20279, 0.67788, 0.32377, 0.67788, 0.40075, 0.74142, 0.45574, 0.84086, 0.47994, 0.95964, 0.55693, 0.98727, 0.61192, 0.97069, 0.63172, 0.83534, 0.62952, 0.65578, 0.70211, 0.47899, 0.83848, 0.37126, 0.93527, 0.26077, 0.99246, 0.1558, 0.99466, 0.03978, 0.92647, 0, 0.86488, 0.02321, 0.82749, 0.09227, 0.7681, 0.16685, 0.64491, 0.26353, 0.59212, 0.38231, 0.53273, 0.50109, 0.52393, 0.6696, 0.44475, 0.56186, 0.31717, 0.52595, 0.19399, 0.50662, 0.1104, 0.45966, 0.02022, 0.41546, 0, 0.50386], "triangles": [15, 17, 16, 15, 18, 17, 15, 19, 18, 14, 19, 15, 20, 19, 14, 13, 20, 14, 21, 20, 13, 12, 21, 13, 22, 21, 12, 23, 22, 12, 30, 29, 28, 0, 30, 28, 1, 0, 28, 11, 23, 12, 24, 23, 11, 2, 28, 27, 1, 28, 2, 3, 27, 26, 2, 27, 3, 4, 26, 25, 3, 26, 4, 5, 4, 25, 5, 25, 24, 24, 11, 10, 6, 5, 24, 6, 24, 10, 8, 7, 6, 6, 10, 8, 9, 8, 10], "vertices": [1, 25, 8.64, 1.19, 1, 1, 25, 6.77, 3.35, 1, 2, 24, 12.56, 4.09, 0.00486, 25, 0.85, 4.13, 0.99514, 2, 24, 7.57, 3.83, 0.74626, 25, -3.82, 2.35, 0.25374, 2, 23, 9.25, 4.61, 0.11274, 24, 1.07, 3.18, 0.88726, 2, 23, 4.4, 3.53, 0.92557, 24, -3.34, 5.48, 0.07443, 2, 19, 3.67, 5.63, 0.25828, 23, -0.74, 4.37, 0.74172, 2, 19, -1.6, 5.7, 0.82542, 23, -5.32, 6.99, 0.17458, 2, 19, -3.83, 2, 0.97228, 23, -9.06, 4.83, 0.02772, 1, 19, -3.92, -1.05, 1, 2, 19, 1.42, -3.61, 0.87376, 20, -4.11, -4.78, 0.12624, 2, 20, 3.31, -2.63, 0.99956, 21, -7.28, -6.07, 0.00044, 3, 20, 11.67, -4.41, 0.08352, 21, 0.83, -3.36, 0.91624, 22, -11.76, 0.28, 0.00024, 2, 21, 9.33, -5.17, 0.47541, 22, -3.69, -2.97, 0.52459, 1, 22, 3.22, -4.43, 1, 1, 22, 8.69, -4.29, 1, 1, 22, 12.8, -1.47, 1, 1, 22, 12.03, 2.52, 1, 2, 21, 20.79, 4.56, 0.00432, 22, 9.28, 4.63, 0.99568, 2, 21, 17.28, 3.84, 0.0926, 22, 5.69, 4.53, 0.9074, 2, 21, 12.74, 3.78, 0.68538, 22, 1.21, 5.25, 0.31462, 2, 20, 19.8, 1, 0.0145, 21, 5.07, 5.44, 0.9855, 2, 20, 14.12, 2.41, 0.59171, 21, -0.53, 3.76, 0.40829, 3, 19, 16.69, -2.2, 0.00078, 20, 8.35, 4.16, 0.99899, 21, -6.39, 2.34, 0.00023, 4, 19, 9.82, 0.15, 0.53268, 20, 1.23, 2.71, 0.25006, 23, 1.98, -3.4, 0.21726, 21, -11.79, -2.52, 0, 2, 19, 15.41, 3.07, 0.01553, 23, 8.29, -3.55, 0.98447, 2, 23, 14.19, 0.32, 0.068, 24, 2.07, -3.29, 0.932, 2, 24, 8.77, -3.45, 0.72456, 25, -0.43, -4.2, 0.27544, 2, 24, 13.47, -5.01, 0.04423, 25, 4.52, -4.25, 0.95577, 1, 25, 9.74, -4.03, 1, 1, 25, 9.22, -0.11, 1], "hull": 31, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60], "width": 54, "height": 43}}, "naicha": {"naicha": {"type": "mesh", "uvs": [0.05465, 0.415, 0.04866, 0.91831, 0.08157, 0.96279, 0.32386, 1, 0.58111, 1, 0.67384, 0.93704, 0.79349, 0.52034, 0.88921, 0.49928, 0.92809, 0.37989, 0.86877, 0.34145, 0.73307, 0.32688, 0.73972, 0.28835, 0.70779, 0.28211, 0.63328, 0.29877, 0.71045, 0.20402, 0.76367, 0.18423, 0.88873, 0.26545, 0.94726, 0.26232, 1, 0.21443, 1, 0.17278, 0.95924, 0.12593, 0.73839, 0, 0.64127, 0, 0.59204, 0.05616, 0.54547, 0.07803, 0.55346, 0.11031, 0.53749, 0.14987, 0.45235, 0.25503, 0.22218, 0.23942, 0.18227, 0.20402, 0.12373, 0.20922, 0.11575, 0.2415, 0.03725, 0.23317, 0, 0.29148, 0.01064, 0.39664], "triangles": [25, 24, 23, 15, 21, 20, 23, 21, 15, 21, 23, 22, 14, 23, 15, 25, 23, 14, 26, 25, 14, 17, 20, 19, 20, 16, 15, 19, 18, 17, 17, 16, 20, 14, 13, 26, 13, 27, 26, 10, 12, 11, 33, 0, 34, 31, 33, 32, 31, 0, 33, 7, 9, 8, 6, 10, 9, 6, 9, 7, 31, 30, 29, 28, 31, 29, 0, 31, 28, 27, 0, 28, 6, 0, 27, 6, 1, 0, 13, 6, 27, 10, 13, 12, 6, 13, 10, 6, 3, 1, 3, 2, 1, 6, 5, 3, 4, 3, 5], "vertices": [1, 12, 29.31, 31.33, 1, 1, 12, -28.1, 23.97, 1, 1, 12, -32.76, 20.34, 1, 1, 12, -34.03, -1.85, 1, 2, 12, -30.87, -24.78, 0.9995, 13, -62.9, -53.08, 0.0005, 2, 12, -22.56, -32.06, 0.99435, 13, -52.11, -55.47, 0.00565, 2, 12, 26.38, -36.2, 0.41015, 13, -7.22, -35.54, 0.58985, 2, 12, 29.95, -44.4, 0.24642, 13, -0.14, -41.01, 0.75358, 2, 12, 44.03, -46, 0.16181, 13, 12.97, -35.63, 0.83819, 2, 12, 47.68, -40.1, 0.15354, 13, 13.34, -28.71, 0.84646, 2, 12, 47.68, -27.78, 0.12066, 13, 7.4, -17.9, 0.87934, 2, 12, 52.15, -27.77, 0.08355, 13, 11.31, -15.74, 0.91645, 2, 12, 52.47, -24.82, 0.08158, 13, 10.18, -13.01, 0.91842, 3, 12, 49.66, -18.44, 0.04964, 13, 4.64, -8.77, 0.94988, 14, 6.36, -25.12, 0.00048, 2, 13, 17.53, -7.84, 0.76779, 14, 5.73, -12.21, 0.23221, 2, 13, 22.21, -10.33, 0.24841, 14, 8.33, -7.59, 0.75159, 1, 14, 22.91, -8.7, 1, 1, 14, 27, -5.37, 1, 1, 14, 27.69, 1.87, 1, 1, 14, 24.92, 5.78, 1, 1, 14, 18.81, 8.06, 1, 2, 13, 37.86, 4.13, 0.99895, 14, -5.78, 8.39, 0.00105, 1, 13, 32.65, 11.15, 1, 1, 13, 24.82, 10.85, 1, 1, 13, 20.3, 12.72, 1, 1, 13, 17.75, 9.93, 1, 1, 13, 13.24, 8.37, 1, 2, 12, 52.42, -1.62, 0.28666, 13, -1.03, 7.31, 0.71334, 2, 12, 51.37, 19.14, 0.99383, 13, -11.95, 25.01, 0.00617, 2, 12, 54.92, 23.26, 0.9998, 13, -10.82, 30.32, 0.0002, 1, 12, 53.6, 28.4, 1, 1, 12, 49.83, 28.6, 1, 1, 12, 49.81, 35.73, 1, 1, 12, 42.72, 38.14, 1, 1, 12, 30.87, 35.54, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68], "width": 90, "height": 115}}, "sd": {"sd": {"x": 12.78, "y": -4.56, "rotation": 40.91, "width": 21, "height": 32}}, "ssdad": {"ssdad": {"x": 142.7, "y": -207.53, "rotation": -98.13, "width": 1011, "height": 537}}, "tx1": {"tx1": {"x": 4.16, "y": -0.64, "rotation": -177.27, "width": 13, "height": 21}}, "tx2": {"tx2": {"x": 3.22, "y": -0.57, "rotation": 6.84, "width": 13, "height": 19}}, "ty": {"ty": {"x": 3.39, "y": 3.98, "width": 145, "height": 149}}, "xc": {"xc": {"x": 28.04, "y": 10.06, "rotation": -93.37, "width": 181, "height": 140}}, "xx2": {"xx2": {"x": 1.99, "y": -1.82, "width": 24, "height": 22}}, "xxz": {"xxz": {"x": 1.3, "y": -0.78, "width": 25, "height": 22}}, "yeshu": {"yeshu": {"type": "mesh", "uvs": [0.03909, 0.92685, 0.09993, 0.95502, 0.15335, 0.96014, 0.21271, 0.96014, 0.25425, 0.99087, 0.39523, 1, 0.48723, 1, 0.52136, 0.95118, 0.51839, 0.89996, 0.4249, 0.85386, 0.4249, 0.71428, 0.45903, 0.56702, 0.52729, 0.57983, 0.57626, 0.57727, 0.57478, 0.62721, 0.6, 0.64001, 0.58665, 0.68099, 0.65491, 0.80264, 0.71278, 0.78343, 0.83298, 0.68611, 0.8671, 0.58751, 0.83891, 0.49147, 1, 0.53885, 1, 0.45177, 0.93833, 0.30195, 0.79588, 0.23409, 0.692, 0.22512, 0.63858, 0.24817, 0.61187, 0.11628, 0.43529, 0, 0.26464, 0, 0.16522, 0.04329, 0.1578, 0.08042, 0.20083, 0.12268, 0.22161, 0.15982, 0.26019, 0.17134, 0.26167, 0.19567, 0.17858, 0.19567, 0.09993, 0.23024, 0.06728, 0.27122, 0, 0.35061, 0, 0.47866, 0.07619, 0.49275, 0.13258, 0.45946, 0.14445, 0.44025, 0.20677, 0.43897, 0.27503, 0.42104, 0.31509, 0.44153, 0.25277, 0.48251, 0.20529, 0.58239, 0.17412, 0.67715, 0.1667, 0.76934, 0.1578, 0.8052, 0.03464, 0.85258, 0.0109, 0.89227], "triangles": [33, 31, 30, 32, 31, 33, 35, 34, 33, 30, 35, 33, 29, 36, 35, 30, 29, 35, 28, 36, 29, 27, 36, 28, 46, 36, 47, 46, 45, 37, 46, 37, 36, 38, 37, 45, 39, 38, 45, 44, 40, 39, 45, 44, 39, 27, 47, 36, 43, 40, 44, 41, 40, 43, 21, 25, 24, 21, 24, 23, 42, 41, 43, 21, 23, 22, 11, 47, 27, 27, 12, 11, 13, 12, 27, 27, 26, 25, 27, 25, 21, 13, 27, 21, 20, 15, 13, 20, 13, 21, 14, 13, 15, 19, 15, 20, 11, 48, 47, 11, 49, 48, 10, 49, 11, 50, 49, 10, 51, 50, 10, 18, 15, 19, 16, 15, 18, 17, 16, 18, 51, 10, 9, 52, 51, 9, 1, 0, 53, 54, 53, 0, 52, 1, 53, 2, 1, 52, 3, 2, 52, 9, 3, 52, 4, 3, 9, 5, 4, 9, 7, 5, 9, 7, 9, 8, 6, 5, 7], "vertices": [1, 9, -12.91, 37.03, 1, 1, 9, -17.43, 26.2, 1, 1, 9, -17.56, 17.06, 1, 1, 9, -16.59, 7.02, 1, 1, 9, -21.94, -0.59, 1, 1, 9, -21.42, -24.62, 1, 1, 9, -19.91, -40.19, 1, 1, 9, -9.78, -45.04, 1, 1, 9, 0.22, -43.56, 1, 2, 9, 7.72, -26.87, 0.99071, 10, -25.67, -35.38, 0.00929, 3, 9, 35.09, -24.22, 0.52622, 10, -0.56, -24.17, 0.47234, 60, -53.17, -14.77, 0.00144, 6, 9, 64.53, -27.2, 0.00102, 10, 28.29, -17.63, 0.35995, 11, 1.13, -17.23, 0.37117, 61, -4.96, -37, 0.00747, 60, -27.81, 0.47, 0.24729, 62, 14.07, -45.15, 0.01309, 5, 10, 30.72, -29.25, 0.04738, 11, 7.08, -27.51, 0.21058, 61, 4.39, -29.68, 0.10226, 60, -21.87, -9.81, 0.55168, 62, 11.58, -33.53, 0.08809, 5, 10, 34.58, -36.65, 0.00481, 11, 13.05, -33.33, 0.05145, 61, 9.31, -22.95, 0.24789, 60, -15.89, -15.63, 0.42476, 62, 7.68, -26.16, 0.27109, 4, 11, 5.61, -39.76, 0.00621, 61, 16.72, -29.43, 0.18468, 60, -23.33, -22.06, 0.20881, 62, 16.75, -22.33, 0.60031, 4, 11, 6.63, -44.63, 0.0014, 61, 21.4, -27.74, 0.12813, 60, -22.31, -26.93, 0.13217, 62, 17.28, -17.38, 0.7383, 3, 61, 26.17, -34.64, 0.03426, 60, -29.81, -30.69, 0.04574, 62, 25.57, -16.12, 0.92, 1, 62, 42.61, 4.33, 1, 1, 62, 35.11, 11.74, 1, 3, 61, 53.68, -3.05, 0.01821, 62, 9.21, 22.44, 0.98166, 59, 46.22, -44.81, 0.00013, 4, 61, 42.43, 13.82, 0.39565, 62, -10.87, 19.71, 0.55069, 58, 41.11, -45.34, 0.00122, 59, 36.2, -27.19, 0.05244, 4, 61, 24.8, 22.21, 0.45409, 62, -26.13, 7.54, 0.02251, 58, 34.79, -26.88, 0.05551, 59, 19.21, -17.58, 0.46789, 1, 59, 44.89, -4.24, 1, 1, 59, 32.49, 7.62, 1, 2, 58, 48.58, 11.71, 0.04562, 59, 3.92, 20.44, 0.95438, 4, 11, 88.18, -15.42, 0.00373, 58, 23.36, 23.06, 0.90337, 59, -22.49, 12.18, 0.09288, 66, -7.3, -45.28, 2e-05, 3, 11, 77.6, -1.17, 0.08115, 58, 5.61, 23.38, 0.85434, 66, 1.3, -29.76, 0.06451, 3, 11, 68.13, 2.49, 0.2087, 58, -3.07, 18.11, 0.43878, 66, 0.72, -19.62, 0.35251, 3, 11, 84.28, 23.34, 0.00977, 58, -9.72, 43.64, 0.00295, 66, 26.38, -25.72, 0.98727, 2, 66, 59.29, -7.2, 0.19835, 67, -5.14, -14.99, 0.80165, 1, 67, 23.81, -13.11, 1, 1, 67, 40.13, -3.5, 1, 2, 67, 40.91, 3.88, 0.99705, 64, 22.27, -45.98, 0.00295, 3, 66, 52.84, 38.97, 0.00596, 67, 33.07, 11.71, 0.91794, 64, 16.49, -36.52, 0.07611, 3, 66, 44.72, 38.61, 0.02514, 67, 29.07, 18.79, 0.77927, 64, 14.27, -28.7, 0.19558, 3, 66, 40.04, 33.48, 0.06024, 67, 22.38, 20.63, 0.5543, 64, 8.2, -25.34, 0.38546, 3, 66, 35.54, 35.15, 0.05357, 67, 21.82, 25.39, 0.27388, 64, 8.78, -20.57, 0.67255, 4, 66, 41.13, 48.12, 0.00146, 67, 35.92, 26.31, 0.04751, 64, 22.69, -23, 0.92782, 65, -27.14, -7.77, 0.0232, 3, 67, 48.82, 33.97, 0.00588, 64, 37.04, -18.59, 0.66523, 65, -14.02, -15.05, 0.32889, 3, 67, 53.83, 42.39, 0.00026, 64, 43.89, -11.6, 0.29141, 65, -4.22, -15.13, 0.70833, 1, 65, 15.14, -15.87, 1, 1, 65, 36.05, -1.77, 1, 4, 11, -31.87, 40.74, 0.00014, 63, -62.35, 18.76, 2e-05, 64, 49.91, 31.65, 0.00306, 65, 31.11, 10.52, 0.99678, 5, 10, 25, 41.68, 0, 11, -20.56, 38.07, 0.00487, 63, -51.05, 16.09, 0.00308, 64, 39.34, 26.84, 0.07035, 65, 20.32, 14.8, 0.9217, 5, 10, 29.27, 41.38, 0.00029, 11, -16.41, 39.13, 0.01203, 63, -46.89, 17.14, 0.00823, 64, 36.7, 23.46, 0.15791, 65, 16.05, 14.36, 0.82154, 5, 10, 33.83, 31.81, 0.00681, 11, -9.09, 31.46, 0.05972, 63, -39.57, 9.48, 0.04364, 64, 26.22, 25.04, 0.46183, 65, 9.92, 23, 0.42801, 5, 10, 41.78, 22.66, 0.0545, 11, 1.34, 25.26, 0.20858, 63, -29.15, 3.28, 0.13849, 64, 14.18, 23.55, 0.49869, 65, 0.5, 30.65, 0.09973, 5, 10, 40.88, 14.8, 0.23939, 11, 2.94, 17.51, 0.4095, 63, -27.55, -4.48, 0.1391, 64, 8.16, 28.7, 0.19664, 65, 0.04, 38.55, 0.01539, 5, 10, 29.19, 21.18, 0.8705, 11, -10.17, 19.91, 0.07198, 63, -40.65, -2.08, 0.02573, 64, 19.99, 34.83, 0.03142, 65, 12.65, 34.28, 0.00037, 4, 9, 57.36, 15.44, 0.07092, 10, 7.93, 20.52, 0.92901, 63, -60.63, -9.36, 3e-05, 64, 31.32, 52.82, 4e-05, 2, 9, 38.27, 18.92, 0.72497, 10, -11.28, 17.74, 0.27503, 2, 9, 20.07, 18.42, 0.99915, 10, -28.37, 11.48, 0.00085, 1, 9, 12.89, 19.25, 1, 1, 9, 1.58, 39.19, 1, 1, 9, -6.59, 42.46, 1], "hull": 55, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 0, 108], "width": 170, "height": 197}}, "yh": {"yh": {"x": 15.25, "y": -0.8, "rotation": -37.28, "width": 29, "height": 26}}, "yun1": {"yun1": {"x": -1.48, "y": 3.82, "width": 113, "height": 79}}, "yun2": {"yun2": {"x": -5.41, "y": -0.15, "width": 113, "height": 79}}, "yun3": {"yun3": {"x": 18.31, "y": -1.68, "width": 113, "height": 79}}, "yun4": {"yun4": {"x": 6.4, "y": -1.59, "width": 122, "height": 82}}, "zh": {"zh": {"x": 12.96, "y": 0.23, "rotation": -135.56, "width": 26, "height": 27}}}}], "animations": {"animation": {"slots": {"bb1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "bb2": {"color": [{"color": "ffffff07"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff07"}]}, "bb3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00"}]}, "bb4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00"}]}, "bb5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff00"}]}, "sd": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00"}]}, "tx1": {"color": [{"color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}, "tx2": {"color": [{"color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{}, {"time": 0.6667, "y": -6.39}, {"time": 1.3333}]}, "bone2": {"translate": [{}, {"time": 0.3333, "x": -3.73, "y": 0.79}, {"time": 0.8333, "x": -24.49, "y": -11.73}, {"time": 1.3333}]}, "bone3": {"translate": [{"time": 0.3333}, {"time": 0.8, "x": 34.28, "y": -15.62}, {"time": 1.3333}]}, "bone4": {"translate": [{"time": 0.4}, {"time": 0.6, "x": 13.11, "y": 0.6}, {"time": 1.3333}]}, "bone6": {"translate": [{}, {"time": 0.6667, "y": -4.72}, {"time": 1.3333}]}, "bone7": {"rotate": [{}, {"time": 0.2, "angle": 49.38}, {"time": 0.4, "angle": 123.45}, {"time": 0.6, "angle": 172.83}, {"time": 0.8333, "angle": 222.21}, {"time": 1.0333, "angle": 271.59}, {"time": 1.2333, "angle": 320.97}, {"time": 1.3333, "angle": 360}]}, "bone8": {"rotate": [{}, {"time": 0.1667, "angle": 45}, {"time": 0.3333, "angle": 90}, {"time": 0.5, "angle": 135}, {"time": 0.6667, "angle": 180}, {"time": 0.8333, "angle": 234}, {"time": 1, "angle": 276}, {"time": 1.1667, "angle": 318}, {"time": 1.3333, "angle": 360}]}, "bone9": {"rotate": [{"time": 0.4333}, {"time": 0.8333, "angle": 0.94}, {"time": 1.3333}]}, "bone10": {"rotate": [{}, {"time": 0.4667, "angle": -1.32}, {"time": 1.3333}]}, "bone11": {"rotate": [{}, {"time": 0.5, "angle": -0.68}, {"time": 1.3333}]}, "bone12": {"translate": [{}, {"time": 0.1667, "x": -5.18}, {"time": 0.4333, "x": 6.08}, {"time": 0.8, "x": -5.18}, {"time": 1.0667, "x": 6.08}, {"time": 1.3333}]}, "bone14": {"rotate": [{}, {"time": 0.4, "angle": -12.72}, {"time": 0.6667, "angle": -10.17}, {"time": 1, "angle": -0.45}, {"time": 1.3333}]}, "bone15": {"translate": [{}, {"time": 0.2, "x": -13.89, "y": 1.69}, {"time": 0.5333, "x": 9.01, "y": 1}, {"time": 0.9667, "x": -12.43, "y": 0.44}, {"time": 1.3, "x": 9.01, "y": 1}, {"time": 1.3333}]}, "bone16": {"rotate": [{}, {"time": 0.2333, "angle": -27.32}, {"time": 0.4667, "angle": 49.61}, {"time": 0.8, "angle": -21.27}, {"time": 1.0333, "angle": 60.63}, {"time": 1.3333}]}, "bone17": {"rotate": [{}, {"time": 0.2333, "angle": 17.43}, {"time": 0.4667, "angle": -42.83}, {"time": 0.8, "angle": 8.96}, {"time": 1.0333, "angle": -61.93}, {"time": 1.3333}]}, "bone18": {"scale": [{"time": 0.2}, {"time": 0.5333, "x": 1.1, "y": 1.1}, {"time": 1.3333}]}, "bone19": {"translate": [{}, {"time": 0.6, "y": 4.21}, {"time": 1.3333}]}, "bone20": {"rotate": [{}, {"time": 0.4667, "angle": -29.93}, {"time": 1.3333}]}, "bone21": {"rotate": [{}, {"time": 0.5, "angle": -23.94}, {"time": 1.3333}]}, "bone22": {"rotate": [{}, {"time": 0.5667, "angle": -18.36}, {"time": 1.3333}]}, "bone23": {"rotate": [{}, {"time": 0.4667, "angle": 31.31}, {"time": 1.3333}]}, "bone24": {"rotate": [{}, {"time": 0.5, "angle": 14.09}, {"time": 1.3333}]}, "bone25": {"rotate": [{}, {"time": 0.5667, "angle": 31.97}, {"time": 1.3333}]}, "bone26": {"translate": [{}, {"time": 0.6, "x": 0.99, "y": -1.79}, {"time": 1.3333}]}, "bone27": {"rotate": [{}, {"time": 0.3333, "angle": 10.04}, {"time": 0.8, "angle": -12.79}, {"time": 1.3333}]}, "bone28": {"rotate": [{}, {"time": 0.3333, "angle": 10.62}, {"time": 0.9333, "angle": -12.79}, {"time": 1.3333}]}, "bone29": {"rotate": [{}, {"time": 0.3333, "angle": -14.65}, {"time": 0.6333, "angle": 12.72}, {"time": 1.3333}]}, "bone30": {"rotate": [{}, {"time": 0.3333, "angle": -15.1}, {"time": 0.7333, "angle": 12.72}, {"time": 1.3333}]}, "bone31": {"translate": [{}, {"time": 0.3667, "x": 2.78, "y": 1.24}, {"time": 0.8, "x": -15.78, "y": 6.27}, {"time": 1.3333}]}, "bone32": {"rotate": [{}, {"time": 0.5667, "angle": -5.89}, {"time": 0.8333, "angle": 20}, {"time": 1.3333}], "scale": [{"x": 0.5, "y": 0.5}, {"time": 0.4667, "x": 0.6, "y": 0.6}, {"time": 0.6333, "x": 0.8, "y": 0.8}, {"time": 1.3333, "x": 0.5, "y": 0.5}]}, "bone33": {"rotate": [{"time": 0.4667}, {"time": 0.8333, "angle": -10.15}, {"time": 1.3333}], "scale": [{"x": 0.4, "y": 0.4}, {"time": 0.1667, "x": 0.3, "y": 0.3}, {"time": 0.4667}, {"time": 1.3333, "x": 0.4, "y": 0.4}]}, "bone35": {"scale": [{}, {"time": 0.5, "x": 0.9, "y": 0.9}, {"time": 1.3333}]}, "bone36": {"rotate": [{}, {"time": 0.7, "angle": 2.56}, {"time": 1.1, "angle": -0.5}, {"time": 1.3333}], "scale": [{}, {"time": 0.4, "x": 0.9, "y": 0.9}, {"time": 1.3333}]}, "bone37": {"rotate": [{}, {"time": 0.6333, "angle": -3.13}, {"time": 1.1333, "angle": 2.75}, {"time": 1.3333}], "scale": [{}, {"time": 0.5333, "x": 1.1, "y": 1.1}, {"time": 1.3333}]}, "bone38": {"translate": [{}, {"time": 0.6667, "y": -6.27}, {"time": 1.3333}]}, "bone40": {"translate": [{}, {"time": 0.2, "x": 12.01}, {"time": 0.9333}, {"time": 1.1333, "x": 12.01}, {"time": 1.3333}]}, "bone41": {"translate": [{}, {"time": 0.2333, "x": -16.13}, {"time": 0.9}, {"time": 1.1333, "x": -16.13}, {"time": 1.3333}]}, "bone42": {"translate": [{}, {"time": 0.6, "y": 4.28}, {"time": 1.3333}]}, "bone43": {"rotate": [{}, {"time": 0.5333, "angle": -22.3}, {"time": 1.3333}]}, "bone44": {"rotate": [{}, {"time": 0.6, "angle": -31.08}, {"time": 1.3333}]}, "bone45": {"rotate": [{}, {"time": 0.5333, "angle": 26.3}, {"time": 1.3333}]}, "bone46": {"rotate": [{}, {"time": 0.6, "angle": 37.79}, {"time": 1.3333}]}, "bone47": {"translate": [{}, {"time": 0.2333, "y": 3.59}, {"time": 1.3333}]}, "bone48": {"rotate": [{}, {"time": 0.2333, "angle": -23.27}, {"time": 1.3333}]}, "bone49": {"rotate": [{}, {"time": 0.2333, "angle": -31.86}, {"time": 1.3333}]}, "bone50": {"rotate": [{}, {"time": 0.2333, "angle": 24.77}, {"time": 1.3333}]}, "bone51": {"rotate": [{}, {"time": 0.2333, "angle": 32.23}, {"time": 1.3333}]}, "bone52": {"translate": [{}, {"time": 0.9667, "y": 4.87}, {"time": 1.3333}]}, "bone53": {"rotate": [{}, {"time": 0.9333, "angle": -15.1}, {"time": 1.3333}]}, "bone54": {"rotate": [{}, {"time": 0.9333, "angle": -30.71}, {"time": 1, "angle": -15.1}, {"time": 1.3333}]}, "bone55": {"rotate": [{}, {"time": 0.9333, "angle": 19.36}, {"time": 1.3333}]}, "bone56": {"rotate": [{}, {"time": 0.9333, "angle": 27.94}, {"time": 1, "angle": 19.36}, {"time": 1.3333}]}, "bone5": {"translate": [{"time": 0.4}, {"time": 0.6333, "x": -11.64, "y": 8.06}]}, "bone57": {"scale": [{"x": 0.5, "y": 0.5}, {"time": 0.3, "x": 0.6, "y": 0.6}, {"time": 0.8}, {"time": 1.3333, "x": 0.5, "y": 0.5}]}, "root": {"translate": [{}, {"time": 0.4667, "y": 1}, {"time": 1.3333}]}, "bone58": {"rotate": [{}, {"time": 0.4667, "angle": 6.68}, {"time": 1.3333}]}, "bone61": {"rotate": [{}, {"time": 0.4, "angle": 3.76}, {"time": 1.3333}]}, "bone62": {"rotate": [{}, {"time": 0.4333, "angle": 3.76}, {"time": 1.3333}]}, "bone59": {"rotate": [{}, {"time": 0.5333, "angle": 7.91}, {"time": 1.3333}]}, "bone66": {"rotate": [{}, {"time": 0.4667, "angle": 7.16}, {"time": 1.3333}]}, "bone67": {"rotate": [{}, {"time": 0.5, "angle": -2.44}, {"time": 1.3333}]}, "bone64": {"rotate": [{}, {"time": 0.5, "angle": -8.31}, {"time": 1.3333}]}}}}}