
import { _decorator, Button, Vec3,Label,tween } from 'cc';
import {dialogBase} from "../../framework/dialogBase";
import {uiManager} from "../../framework/uiManager";
import {Constants} from "../../game/Constants";
import {Public} from "../../game/Public";
import {WECHAT} from 'cc/env';
import {configuration} from "../../framework/configuration";
import {audioManager} from "../../framework/audioManager";
import {AdCtl} from "../../channel/AdCtl";
import {gameStorage} from "../../framework/gameStorage";
import {DataCtl} from "../../game/DataCtl";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Success
 * DateTime = Mon Feb 28 2022 17:54:00 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Success.ts
 * FileBasenameNoExtension = Success
 * URL = db://assets/script/ui/common/Success.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Success')
export class Success extends dialogBase {

    @property(Button)
    go:Button = null!
    @property(Label)
    goLabel:Label = null!

    cnt=0

    show(){


        //扣去体力
        DataCtl.instance.AddStrength(-1)

        // super.show(null)
        this.node.position = new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0)
        audioManager.instance.playSound(Constants.Sounds.win)

        //标记这一关通过
        gameStorage.setInt(Constants.LevelHead + Public.CurLevelNum, Constants.LevelState.success)
        console.log("--------success---------", Constants.LevelHead + Public.CurLevelNum)
        let stat = gameStorage.getInt(Constants.LevelHead + Public.CurLevelNum)
        console.log("--------stat---------", stat)
        //解锁下一关
        if(Public.CurLevelNum+1 <= Constants.TotalLevelNum){
            //标记下一关解锁
            //如果该关卡已经通关，那么不需要处理
            let stat = gameStorage.getInt(Constants.LevelHead + (Public.CurLevelNum+1), Constants.LevelState.locked)
            console.log("stat------", stat, stat==Constants.LevelState.locked, Constants.LevelHead + (Public.CurLevelNum+1))
            if(stat==Constants.LevelState.locked){
                gameStorage.setInt(Constants.LevelHead + (Public.CurLevelNum+1), Constants.LevelState.open)
            }
        }
        AdCtl.instance.HideAllCustomAD()
        AdCtl.instance.ShowBannerAd()

        if(WECHAT){
            this.go.enabled = false
            this.cnt=0
            this.unlock()
        }
    }

    unlock(){
        if(this.cnt>=3){
            this.goLabel.string = `继续`
            this.go.enabled = true
            return
        }else{
            this.goLabel.string = `继续${3-this.cnt}`
            this.scheduleOnce(()=>{
                this.cnt++
                this.unlock()
            },1)
        }
    }

    hide(){
        AdCtl.instance.HideBannerAD()
    }

    OnClickContinue(){

        //提示体力不足
        if(gameStorage.getInt(Constants.CacheDataKey.strength,0)<=0){
            uiManager.instance.showDialog(Constants.Dialogs.strengthPrompt)
        }else{
            this.close()
            uiManager.instance.destroyDialog(Constants.Dialogs.ops)
            uiManager.instance.destroyDialog(Public.GetRightLevelPath())
            if(Public.CurLevelNum+1<=Constants.TotalLevelNum){
                Public.CurLevelNum +=1
                // uiManager.instance.showDialog(Public.GetRightLevelPath())
                uiManager.instance.showDialog(Constants.Dialogs.levelLoading)
            }else{
                uiManager.instance.showDialog(Constants.Dialogs.mainUI)
            }

        }
        //检查体力是否足够
        // if(gameStorage.getInt(Constants.CacheDataKey.strength,0)<=0){
        //
        // }


    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
