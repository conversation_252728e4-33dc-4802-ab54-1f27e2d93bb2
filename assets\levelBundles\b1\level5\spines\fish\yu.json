{"skeleton": {"hash": "dssvyStgKJc", "spine": "3.8-from-4.0.09", "x": -45.6, "y": -0.27, "width": 92.94, "height": 83.48, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/如鱼得水/鱼"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 5.71, "y": 30.51}, {"name": "bone2", "parent": "bone", "length": 25.22, "rotation": 179.36, "x": -4.51, "y": 0.14}, {"name": "bone3", "parent": "bone", "length": 6.69, "rotation": -8.47, "x": 3.66, "y": 1.13}, {"name": "bone4", "parent": "bone3", "length": 12.45, "rotation": 3.28, "x": 7.14, "y": 5.62}, {"name": "bone5", "parent": "bone4", "length": 8.69, "rotation": 22.16, "x": 14.69, "y": 0.2}, {"name": "bone6", "parent": "bone3", "length": 11.23, "rotation": -64, "x": 5.07, "y": -4.37}, {"name": "bone7", "parent": "bone6", "length": 12.06, "rotation": 25.11, "x": 14.25, "y": 0.23}, {"name": "bone8", "parent": "bone2", "length": 5.07, "rotation": -0.95, "x": 13.9, "y": -8.72}, {"name": "bone9", "parent": "bone", "length": 14.28, "rotation": -178.98, "x": 15.54, "y": 32.63}, {"name": "bone10", "parent": "bone", "length": 8.6, "rotation": 168.44, "x": -42.87, "y": 26.86}], "slots": [{"name": "body", "bone": "root", "attachment": "body"}, {"name": "y1", "bone": "bone8", "attachment": "y1"}, {"name": "y2", "bone": "root"}, {"name": "3", "bone": "bone10", "attachment": "4"}, {"name": "4", "bone": "root"}, {"name": "2", "bone": "root"}, {"name": "1", "bone": "bone9", "attachment": "2"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"x": 0.78, "y": -4.09, "rotation": 178.98, "width": 24, "height": 32}, "2": {"x": 0.78, "y": -4.09, "rotation": 178.98, "width": 24, "height": 32}}, "3": {"3": {"x": 3.36, "y": -1.71, "rotation": -168.44, "width": 13, "height": 25}, "4": {"x": 3.36, "y": -1.71, "rotation": -168.44, "width": 11, "height": 22}}, "body": {"body": {"type": "mesh", "uvs": [0.07461, 0.3792, 0.01701, 0.395, 0, 0.41713, 0, 0.50566, 0.03294, 0.55466, 0.09545, 0.5847, 0.0452, 0.63528, 0.05133, 0.70957, 0.1077, 0.73171, 0.17143, 0.80442, 0.23148, 0.82971, 0.23883, 0.91033, 0.28418, 0.96724, 0.32462, 0.95617, 0.33442, 0.90875, 0.38345, 0.95143, 0.42021, 0.93246, 0.41408, 0.87714, 0.48884, 0.87714, 0.50968, 0.84394, 0.49252, 0.76806, 0.47291, 0.74593, 0.51458, 0.69219, 0.52438, 0.77281, 0.5685, 0.86765, 0.66042, 0.96566, 0.7462, 0.99253, 0.84424, 0.98621, 0.84915, 0.93246, 0.78787, 0.87239, 0.76949, 0.83445, 0.77439, 0.77755, 0.73517, 0.71748, 0.78052, 0.66531, 0.77807, 0.62421, 0.86385, 0.6005, 0.95944, 0.48195, 0.98814, 0.35684, 0.97997, 0.33771, 0.86124, 0.37031, 0.76443, 0.30965, 0.65154, 0.27962, 0.57117, 0.32244, 0.54292, 0.39127, 0.53909, 0.31913, 0.5685, 0.22902, 0.52438, 0.13734, 0.47291, 0.13734, 0.45453, 0.04408, 0.3859, 0, 0.34791, 0.04724, 0.30624, 0, 0.24373, 0, 0.18859, 0.10256, 0.1604, 0.2148, 0.15672, 0.2638, 0.10893, 0.32703, 0.54344, 0.45542, 0.54344, 0.52046, 0.53808, 0.57858, 0.54022, 0.62286, 0.51555, 0.63532, 0.53057, 0.65746, 0.51876, 0.48863, 0.5134, 0.55506, 0.6216, 0.4225, 0.71751, 0.41452, 0.7995, 0.45642, 0.85055, 0.47438, 0.83353, 0.51828, 0.70359, 0.53823, 0.67574, 0.58014, 0.67883, 0.64998, 0.65563, 0.7278, 0.66955, 0.8116, 0.72834, 0.90339], "triangles": [25, 75, 26, 27, 26, 28, 26, 75, 29, 28, 26, 29, 25, 74, 75, 25, 24, 74, 75, 30, 29, 75, 74, 30, 74, 24, 73, 30, 74, 31, 74, 32, 31, 74, 73, 32, 24, 23, 73, 23, 62, 73, 23, 22, 62, 62, 60, 73, 73, 72, 32, 73, 60, 72, 32, 72, 33, 22, 61, 62, 72, 34, 33, 62, 61, 60, 60, 71, 72, 72, 71, 34, 61, 59, 60, 60, 59, 71, 59, 58, 71, 34, 69, 35, 69, 70, 67, 69, 68, 35, 35, 68, 36, 69, 67, 68, 68, 39, 36, 37, 39, 38, 37, 36, 39, 68, 67, 39, 67, 40, 39, 71, 70, 34, 69, 34, 70, 70, 71, 65, 71, 58, 65, 70, 66, 67, 70, 65, 66, 58, 57, 65, 66, 40, 67, 57, 43, 65, 43, 42, 65, 65, 41, 66, 65, 42, 41, 66, 41, 40, 13, 12, 14, 14, 12, 11, 16, 15, 17, 15, 14, 17, 11, 10, 14, 14, 10, 17, 19, 18, 20, 18, 17, 20, 17, 21, 20, 17, 10, 21, 21, 10, 64, 8, 64, 10, 8, 10, 9, 5, 55, 64, 55, 5, 56, 22, 21, 61, 63, 64, 55, 8, 6, 5, 64, 8, 5, 63, 55, 54, 47, 54, 50, 8, 7, 6, 61, 21, 64, 61, 64, 59, 54, 53, 50, 50, 48, 47, 50, 49, 48, 4, 0, 5, 5, 0, 56, 59, 64, 58, 64, 63, 58, 44, 63, 54, 4, 3, 0, 0, 3, 1, 63, 57, 58, 3, 2, 1, 63, 43, 57, 63, 44, 43, 45, 44, 47, 47, 44, 54, 45, 47, 46, 53, 52, 50, 52, 51, 50], "vertices": [2, 2, 35.29, -11, 0.99538, 3, -44.4, 3.91, 0.00462, 2, 2, 40.41, -9.86, 0.9995, 3, -49.31, 2.08, 0.0005, 2, 2, 41.9, -8.31, 0.99982, 3, -50.58, 0.35, 0.00018, 1, 2, 41.84, -2.2, 1, 1, 2, 38.87, 1.14, 1, 1, 2, 33.28, 3.15, 1, 1, 2, 37.71, 6.69, 1, 1, 2, 37.11, 11.81, 1, 2, 2, 32.08, 13.28, 0.99992, 6, -5.05, -45.35, 8e-05, 2, 2, 26.35, 18.24, 0.99636, 6, 1.44, -41.45, 0.00364, 2, 2, 20.99, 19.92, 0.9852, 6, 4.72, -36.88, 0.0148, 2, 2, 20.27, 25.48, 0.97659, 6, 10.22, -37.93, 0.02341, 2, 2, 16.19, 29.36, 0.97222, 6, 15.18, -35.26, 0.02778, 2, 2, 12.6, 28.56, 0.96921, 6, 15.53, -31.6, 0.03079, 2, 2, 11.76, 25.27, 0.96016, 6, 12.68, -29.78, 0.03984, 2, 2, 7.37, 28.17, 0.94335, 6, 16.8, -26.51, 0.05665, 2, 2, 4.11, 26.83, 0.93994, 6, 16.54, -22.99, 0.06006, 2, 2, 4.7, 23.01, 0.92606, 6, 12.73, -22.37, 0.07394, 2, 2, -1.95, 22.94, 0.89119, 6, 14.73, -16.02, 0.10881, 2, 2, -3.78, 20.63, 0.88701, 6, 13.11, -13.56, 0.11299, 2, 2, -2.2, 15.41, 0.86178, 6, 7.66, -13.44, 0.13822, 2, 2, -0.44, 13.9, 0.82582, 6, 5.67, -14.65, 0.17418, 4, 2, -4.1, 10.15, 0.42005, 3, -2.49, -11.68, 0.01498, 6, 3.25, -9.99, 0.55795, 7, -14.3, -4.59, 0.00702, 3, 2, -5.04, 15.71, 0.09098, 6, 8.82, -10.84, 0.77274, 7, -9.61, -7.71, 0.13627, 3, 2, -9.04, 22.21, 0.00787, 6, 16.25, -9.06, 0.34232, 7, -2.14, -9.26, 0.64981, 1, 7, 8.38, -7.82, 1, 1, 7, 14.91, -3.46, 1, 1, 7, 20.5, 3.26, 1, 1, 7, 18.07, 6.09, 1, 3, 4, 14.75, -26.9, 9e-05, 6, 22.44, 9.46, 0.00481, 7, 11.33, 4.89, 0.99509, 4, 3, 21.4, -18.05, 0.00108, 4, 12.88, -24.44, 0.00309, 6, 19.45, 8.68, 0.05045, 7, 8.29, 5.46, 0.94538, 4, 3, 21.25, -14.1, 0.00723, 4, 12.96, -20.49, 0.0156, 6, 15.83, 10.28, 0.18064, 7, 5.7, 8.44, 0.79654, 5, 3, 17.19, -10.51, 0.05661, 4, 9.11, -16.68, 0.106, 5, -11.54, -13.53, 0.00809, 6, 10.83, 8.2, 0.52741, 7, 0.29, 8.68, 0.30188, 5, 3, 20.65, -6.36, 0.11935, 4, 12.8, -12.73, 0.32366, 5, -6.63, -11.27, 0.072, 6, 8.61, 13.14, 0.43733, 7, 0.37, 14.08, 0.04767, 5, 3, 20.01, -3.59, 0.10971, 4, 12.33, -9.93, 0.4195, 5, -6.01, -8.49, 0.16984, 6, 5.84, 13.78, 0.2857, 7, -1.86, 15.84, 0.01525, 4, 3, 27.33, -0.84, 0.01316, 4, 19.78, -7.61, 0.12639, 5, 1.77, -9.15, 0.82724, 6, 6.58, 21.55, 0.03321, 1, 5, 12.29, -3.81, 1, 1, 5, 17.25, 3.7, 1, 1, 5, 16.95, 5.17, 1, 2, 4, 18.11, 8.19, 0.08241, 5, 6.18, 6.11, 0.91759, 2, 4, 9.16, 11.58, 0.84387, 5, -0.84, 12.62, 0.15613, 4, 2, -15.97, -18.45, 0.01173, 3, 5.37, 18.27, 0.02133, 4, -1.04, 12.73, 0.96578, 5, -9.84, 17.54, 0.00117, 4, 2, -8.85, -15.41, 0.19234, 3, -1.27, 14.3, 0.18058, 4, -7.9, 9.14, 0.62657, 5, -17.55, 16.8, 0.00052, 3, 2, -6.39, -10.64, 0.33541, 3, -3.05, 9.23, 0.30673, 4, -9.97, 4.19, 0.35786, 3, 2, -6, -15.61, 0.52718, 3, -4.12, 14.1, 0.27966, 4, -10.76, 9.11, 0.19317, 3, 2, -8.54, -21.86, 0.6478, 3, -2.45, 20.64, 0.2451, 4, -8.71, 15.54, 0.1071, 3, 2, -4.55, -28.14, 0.69987, 3, -7.27, 26.32, 0.22168, 4, -13.2, 21.49, 0.07844, 3, 2, 0.03, -28.09, 0.74989, 3, -11.8, 25.64, 0.19434, 4, -17.76, 21.07, 0.05577, 3, 2, 1.74, -34.5, 0.80588, 3, -14.36, 31.77, 0.16431, 4, -19.97, 27.33, 0.02981, 3, 2, 7.88, -37.48, 0.82665, 3, -20.85, 33.87, 0.15163, 4, -26.33, 29.81, 0.02172, 3, 2, 11.23, -34.18, 0.84869, 3, -23.72, 30.15, 0.13608, 4, -29.4, 26.25, 0.01522, 3, 2, 14.97, -37.4, 0.8762, 3, -27.86, 32.83, 0.11751, 4, -33.39, 29.16, 0.00629, 3, 2, 20.53, -37.34, 0.88763, 3, -33.37, 32.01, 0.10918, 4, -38.93, 28.66, 0.00318, 3, 2, 25.36, -30.2, 0.90814, 3, -37.18, 24.29, 0.09099, 4, -43.18, 21.17, 0.00087, 2, 2, 27.79, -22.43, 0.93849, 3, -38.52, 16.26, 0.06151, 2, 2, 28.08, -19.05, 0.95647, 3, -38.34, 12.87, 0.04353, 2, 2, 32.28, -14.64, 0.98415, 3, -41.91, 7.92, 0.01585, 6, 2, -6.49, -6.21, 0.34786, 3, -2.35, 4.86, 0.22033, 4, -9.52, -0.22, 0.26164, 5, -22.59, 8.74, 0.00546, 6, -11.55, -2.62, 0.16229, 7, -24.58, 8.37, 0.00242, 6, 2, -6.54, -1.72, 0.35253, 3, -1.69, 0.42, 0.16618, 4, -9.12, -4.69, 0.19451, 5, -23.9, 4.45, 0.00427, 6, -7.27, -3.98, 0.2684, 7, -21.27, 5.33, 0.01411, 6, 2, -6.11, 2.29, 0.36696, 3, -1.58, -3.62, 0.11625, 4, -9.23, -8.73, 0.13132, 5, -25.52, 0.75, 0.00299, 6, -3.59, -5.64, 0.36497, 7, -18.65, 2.26, 0.01751, 6, 2, -6.33, 5.34, 0.36604, 3, -0.94, -6.61, 0.07999, 4, -8.76, -11.75, 0.0869, 5, -26.23, -2.22, 0.00226, 6, -0.62, -6.38, 0.43651, 7, -16.27, 0.33, 0.0283, 5, 2, -4.14, 6.23, 0.43935, 3, -2.98, -7.78, 0.05335, 4, -10.87, -12.81, 0.02785, 6, -0.46, -8.73, 0.47349, 7, -17.12, -1.87, 0.00596, 6, 2, -5.5, 7.74, 0.36192, 3, -1.43, -9.1, 0.05235, 4, -9.4, -14.21, 0.05154, 5, -27.75, -4.26, 0.00134, 6, 1.4, -7.91, 0.50295, 7, -15.1, -1.92, 0.02989, 5, 2, -4.32, -3.9, 0.48771, 3, -4.19, 2.27, 0.1525, 4, -11.5, -2.7, 0.09961, 6, -10.03, -5.41, 0.25694, 7, -24.38, 5.2, 0.00323, 5, 2, -3.89, 0.69, 0.46968, 3, -3.99, -2.34, 0.10464, 4, -11.56, -7.31, 0.06461, 6, -5.8, -7.24, 0.35658, 7, -21.33, 1.74, 0.00449, 6, 2, -13.42, -8.56, 0.19924, 3, 4.19, 8.13, 0.0966, 4, -2.8, 2.67, 0.50126, 5, -15.27, 8.89, 0.04482, 6, -11.62, 4.69, 0.15018, 7, -21.54, 15.02, 0.0079, 6, 2, -21.95, -9.21, 0.06942, 3, 12.55, 9.93, 0.05652, 4, 5.65, 3.99, 0.63607, 5, -6.94, 6.92, 0.12021, 6, -9.58, 13, 0.11186, 7, -16.16, 21.68, 0.00593, 6, 2, -29.28, -6.4, 0.03018, 3, 20.2, 8.15, 0.0284, 4, 13.18, 1.78, 0.33594, 5, -0.81, 2.03, 0.54461, 6, -4.62, 19.09, 0.05829, 7, -9.09, 25.09, 0.00258, 6, 2, -33.83, -5.21, 0.01923, 3, 24.87, 7.59, 0.01906, 4, 17.82, 0.95, 0.22326, 5, 3.18, -0.48, 0.69725, 6, -2.07, 23.05, 0.03957, 7, -5.1, 27.59, 0.00164, 6, 2, -32.35, -2.16, 0.01342, 3, 23.82, 4.37, 0.04095, 4, 16.58, -2.2, 0.26587, 5, 0.84, -2.94, 0.57532, 6, 0.36, 20.69, 0.09956, 7, -3.9, 24.42, 0.00489, 6, 2, -20.8, -0.66, 0.09717, 3, 12.58, 1.31, 0.10448, 4, 5.19, -4.62, 0.44268, 5, -10.62, -0.88, 0.11128, 6, -1.81, 9.25, 0.23212, 7, -10.72, 14.98, 0.01227, 6, 2, -18.36, 2.26, 0.13665, 3, 10.56, -1.92, 0.09292, 4, 2.98, -7.72, 0.32508, 5, -13.83, -2.92, 0.07524, 6, 0.2, 6.01, 0.31399, 7, -10.27, 11.2, 0.05612, 6, 2, -18.69, 7.08, 0.11306, 3, 11.54, -6.64, 0.0714, 4, 3.69, -12.5, 0.19087, 5, -14.98, -7.61, 0.03561, 6, 4.88, 4.82, 0.43296, 7, -6.54, 8.14, 0.15609, 6, 2, -16.68, 12.47, 0.06103, 3, 10.29, -12.26, 0.04719, 4, 2.12, -18.03, 0.11803, 5, -18.52, -12.15, 0.01993, 6, 9.38, 1.24, 0.42254, 7, -3.99, 2.98, 0.33129, 6, 2, -17.99, 18.24, 0.03584, 3, 12.37, -17.8, 0.02783, 4, 3.88, -23.68, 0.06965, 5, -19.02, -18.04, 0.0117, 6, 15.27, 0.68, 0.25366, 7, 1.11, -0.02, 0.60132, 4, 3, 18.47, -23.29, 0.00026, 4, 9.67, -29.51, 0.00079, 6, 22.88, 3.76, 0.01389, 7, 9.31, -0.46, 0.98506], "hull": 57, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 0, 112, 82, 84, 84, 86], "width": 89, "height": 69}}, "y1": {"y1": {"x": 1.51, "y": -0.15, "rotation": -178.41, "width": 19, "height": 21}, "y2": {"x": 1.51, "y": -0.15, "rotation": -179.23, "width": 19, "height": 21}}}}], "animations": {"noWater": {"slots": {"1": {"attachment": [{"time": 0.2333, "name": "1"}, {"time": 0.4333, "name": "2"}, {"time": 0.5667, "name": "1"}, {"time": 0.7667, "name": "2"}]}, "3": {"attachment": [{"time": 0.1667, "name": "3"}, {"time": 0.3333, "name": "4"}, {"time": 0.5333, "name": "3"}, {"time": 0.7, "name": "4"}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.3333, "angle": 2.38}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.3333, "angle": -3.25}, {"time": 1}]}, "bone4": {"rotate": [{}, {"time": 0.3667, "angle": -1.32}, {"time": 1}]}, "bone5": {"rotate": [{}, {"time": 0.4, "angle": -4.68}, {"time": 1}]}}}, "water": {"slots": {"1": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "y1": {"attachment": [{"name": "y2"}]}}, "bones": {"bone8": {"rotate": [{}, {"time": 0.2, "angle": -2.92}, {"time": 0.6667}]}, "bone": {"translate": [{}, {"time": 0.3333, "y": 1.42}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.2, "angle": -0.56}, {"time": 0.4667, "angle": 3.61}, {"time": 0.6667}]}, "bone3": {"rotate": [{}, {"time": 0.2, "angle": 15.68}, {"time": 0.4667, "angle": 4.36}, {"time": 0.6667}]}, "bone4": {"rotate": [{}, {"time": 0.2667, "angle": 4.44}, {"time": 0.4667, "angle": -1.13}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.3, "angle": 10.01}, {"time": 0.5333, "angle": -10.27}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.2, "angle": -4}, {"time": 0.4667, "angle": 7.67}, {"time": 0.6667}]}, "bone7": {"rotate": [{}, {"time": 0.2333, "angle": -17.79}, {"time": 0.5333, "angle": 3.32}, {"time": 0.6667}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.2, "offset": 92, "vertices": [1.6865, 0.13078, -0.77484, -1.50366, -1.07966, -1.30219, -2.15101, -2.59435, -4.61116, -5.56157, -7.65653, -8.58747, -5.92715, -9.30593, -5.1139, 0.9617, -2.38353, -4.62554, -3.32124, -4.00576, -5.1868, 0.41695, -5.1139, 0.9617, -2.38353, -4.62554, -3.32124, -4.00576, -2.04756, 0.30273, -2.00422, 0.51701, -1.0684, -1.77277, -1.42417, -1.50197, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.71169, 0.63236, -2.62988, 0.91488, -1.90241, 2.03322, -1.6285, -2.25858, -2.07552, -1.85616, -5.1868, 0.41695, -5.1139, 0.9617, -4.07949, 3.23026, -2.38353, -4.62554, -3.32124, -4.00576, -6.14244, 1.73204, -5.92552, 2.37026, -4.14269, 4.85466, -3.96532, -5.0006, -7.13488, 6.63878, -11.13618, 7.35109, -12.07449, 6.1623, -8.10275, 2.24145, -6.12993, 5.75349, -5.1139, 0.9617, -4.07949, 3.23026, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.68941, -1.01508, 1.8278, 0.73733, 1.89536, 0.54046, -1.349, -0.85383, 1.46629, 0.63152, 1.52472, 0.47335, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.80183, -0.39517, 0.85392, 0.26442, 0.87704, 0.17288, 0.85679, -0.25496, 0.08507, 0.88986, 0.27419, 0.85083, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.32468, -1.17925, 2.48097, 0.79975, 2.55148, 0.53366, 0.21807, 2.59753, 0.77083, 2.49009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.29447, 0.25548, -3.29373, 0.26477, -3.24744, 0.61072, -2.59056, 2.05128, -1.5136, -2.93732, -2.10906, -2.54375, 3.29447, 0.25548, -3.29373, 0.26477, -3.24744, 0.61072, -2.59056, 2.05128, -1.5136, -2.93732, -2.10906, -2.54375, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.49859, 0.2586, -1.46297, 0.41527, -0.81611, -1.28321, -1.07261, -1.078]}, {"time": 0.2667, "offset": 34, "vertices": [-0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, -0.95164, -0.83514, -0.26956, 1.23708, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.43352, 0.11117, -0.65861, -1.27811, -0.91771, -1.10686, -1.82836, -2.20519, -3.91949, -4.72734, -6.50805, -7.29935, -5.03807, -7.91004, -4.34682, 0.81745, -2.026, -3.93171, -2.82305, -3.4049, -4.40878, 0.35441, -4.34682, 0.81745, -2.026, -3.93171, -2.82305, -3.4049, -1.74043, 0.25732, -1.70358, 0.43946, -0.90814, -1.50685, -1.21054, -1.27668, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.30494, 0.53751, -2.2354, 0.77765, -1.61705, 1.72824, -1.38423, -1.91979, -1.76419, -1.57774, -4.40878, 0.35441, -4.34682, 0.81745, -3.46756, 2.74572, -2.026, -3.93171, -2.82305, -3.4049, -5.22107, 1.47223, -5.03669, 2.01472, -3.52129, 4.12646, -3.37052, -4.25051, -6.06465, 5.64296, -10.29814, 4.97047, -10.55997, 4.04705, -7.02411, 1.64564, -5.41961, 4.6847, -4.34682, 0.81745, -3.46756, 2.74572, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.436, -0.86282, 1.55363, 0.62673, 1.61106, 0.45939, -1.14665, -0.72576, 1.24635, 0.53679, 1.29602, 0.40235, 0, 0, 0, 0, 0, 0, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, -0.49109, 1.21809, 0.39322, -1.25312, 0.22131, -1.29457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.68156, -0.3359, 0.72583, 0.22475, 0.74549, 0.14695, 0.72827, -0.21672, 0.07231, 0.75638, 0.23306, 0.72321, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.97598, -1.00236, 2.10882, 0.67979, 2.16875, 0.45361, 0.18536, 2.2079, 0.6552, 2.11658, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.8003, 0.21716, -2.79967, 0.22506, -2.76033, 0.51911, -2.20198, 1.74358, -1.28656, -2.49672, -1.7927, -2.16218, 2.8003, 0.21716, -2.79967, 0.22506, -2.76033, 0.51911, -2.20198, 1.74358, -1.28656, -2.49672, -1.7927, -2.16218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.2738, 0.21981, -1.24352, 0.35298, -0.69369, -1.09073, -0.91172, -0.9163]}, {"time": 0.3, "offset": 34, "vertices": [-1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, -1.42746, -1.25271, -0.40434, 1.85563, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.30703, 0.10136, -0.6005, -1.16534, -0.83674, -1.0092, -1.66703, -2.01062, -3.57365, -4.31022, -5.93381, -6.65529, -4.59354, -7.21209, -3.96327, 0.74532, -1.84724, -3.58479, -2.57396, -3.10447, -4.01977, 0.32314, -3.96327, 0.74532, -1.84724, -3.58479, -2.57396, -3.10447, -1.58686, 0.23462, -1.55327, 0.40068, -0.82801, -1.3739, -1.10373, -1.16403, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.10156, 0.49008, -2.03816, 0.70903, -1.47437, 1.57575, -1.26209, -1.7504, -1.60853, -1.43852, -4.01977, 0.32314, -3.96327, 0.74532, -3.1616, 2.50345, -1.84724, -3.58479, -2.57396, -3.10447, -4.76039, 1.34233, -4.59228, 1.83695, -3.21058, 3.76236, -3.07312, -3.87547, -5.52953, 5.14505, -9.87912, 3.78016, -9.80271, 2.98942, -6.48479, 1.34773, -5.06445, 4.15031, -3.96327, 0.74532, -3.1616, 2.50345, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.30929, -0.78669, 1.41654, 0.57143, 1.46891, 0.41886, -1.04547, -0.66172, 1.13638, 0.48942, 1.18166, 0.36685, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.62142, -0.30626, 0.66179, 0.20492, 0.67971, 0.13398, 0.66401, -0.19759, 0.06593, 0.68964, 0.21249, 0.65939, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.80163, -0.91392, 1.92275, 0.61981, 1.97739, 0.41358, 0.169, 2.01309, 0.59739, 1.92982, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.55321, 0.198, -2.55264, 0.2052, -2.51677, 0.47331, -2.00769, 1.58974, -1.17304, -2.27643, -1.63452, -1.9714, 2.55321, 0.198, -2.55264, 0.2052, -2.51677, 0.47331, -2.00769, 1.58974, -1.17304, -2.27643, -1.63452, -1.9714, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1614, 0.20042, -1.1338, 0.32184, -0.63248, -0.99449, -0.83127, -0.83545]}, {"time": 0.4667, "offset": 92, "vertices": [0.6746, 0.05231, -0.30993, -0.60146, -0.43187, -0.52088, -0.8604, -1.03774, -1.84446, -2.22463, -3.06261, -3.43499, -2.37086, -3.72237, -2.04556, 0.38468, -0.95341, -1.85021, -1.32849, -1.60231, -2.07472, 0.16678, -2.04556, 0.38468, -0.95341, -1.85021, -1.32849, -1.60231, -0.81903, 0.12109, -0.80169, 0.2068, -0.42736, -0.70911, -0.56967, -0.60079, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.08468, 0.25294, -1.05195, 0.36595, -0.76096, 0.81329, -0.6514, -0.90343, -0.83021, -0.74246, -2.07472, 0.16678, -2.04556, 0.38468, -1.63179, 1.29211, -0.95341, -1.85021, -1.32849, -1.60231, -2.45697, 0.69282, -2.37021, 0.9481, -1.65708, 1.94187, -1.58613, -2.00024, -2.85395, 2.65551, -7.784, -2.17139, -6.01641, -2.29873, -3.78819, -0.14179, -3.28864, 1.47833, -2.04556, 0.38468, -1.63179, 1.29211, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67576, -0.40603, 0.73112, 0.29493, 0.75815, 0.21618, -0.5396, -0.34153, 0.58652, 0.25261, 0.60989, 0.18934, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.32073, -0.15807, 0.34157, 0.10577, 0.35082, 0.06915, 0.34272, -0.10198, 0.03403, 0.35594, 0.10967, 0.34033, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.92987, -0.4717, 0.99239, 0.3199, 1.02059, 0.21346, 0.08723, 1.03901, 0.30833, 0.99604, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.31779, 0.10219, -1.31749, 0.10591, -1.29898, 0.24429, -1.03623, 0.82051, -0.60544, -1.17493, -0.84362, -1.0175, 1.31779, 0.10219, -1.31749, 0.10591, -1.29898, 0.24429, -1.03623, 0.82051, -0.60544, -1.17493, -0.84362, -1.0175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.59943, 0.10344, -0.58519, 0.16611, -0.32644, -0.51329, -0.42904, -0.4312]}, {"time": 0.6667}]}}}}}}