{"skeleton": {"hash": "OHWhz/4icZM", "spine": "3.8-from-4.0.09", "x": -90.39, "y": 3.96, "width": 169, "height": 133, "images": "E:/游戏项目/奶茶大冒险/spine/猛龙过江/images/", "audio": "E:\\游戏项目\\奶茶大冒险\\spine\\猛龙过江"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 43.28, "rotation": 115.81, "x": -1.23, "y": 66.91}, {"name": "bone2", "parent": "bone", "length": 12.69, "rotation": -0.34, "x": 80.15, "y": -3.95}, {"name": "bone3", "parent": "bone", "length": 21.27, "rotation": -93.59, "x": 1.89, "y": -64.66}, {"name": "bone4", "parent": "bone", "length": 44.21, "rotation": 175.1, "x": -11.08, "y": -0.95}, {"name": "bone5", "parent": "bone", "length": 12.69, "rotation": 89.9, "x": 19.5, "y": 72.9}], "slots": [{"name": "qipao", "bone": "bone4", "attachment": "qipao"}, {"name": "d2", "bone": "bone2"}, {"name": "d3", "bone": "bone5", "attachment": "d2"}, {"name": "d1", "bone": "bone3", "attachment": "d1"}], "skins": [{"name": "default", "attachments": {"d1": {"d1": {"x": 15.17, "y": 1.57, "rotation": -22.23, "width": 18, "height": 26}}, "d3": {"d2": {"x": 8.18, "y": 1.9, "rotation": 154.29, "width": 17, "height": 17}}, "qipao": {"qipao": {"type": "mesh", "uvs": [0.31673, 0, 0.17876, 0.07131, 0.0793, 0.18436, 0.02315, 0.30377, 0, 0.41682, 0, 0.59196, 0.03278, 0.73685, 0.11139, 0.84512, 0.23331, 0.95498, 0.35844, 1, 0.5702, 1, 0.65683, 1, 0.80443, 0.91358, 0.93437, 0.77825, 1, 0.59674, 1, 0.41363, 0.95041, 0.23849, 0.81405, 0.09679, 0.6825, 0.00603, 0.59587, 0, 0.44347, 0, 0.21727, 0.69545, 0.48678, 0.79098, 0.80122, 0.63973, 0.80122, 0.33084, 0.62314, 0.16366, 0.39534, 0.1748, 0.22689, 0.38497], "triangles": [12, 11, 22, 9, 22, 10, 11, 10, 22, 9, 8, 22, 7, 21, 8, 8, 21, 22, 22, 23, 12, 12, 23, 13, 7, 6, 21, 21, 27, 22, 22, 27, 24, 23, 22, 24, 26, 24, 27, 13, 23, 14, 6, 5, 21, 26, 20, 25, 20, 19, 25, 21, 5, 27, 24, 26, 25, 14, 23, 15, 23, 24, 15, 5, 4, 27, 4, 3, 27, 24, 16, 15, 3, 2, 27, 2, 1, 27, 27, 1, 26, 24, 17, 16, 24, 25, 17, 1, 0, 26, 26, 0, 20, 25, 18, 17, 25, 19, 18], "vertices": [1, 1, 73.66, -8.58, 1, 1, 1, 73.05, 11.95, 1, 2, 4, -73.37, -37.67, 9e-05, 1, 65.24, 30.31, 0.99991, 2, 4, -61.18, -50.26, 0.0106, 1, 54.17, 43.9, 0.9894, 2, 4, -48.22, -58.48, 0.04727, 1, 41.96, 53.2, 0.95273, 2, 4, -26.46, -66.79, 0.17936, 1, 20.99, 63.34, 0.82064, 2, 4, -6.92, -69.63, 0.33993, 1, 1.76, 67.84, 0.66007, 2, 4, 10.24, -65.07, 0.50981, 1, -15.72, 64.77, 0.49019, 2, 4, 29.63, -55.25, 0.72708, 1, -35.88, 56.64, 0.27292, 2, 4, 41.12, -41.96, 0.8607, 1, -48.46, 44.38, 0.1393, 2, 4, 51.1, -15.85, 0.9898, 1, -60.64, 19.21, 0.0102, 2, 4, 55.18, -5.17, 0.99976, 1, -65.61, 8.92, 0.00024, 1, 4, 51.39, 17.13, 1, 2, 4, 40.7, 39.58, 0.98363, 1, -55.02, -36.9, 0.01637, 2, 4, 21.24, 56.29, 0.86258, 1, -37.06, -55.21, 0.13742, 2, 4, -1.51, 64.98, 0.64315, 1, -15.13, -65.82, 0.35685, 2, 4, -25.61, 67.18, 0.40958, 1, 8.69, -70.07, 0.59042, 2, 4, -49.64, 57.09, 0.19118, 1, 33.49, -62.07, 0.80882, 2, 4, -67.11, 45.18, 0.07677, 1, 51.92, -51.69, 0.92323, 2, 4, -71.94, 34.78, 0.04241, 1, 57.62, -41.75, 0.95759, 2, 4, -79.12, 15.99, 0.00471, 1, 66.38, -23.64, 0.99529, 2, 4, -3.37, -44.91, 0.40489, 1, -3.89, 43.52, 0.59511, 2, 4, 21.2, -16.22, 0.93068, 1, -30.82, 17.02, 0.06932, 2, 4, 17.22, 29.74, 0.93304, 1, -30.78, -29.1, 0.06696, 2, 4, -21.16, 44.4, 0.40932, 1, 6.2, -46.99, 0.59068, 2, 4, -50.32, 30.37, 0.0832, 1, 36.46, -35.51, 0.9168, 2, 4, -59.67, 1.76, 0.00096, 1, 48.22, -7.8, 0.99904, 2, 4, -41.49, -28.99, 0.01587, 1, 32.73, 24.39, 0.98413], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 132, "height": 133}}}}], "animations": {"animation": {"slots": {"d1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "fffffff3"}, {"time": 0.9, "color": "ffffff00"}]}, "d2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffec"}, {"time": 0.9333, "color": "ffffff00"}]}, "d3": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffec"}, {"time": 0.7333, "color": "ffffff00"}]}}, "bones": {"bone3": {"translate": [{}, {"time": 0.1667, "x": -0.31, "y": 6.97}, {"time": 0.9, "x": -1.66, "y": -19.08}]}, "bone": {"rotate": [{}, {"time": 0.7333, "angle": -5.81}, {"time": 1.3333}], "scale": [{}, {"time": 0.3, "x": 0.98}, {"time": 0.7, "x": 0.95, "y": 0.95}, {"time": 0.9667, "y": 0.98}, {"time": 1.3333}]}, "bone2": {"translate": [{}, {"time": 0.4, "x": -4.87, "y": -29.7}, {"time": 0.4333, "x": -6.08, "y": -6.66}, {"time": 0.4667, "x": -7.29, "y": -10.13}, {"time": 0.9333, "x": 16.38, "y": -20.49}]}, "bone5": {"translate": [{"x": -4.87, "y": -29.7}, {"time": 0.0667, "x": -7.29, "y": -10.13}, {"time": 0.7333, "x": -3.93, "y": 7.84}]}}, "deform": {"default": {"qipao": {"qipao": [{}, {"time": 0.4333, "vertices": [-2.68781, 0.23065, -2.68781, 0.23065, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.69765, 0, -2.68781, 0.23065, 2.69765, 0, -2.68781, 0.23065, 2.69765, 0, -2.68781, 0.23065]}, {"time": 0.6667, "vertices": [-2.21349, 0.18995, -2.21349, 0.18995, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.12919, 0, 3.11774, -0.26756, -3.12919, 0, 3.11774, -0.26756, -3.12919, 0, 3.11774, -0.26756, -3.12919, 0, 3.11774, -0.26756, -3.12919, 0, 3.11774, -0.26756, -3.12919, 0, -3.12919, 0, 3.11774, -0.26756, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.2216, 0, -2.21349, 0.18995, 2.2216, 0, -2.21349, 0.18995, 2.2216, 0, -2.21349, 0.18995, 0, 0, 0, 0, -3.12919, 0, 3.11774, -0.26756]}, {"time": 1.3333}]}}}}}}