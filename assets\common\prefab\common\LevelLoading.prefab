[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "LevelLoading", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 37}, {"__id__": 39}, {"__id__": 41}, {"__id__": 43}, {"__id__": 45}, {"__id__": 47}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}, {"__id__": 19}], "_active": true, "_components": [{"__id__": 34}], "_prefab": {"__id__": 36}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 1600, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06E88qXndHgY8trx72/yj6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "efd66976-d09e-4047-8ab7-df088901132a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cNGU0TBpPmbQFb5AfFbU5"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 45, "_target": {"__id__": 1}, "_left": -160, "_right": -160, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68tFaskyxHj6gSwN2tP7HA"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_opacity": 255, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c5JUHldzxGYKRIcFH5c9EB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2C7k0XoxPx5mJQue5p4os"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": -2.728, "y": 4.547, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 15}, "_contentSize": {"__type__": "cc.Size", "width": 206.64, "height": 110.88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3+sQmF8NNn5AUozunarIO"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 17}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "加载中...", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 60, "_fontSize": 60, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 88, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27sBTdvXtK6aosWhJ385VW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "51dSdLduFLB4WaZYt2YGkk"}, {"__type__": "cc.Node", "_name": "Role", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 20}], "_active": true, "_components": [{"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 19}, "_prefab": {"__id__": 21}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 20}, "asset": {"__uuid__": "4e1e2978-04c8-4134-ab3d-fdf2c594404f", "__expectedType__": "cc.Prefab"}, "fileId": "6bbq4XZWtIJaGdyQ0SHyQm", "instance": {"__id__": 22}}, {"__type__": "cc.PrefabInstance", "fileId": "d1SZ5noLFJSImBpDzbeeaR", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 23}, {"__id__": 25}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 30}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_name"], "value": "RoleSpine"}, {"__type__": "cc.TargetInfo", "localID": ["6bbq4XZWtIJaGdyQ0SHyQm"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -163.707, "y": -26.365, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["defaultAnimation"], "value": "run"}, {"__type__": "cc.TargetInfo", "localID": ["97uPNmOZtKebfNSmNHON7J"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cesBtkd8tCE7Sp8hqpAkFT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bNJv5ZulGIIkZeBihaEOY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "faH39RJ0VGobYkp1z043zP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2NcMa1OhLDKsFhfhOfvjW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 38}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aak7uus2xIrIZklYMYUyfE"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 40}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c4+oybspNRbPAxN/BrKDE"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 42}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05ZyYNraFFCKzwLSVMQZqb"}, {"__type__": "49458xWVcpMNpjBh40m4PCo", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 44}, "zIndex": 100, "MusicLabel": null, "SoundLabel": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86qBtdZ3FFeool3x1iGemj"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 46}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2fQ2vgl9Ao70pqlI0e0+L"}, {"__type__": "f3d36qkNslPB4TIGcZlp8vd", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 48}, "zIndex": 100, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cElucyfFADpophBCwvfZf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx", "nestedPrefabInstanceRoots": [{"__id__": 20}]}]