import { Component, Sprite, _decorator, Node, Sprite<PERSON><PERSON>e, assetManager, Texture2D, UITransform, ImageAsset } from "cc";
import { EDartsType, tyqSDK } from "../tyq-sdk";
import TyqViewMgr from "../tyq-view-mgr";

const { ccclass, property } = _decorator;

@ccclass
export default class DartAd extends Component {
    @property(Sprite) spt_img: Sprite = null;
    private dartType: EDartsType = -1;
    private adData: any = {};
    @property(Node) btn_close: Node = null;
    private clickTime = 0;
    private config: any = null;
    protected onLoad(): void {
        this.btn_close.active = false;
    }
    protected start(): void {

    }
    public initData(data, type: EDartsType, config) {
        if (data) {
            this.adData = data;
            this.dartType = type;
            this.config = config;
            assetManager.loadRemote(data.img_name, (err: <PERSON>rror, image: ImageAsset) => {
                if (err) {
                    console.error(err);
                } else {
                    if (!this.spt_img) return;
                    let texture = new Texture2D();
                    texture.image = image;
                    let sptFrame = new SpriteFrame();
                    sptFrame.texture = texture;
                    this.spt_img.spriteFrame = sptFrame;

                    let uiCom_spt = this.spt_img.node.getComponent(UITransform);
                    let uiCom_close = this.btn_close.getComponent(UITransform);
                    if (this.dartType == EDartsType.grid) {
                        this.btn_close.active = false;
                        this.spt_img.node.getComponent(UITransform).setContentSize(60, 60);
                    } else if (this.dartType == EDartsType.infoFlow) {
                        // this.spt_img.node.setContentSize(510, 420);
                        this.btn_close.active = true;

                        this.btn_close.setPosition(uiCom_spt.width / 2 - uiCom_close.width / 2, uiCom_spt.height / 2 - uiCom_close.height / 2);
                    } else if (this.dartType == EDartsType.interstitial) {
                        // this.spt_img.node.setContentSize(705, 450);
                        this.btn_close.active = true;
                        this.btn_close.setPosition(uiCom_spt.width / 2 - uiCom_close.width / 2, uiCom_spt.height / 2 - uiCom_close.height / 2);
                    }
                }

            })
        }

    }
    public onclick() {
        console.log("点击");
        let nowTime = Date.now();
        if (nowTime - this.clickTime <= 500) {
            console.log("防止连续点击");
            return;
        }
        console.log("展示");
        this.clickTime = nowTime;
        let dartAd = this.adData;

        if (dartAd.is_h5) {
            TyqViewMgr.showLandPage(dartAd, this.config);
        } else {
            // 

            tyqSDK.onClickDart(dartAd, this.config);
        }
    }
    public onClickClose() {
        this.node.destroy();
    }

}
