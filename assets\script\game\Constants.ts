
import { _decorator, Component, Node } from 'cc';
import {uiManager} from "../framework/uiManager";
import {PosCenterMode, PosCenterSideMode} from "../channel/wechat/WXCustomAd";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = constants
 * DateTime = Wed Feb 23 2022 15:26:24 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = constants.ts
 * FileBasenameNoExtension = constants
 * URL = db://assets/script/game/constants.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Constants')
export class Constants extends Component {
    public static Version = "3.2.4" //整体json保存时的key头
    public static SaveHead = "MilkTeaAdventure" //整体json保存时的key头
    public static LevelHead = "level" // 关卡保存时用的key头
    public static TotalLevelNum = 16 // 总的关卡数量
    public static AdReloadTime = 30 // 刷新时间，秒
    public static MoveXSpeed = 280 // 主角左右移动的速度
    public static ImpulseUp = 115 // 主角跳起的的冲量大小
    public static PromptInterval = 15 // 提示信息秒数
    public static PromptShakeInterval = 30 // 提示按钮抖动

    //游戏变量，可以修改
    public static GameRunTime = 0 //游戏时间
    public static StrengthRecoverTime = 5*60 //每5分钟回复1点体力

    public static LevelPath = 'level{0}/Level{1}' //关卡的路径
    public static CSVTables = { //新增数据表 请往该数组中添加....
        level:"level", //关卡的配置表
        order:"order", //关卡的顺序配置表，用来调整关卡的顺序
    }
    //所有预制体预先加载的列表-待定已不是按类型加载
    public static Prefabs = [ //所有预先加载的预制体
        "resources|prefab/ui/mainUI",
        'prefab|common/AdRes',
        'prefab|common/LevelLoading'
    ]
    //二段预加载的包，不加载也可以运行，就是第一次会闪一下
    public static PrefabsLevel = [ //所有预先加载的预制体
        'levelCommon|Prefab/Prompt',
        'levelCommon|Prefab/PromptRes',
        'levelCommon|Prefab/Success',
        'levelCommon|Prefab/Menu',
        'levelCommon|Prefab/tips',
    ]

    //所有对话框加载的地址
    public static Dialogs = {
        mainUI:'resources|prefab/ui/mainUI', //主界面，关卡列表
        initAd:'resources|prefab/ui/InitAd', //初始化广告
        levelLoading:'prefab|common/LevelLoading', //关卡加载页面
        adRes:'prefab|common/AdRes',
        strengthPrompt:'prefab|common/StrengthPrompt',
        ops:'levelCommon|Prefab/Ops', //操作UI
        prompt:'levelCommon|Prefab/Prompt', //提示看广告界面
        promptRes:'levelCommon|Prefab/PromptRes', //提示答案界面
        success:'levelCommon|Prefab/Success', //关卡结束成功
        menu:'levelCommon|Prefab/Menu', //关卡
        tips:'levelCommon|Prefab/tips', //提示
    }

    //事件名称统一定义
    public static Events = {
        updateLoading: "updateLoading",
        clearLevelInsertTimeout: "clearLevelInsertTimeout", //插屏广告重新计时
        resetLevelInsertTimeout: "resetLevelInsertTimeout", //插屏广告重新计时
        finishLoading: "finishLoading",

        touchStart: "touchStart",
        touchMove: "touchMove",
        touchEnd: "touchEnd",
        touchCancel: "touchCancel",
        opsUp: "opsUp",
        opsDown: "opsDown",
        opsLeftStart: "opsLeftStart",
        opsLeftEnd: "opsLeftEnd",
        opsRightStart: "opsRightStart",
        opsRightEnd: "opsRightEnd",
        restart: "restart",
        showList: "showList",

        updateStrength: "updateStrength",
    }

    //声音文件统一定义
    public static Sounds = {
        unlock:"unlock",
        prompt:"prompt",
        jump:"jump",
        out:"out",
        openDoor:"openDoor",
        gameOver:"gameOver",
        win:"win",
        dragon:"dragon",
        tiger:"tiger",
        drop:"drop",
        disappear:"disappear",
        shout:"shout",
        appear:"appear",
        crush:"crush",
        waterFlow:"waterFlow",
    }

    //关卡通过状态
    public static LevelState = {
        locked: 0,
        open: 1,
        success: 2,
    }

    //关卡和包的配置关系，添加关卡的时候需要添加
    public static LevelBundlesConfig = {
        level1: "b1",
        level2: "b1",
        level3: "b1",
        level4: "b1",
        level5: "b1",
        level6: "b2",
        level7: "b2",
        level8: "b2",
        level9: "b2",
        level10: "b2",
        level11: "b3",
        level12: "b3",
        level13: "b3",
        level14: "b3",
        level15: "b3",
        level16: "b4",
        level17: "b4",
        level18: "b4",
        level19: "b4",
        level20: "b4",
        level21: "b5",
    }

    public static Colors = {
        black: `#000000`
    }

    public static AdWechatIds = {
        banner: {id:"adunit-1c8d548157dc1f64", w: 300, h:100}, //只被宽度控制最小300
        banners1: [
            //banner关卡界面3
            {id:"adunit-1c8d548157dc1f64", w: 300, h:100, x:0, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
            {id:"adunit-ddbba10418344551", w: 300, h:100, x:0, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
            {id:"adunit-2bbbe5f2076d74a7", w: 300, h:100, x:0, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
            //banner游戏中界面3
            {id:"adunit-f1a9791889523b4d", w: 300, h:100, x:66, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
            {id:"adunit-ed99d808561d2e83", w: 300, h:100, x:66, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
            {id:"adunit-0eb26d60c882acb5", w: 300, h:100, x:66, y:0, mode:PosCenterMode.horizontalCenter, sideMode:PosCenterSideMode.down}, //只被宽度控制最小300
        ],
        video: "adunit-d1117188656c74b5",
        interstitialAdUnitId: "adunit-47fcd300d48602c9",  //插屏广告ID
        matrix2x5: "adunit-88dde7f271b0f8f9",
        shu5x2: "adunit-eb2fa30efdb2f09b",
        shu5: "adunit-f8930b59dc4a3a50",
        shu4x2: "adunit-35be005452d306bf",
        initCenter: {id:"adunit-84e170da01a94819", w:300, h:188},
        shu4: {id:"adunit-a79feb80ea590919", w:63, h:280},
        shu4copy: {id:"adunit-35be005452d306bf", w:63, h:280},
        shu3: {id:"adunit-fa031f6250e4ea32",w:63, h:210},
        shu3copy: {id:"adunit-613a8d9bbb241af2",w:63, h:210},
        shu2x2: "adunit-2ce2843dea7c94cc",
        shu2: "adunit-0517072049538d43",
        heng3: {id:"adunit-6ade6effd6e99993", w: 174, h:83} ,
        heng4: {id:"adunit-884335792d453d39", w: 231, h:83} ,
        heng4Copy: {id:"adunit-1013f235efa06400", w: 231, h:83} ,
        heng5: {id:"adunit-c6e46e6953e832c1", w: 289, h:83}, //
        single2: {id:"adunit-c816062016a10fb7", w: 83, h:83 },
        single: {id:"adunit-bd12a44c9e5d41a8", w: 83, h:83},
    }
    public static AdFlags = {
        mainCanvasRight: "mainCanvasRight",
        mainCanvasLeft:"mainCanvasLeft",
        mainCanvasCenter:"mainCanvasCenter",
        mainCanvasInsert:"mainCanvasInsert",
        mainUIBottomLeft:"mainUIBottomLeft",
        mainUIBottomRight:"mainUIBottomRight",
        levelBottom:"levelBottom",
    }

    public static CacheDataKey = {
        music:"music",
        sound:"sound",
        playTime:"playTime",
        strength:"strength",//具体的体力值
        strengthRecoverTimeCal:"strengthRecoverTimeCal", //体力累计时间秒 231
        strengthRefreshTime:"strengthRefreshTime", //体力刷新时间标记 2018-01-01
        strengthAdTimes:"strengthAdTimes", //累计看广告的次数，2次就满了
    }

    public static WinType={
        videoSuccess:"videoSuccess",
        videoFail:"videoFail",
        levelSuccess:"levelSuccess",
        levelFail:"levelFail",
        restart:"restart",
    }

    start () {
        // [3]
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
