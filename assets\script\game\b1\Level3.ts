
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, UITransform, Prefab, UIOpacity, Sprite, SpriteFrame} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
import {poolManager} from "../../framework/poolManager";
import {MainListItem} from "../../items/MainListItem";
import {S12} from "../common/S12";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level3')
export class Level3 extends LevelDialogBase {

    @property(Node)
    BoxLeft:Node = null
    @property(Node)
    BoxRight:Node = null
    @property(Prefab)
    S12Prefab:Prefab =null
    //鼠、牛、虎、兔、龙、蛇、马、羊、猴、鸡、狗、猪
    leftAni:number = 0  //0-11  4 龙
    rightAni:number = 5 //0-11  2 虎

    leftNode:Node = null
    rightNode:Node = null
    isTrigger:boolean = false

    start () {
        super.start()
        //左边接触物体的逻辑
        this.BoxLeft.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBoxLeftContact, this);
        //右边边接触物体的逻辑
        this.BoxRight.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBoxRightContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false

        this.leftNode = poolManager.instance.getNode(this.S12Prefab, this.BoxLeft.getChildByName("AniNode"))
        this.leftNode.getComponent(S12).Init({num:this.leftAni})
        this.rightNode = poolManager.instance.getNode(this.S12Prefab, this.BoxRight.getChildByName("AniNode"))
        this.rightNode.getComponent(S12).Init({num:this.rightAni})

    }

    getSx12(num:number){
        num += 1
        if(num>=12){
            num = 0
        }
        return num
    }

    async checkKey(){
        if(this.leftAni==4 && this.rightAni==2){
            this.isTrigger = true
            //播放龙吟虎啸的动画
            audioManager.instance.playSound(Constants.Sounds.dragon)
            tween(this.BoxLeft.getChildByName("power")).to(0.5, {scale:new Vec3(5,5,1)}).call(()=>{
                this.BoxLeft.getChildByName("power").setScale(0,0)
            }).start()
            tween(this.BoxLeft.getChildByName("power").getComponent(UIOpacity)).to(0.6,{opacity:0}).start()

            // await Public.instance.DelayTime(0.5)
            await this.delayTime(0.5)

            audioManager.instance.playSound(Constants.Sounds.tiger)
            tween(this.BoxRight.getChildByName("power")).to(0.5, {scale:new Vec3(5,5,1)}).call(()=>{
                this.BoxRight.getChildByName("power").setScale(0,0)
            }).start()
            tween(this.BoxRight.getChildByName("power").getComponent(UIOpacity)).to(0.6,{opacity:0}).start()

            await this.delayTime(3)
            this.showKey()
        }
    }

    onBoxLeftContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            console.log("------left touch----below----")
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.BoxLeft.position.y
            tween(this.BoxLeft)
                .to(0.05,{position:new Vec3(this.BoxLeft.position.x,y+10,this.BoxLeft.position.z)})
                .to(0.1,{position:new Vec3(this.BoxLeft.position.x,y,this.BoxLeft.position.z)})
                .call(()=>{
                    if(this.isTrigger==false){
                        this.leftAni = this.getSx12(this.leftAni)
                        this.leftNode.getComponent(S12).Init({num:this.leftAni})
                        // this.BoxLeft.getChildByName("AniNode").removeChild()
                        // this.BoxLeft.getChildByName("AniNode").getComponent(Sprite).spriteFrame = this.sx12[this.leftAni]
                        this.checkKey()
                    }

                })
                .start()
        })
    }

    onBoxRightContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            // console.log("------left touch----below----")
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.BoxRight.position.y
            tween(this.BoxRight)
                .to(0.05,{position:new Vec3(this.BoxRight.position.x,y+10,this.BoxRight.position.z)})
                .to(0.1,{position:new Vec3(this.BoxRight.position.x,y,this.BoxRight.position.z)})
                .call(()=>{
                    if(this.isTrigger==false){
                        this.rightAni = this.getSx12(this.rightAni)
                        this.rightNode.getComponent(S12).Init({num:this.rightAni})
                        // this.BoxRight.getChildByName("AniNode").getComponent(Sprite).spriteFrame = this.sx12[this.rightAni]
                        this.checkKey()
                    }

                })
                .start()
        })
    }


}
