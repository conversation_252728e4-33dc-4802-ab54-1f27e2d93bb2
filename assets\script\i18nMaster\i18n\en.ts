//@ts-ignore
if (!window.i18nConfig) window.i18nConfig = {};
if (!window.i18nConfig.languages) window.i18nConfig.languages = {};
window.i18nConfig.languages.en = {
    "main": {
        "resources": "Loading Default.",
        "font": "Loading Font.",
        "prefab": "Loading prefab.",
        "textures": "Loading textures.",
    },
    // "main": {
    //     "%{value}/s": "%{value}/s",
    //     "free": "free"
    // },
    // "signReward": {
    //     "你已经连续签到%{value}天，继续保持": "Sign in for %{value} days，keep on",
    //     "diamondsNum": "diamonds x2",
    // },
    // "button": {
    //     "normalReceive": "<u><color=#ffffff>Normal receive</color></u>",
    //     "receive": "Receive",
    //     "directCollection": "<u><color=#ffffff>Receive</color></u>",
    //     "doubleReceive": "Double",
    //     "noUpdate": "<u><color=#ffffff>No update</color></u>",
    //     "giveUpReward": "<u><color=#ffffff>Give up reward</color></u>"
    // }

};
