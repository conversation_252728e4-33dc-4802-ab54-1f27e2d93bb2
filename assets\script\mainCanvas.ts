// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import { _decorator, tween, Camera, Node, Vec3,find ,assetManager, Label} from "cc";
import {loading} from "./ui/common/loading";
import {clientEvent} from "./framework/clientEvent";
import {Constants} from "./game/Constants";
import {resourceUtil} from "./framework/resourceUtil";
import {uiManager} from "./framework/uiManager";
import {Public} from "./game/Public";
import {comm} from "./framework/comm";
import {AdCtl} from "./channel/AdCtl";
import TyqEventMgr from "./tyqSDK/tyq-event-mgr";
import {i18n} from "./i18nMaster/runtime-scripts/LanguageData";
import {gameStorage} from "./framework/gameStorage";
import {DataCtl} from "./game/DataCtl";
const { ccclass, property } = _decorator;

@ccclass("mainCanvas")
export class mainCanvas extends comm {
    /* class member could be defined like this */
    // dummy = '';
    @property(loading)
    loadingUI: loading = null!;

    @property(Label)
    lbVersion: Label = null!;

    @property(Node)
    tip: Node = null;

    @property(Node)
    bottle: Node = null;

    @property(Camera)
    CameraNode: Camera = null;

    @property(Label)
    StrengthTimeLab: Label = null;

    @property(Label)
    CurStrengthLab: Label = null;

    curProgress: number = 0;
    isFirstLoad: boolean = true; //首次加载
    calTime:number=0
    calPlayTime:number=0 //记录游戏中的时长
    protoPosX:number=0 //原始位置

    adCnt:number=0

    start(){
        clientEvent.on(Constants.Events.updateStrength, this.updateStrength, this);
        this.lbVersion.string = `版本 ${Constants.Version}`;
        this.initStrength()

        AdCtl.instance.InitAdPage()

        //设置摄像机
        Public.MainCamera = this.CameraNode

        Public.isShowMainCanvas = true

        //首次进来，起始50%（前面为登录加载）
        this.loadingUI.show(this.curProgress);

    }

    start2(){
        this.protoPosX = this.bottle.position.x
        // let pos = this.bottle.position
        // tween(this.bottle).to(2,{position:new Vec3(pos.x+900, pos.y, pos.z)}).start()
        // console.log("------pos ---------0",pos)

        //加载所有主界面需要的字体，预制体，图集
        this.loadAllMainRes(()=>{
            console.log("------加载资源完毕，进入广告加载阶段---------")
        });
    }

    share(){
        Public.Share()
    }

    initStrength(){
        let strength = gameStorage.getInt(Constants.CacheDataKey.strength,-1)
        if(strength==-1){//没有体力
            gameStorage.setInt(Constants.CacheDataKey.strength,5)
        }
        this.updateStrength()
    }

    //隔日刷新体力
    refreshStrength(strength, StrengthRecoverTimeCal){
        this.StrengthTimeLab.string = Public.TimerFormat1(Constants.StrengthRecoverTime-StrengthRecoverTimeCal)
        this.CurStrengthLab.string = strength+""
        let lastUpdate = gameStorage.getString(Constants.CacheDataKey.strengthRefreshTime, null)
        if(lastUpdate==null){
            gameStorage.setString(Constants.CacheDataKey.strengthRefreshTime, Public.DateFormat(new Date(), "YYYY/mm/dd"))
        }
        if(lastUpdate != Public.DateFormat(new Date(), "YYYY/mm/dd")){
            console.log("@@@@@@@@@@@@@@@@ 隔日刷新体力------", lastUpdate)
            gameStorage.setInt(Constants.CacheDataKey.strength, 5)
            gameStorage.setInt(Constants.CacheDataKey.strengthAdTimes, 0)
            gameStorage.setString(Constants.CacheDataKey.strengthRefreshTime, Public.DateFormat(new Date(), "YYYY/mm/dd"))
        }
    }

    updateStrength(){
        let StrengthRecoverTimeCal = gameStorage.getInt(Constants.CacheDataKey.strengthRecoverTimeCal)
        let strength = gameStorage.getInt(Constants.CacheDataKey.strength)
        this.refreshStrength(strength, StrengthRecoverTimeCal)
    }

    OnClickPrompt(){
        uiManager.instance.showDialog(Constants.Dialogs.strengthPrompt)
    }

    update(dt){
        this.calTime+=dt
        if(this.calTime>2){
            if(Public.isRealLoadingEnd==true){
                this.calTime=-1000
                // clientEvent.dispatchEvent(Constants.Events.finishLoading);
                //等进度条加载完后展示主界面
                // this.showMainUI();
                find("Canvas").getChildByName("loading").active = true
                find("Canvas").getChildByName("loading").getComponent(loading).showBtn()
            }
        }
        if(Public.isRealLoadingEnd==false){
            if(this.adCnt>=1){
                console.log("------加载完毕---------")
                Public.isRealLoadingEnd=true
                TyqEventMgr.ins.sendEvent("进入游戏");
            }
        }

        //每10秒记录一次
        this.calPlayTime += dt
        if(this.calPlayTime>1){
            let oldVal = gameStorage.getInt(Constants.CacheDataKey.playTime)
            let StrengthRecoverTimeCal = gameStorage.getInt(Constants.CacheDataKey.strengthRecoverTimeCal)
            oldVal += this.calPlayTime
            StrengthRecoverTimeCal += this.calPlayTime
            // console.log("---this.calPlayStrengthTime----playTime-", StrengthRecoverTimeCal, oldVal)
            gameStorage.setInt(Constants.CacheDataKey.playTime, oldVal)
            // configuration.instance.setGlobalData()
            this.calPlayTime=0

            //如果时间超过5分钟，那么回复1点体力
            let strength = gameStorage.getInt(Constants.CacheDataKey.strength)
            // if(StrengthRecoverTimeCal>=5*60){
            //     DataCtl.instance.AddStrength(1)
            //     gameStorage.setInt(Constants.CacheDataKey.strengthRecoverTimeCal, 0)
            // }else{
            //     gameStorage.setInt(Constants.CacheDataKey.strengthRecoverTimeCal, StrengthRecoverTimeCal)
            // }
            this.refreshStrength(strength, StrengthRecoverTimeCal)
        }

    }

    onEnable () {
        clientEvent.on(Constants.Events.updateLoading, this.updateLoadingProgress, this);
        clientEvent.on(Constants.Events.finishLoading, this.finishLoading, this);

    }

    onDisable () {
        clientEvent.off(Constants.Events.updateLoading, this.updateLoadingProgress, this);
        clientEvent.off(Constants.Events.finishLoading, this.finishLoading, this);
    }

    updateLoadingProgress (progress: number, tips: string) {
        if (!this.isFirstLoad) {
            this.curProgress += progress;
        } else {
            this.curProgress += Math.floor(progress / 2);  //首次加载是跟登录一块的，这样起始是50%
        }
        this.loadingUI.updateProgress(this.curProgress, tips);
    }

    finishLoading () {
        console.log("-----------接收到加载完毕的请求----")
        this.start2()
        // this.loadingUI.node.active = false;
    }

    async loadAllMainRes(callback:Function){

        let pos = this.bottle.position
        this.protoPosX+=100
        tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        await this.loadBundle('resources')
        await this.delayTime(0.2)
        this.protoPosX+=100
        tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // console.log("----pos---2", pos)
        await this.loadBundle('font')
        await this.delayTime(0.2)
        this.protoPosX+=100
        tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // console.log("----pos---3", pos)
        await this.loadBundle('prefab')
        await this.delayTime(0.2)
        this.protoPosX+=100
        tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // console.log("----pos---4", pos)
        await this.loadBundle('textures')
        await this.delayTime(0.2)

        let arrPreload = Constants.Prefabs
        let cur = 0;
        let total = 100
        let pVal = 1/arrPreload.length
        for(let i=0; i<arrPreload.length; i++){
            cur++;
            this.protoPosX += total*pVal
            tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
            await resourceUtil.getUIPrefabResAsync(arrPreload[i])
            await this.delayTime(0.2)
            // await this.delayTime(0.5)
            // clientEvent.dispatchEvent(Constants.Events.updateLoading, pVal);
        }

        //加载广告实例
        AdCtl.instance.InitParam()
        this.protoPosX+=480
        tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()

        AdCtl.instance.InitCreateRewardVideoAd().then(()=>{
            // this.adCnt+=1
        })
        AdCtl.instance.InitCreateInterstitialAd().then(()=>{
            this.adCnt+=1
        })
        AdCtl.instance.InitCreateBannerAd()

        // this.loadCustomAd()

        // this.protoPosX+=100
        // tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // await this.delayTime(0.2)
        // await AdCtl.instance.InitCreateRewardVideoAd()
        //
        // this.protoPosX+=100
        // tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // await this.delayTime(0.2)
        // await AdCtl.instance.InitCreateInterstitialAd()
        //
        // this.protoPosX+=100
        // tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // await this.delayTime(0.2)
        // await AdCtl.instance.InitCreateCustomAdMainUI()
        //
        // this.protoPosX+=80
        // tween(this.bottle).to(0.2,{position:new Vec3(this.protoPosX, pos.y, pos.z)}).start()
        // await this.delayTime(0.2)
        // await AdCtl.instance.InitCreateMainCanvas()

        callback && callback()

        //加载所有UI窗口到隐藏节点
        // uiManager.instance.loadDialog(Constants.Dialogs.success)
        // uiManager.instance.loadDialog(Constants.Dialogs.prompt)
        // uiManager.instance.loadDialog(Constants.Dialogs.promptRes)
        // for(let key in Constants.Dialogs){
        //     uiManager.instance.showDialog(Constants.Dialogs[key], null, null, this.node.getChildByName("panelRoot"), false)
        // }
        // uiManager.instance.showDialog(Constants.Dialogs.success, null, null, this.node.getChildByName("panelRoot"), false)
        // uiManager.instance.showDialog(Constants.Dialogs.prompt, null, null, this.node.getChildByName("panelRoot"), false)
        // uiManager.instance.showDialog(Constants.Dialogs.promptRes, null, null, this.node.getChildByName("panelRoot"), false)


        // resourceUtil.getPrefabBatch(arrPreload, (arg)=>{
        //     cur++;
        //     if (cur <= 15) {
        //         clientEvent.dispatchEvent('updateLoading', 2);
        //     }
        // }, ()=>{
        //     if (cur <= 15) {
        //         clientEvent.dispatchEvent('updateLoading', 30 - cur * 2);
        //     }
        //     callback && callback();
        // });
    }

    async loadCustomAd(){
        //异步的同步方式去加载原生广告
        await AdCtl.instance.InitCreateCustomAdMainUI().then(()=>{ })
        await AdCtl.instance.InitCreateMainCanvas().then(()=>{
            console.log("原生广告加载完毕")
        })
    }

    OnClickStartGame(){
        TyqEventMgr.ins.sendEvent("进入游戏");
        this.showMainUI()
    }

    showMainUI(){
        //一开始加载主界面
        this.loadingUI.node.active = false;
        uiManager.instance.showDialog(Constants.Dialogs.mainUI);
        // AdCtl.instance.HideAllCustomAD()
        Public.isShowMainCanvas = false
    }

    async loadBundle(name){
        return new Promise(result=>{
            assetManager.loadBundle(name, (err,bundle)=>{
                result(bundle);
                this.curProgress += 5;
                this.loadingUI.updateProgress(this.curProgress, i18n.t("main."+name));
                if (err) {
                    return console.error(err);
                }
                // if (err) {
                //     console.log('加载bundle错误:',name,err)
                //     return;
                // }
                // console.log("加载完成")
            })
        })
    }

    // update (deltaTime: number) {
    //     // Your update function goes here.
    // }
}
