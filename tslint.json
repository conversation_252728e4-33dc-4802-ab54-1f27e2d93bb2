{"defaultSeverity": "error", "extends": ["tslint:recommended"], "jsRules": {}, "rules": {"quotemark": [true, "single"], "one-line": [true, "check-open-brace"], "no-console": false, "space-before-function-paren": true, "max-classes-per-file": false, "variable-name": false, "object-literal-sort-keys": false, "ban-types": false, "no-bitwise": false, "no-empty": false, "radix": false, "max-line-length": [true, 160]}, "rulesDirectory": []}