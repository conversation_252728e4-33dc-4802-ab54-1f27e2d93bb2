
import { _decorator, Component, Node } from 'cc';
import {gameStorage} from "../framework/gameStorage";
import {Constants} from "./Constants";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = dataCtl
 * DateTime = Wed Apr 27 2022 19:12:07 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = dataCtl.ts
 * FileBasenameNoExtension = dataCtl
 * URL = db://assets/script/game/dataCtl.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('DataCtl')
export class DataCtl extends Component {

    private static _instance: DataCtl;
    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new DataCtl();
        return this._instance;
    }

    public AddStrength(val:number){
        let ori = gameStorage.getInt(Constants.CacheDataKey.strength, 0)
        ori += val
        if(ori>=999){
            ori = 999
        }
        if(ori<=0){
            ori=0
        }
        gameStorage.setInt(Constants.CacheDataKey.strength, ori )
    }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
