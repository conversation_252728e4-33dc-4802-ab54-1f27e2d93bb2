{"skeleton": {"hash": "Uvi9+ahR+HY", "spine": "3.8-from-4.0.09", "x": -79.3, "y": 25.76, "width": 133, "height": 103.79, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/虎啸龙吟/羊"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 3.53, "y": 58.5}, {"name": "bone2", "parent": "bone", "length": 30.05, "rotation": 20.94, "x": 6.1, "y": 1.22}, {"name": "bone3", "parent": "bone", "length": 28.9, "rotation": 164.32, "x": -5.37, "y": 3.17}, {"name": "bone4", "parent": "bone3", "length": 16.83, "rotation": -3.81, "x": 19.71, "y": -7.9}, {"name": "bone5", "parent": "bone3", "length": 19.71, "rotation": 37.48, "x": 18.41, "y": 14.8}, {"name": "bone6", "parent": "bone2", "length": 23.53, "rotation": -61.73, "x": 17.74, "y": -13.06}, {"name": "bone7", "parent": "bone2", "length": 9.38, "rotation": 30.4, "x": 23.71, "y": 8.7}, {"name": "bone8", "parent": "bone4", "length": 25.01, "rotation": 170.92, "x": 3.82, "y": -18.84}, {"name": "bone9", "parent": "bone4", "length": 23.47, "rotation": 47.38, "x": 36.91, "y": -9.2}], "slots": [{"name": "j1", "bone": "bone5", "attachment": "j1"}, {"name": "j2", "bone": "bone6", "attachment": "j2"}, {"name": "body", "bone": "bone", "attachment": "body"}, {"name": "w", "bone": "bone7", "attachment": "w"}, {"name": "y2", "bone": "bone9", "attachment": "y2"}, {"name": "y1", "bone": "bone8", "attachment": "y1"}, {"name": "h", "bone": "bone4", "attachment": "h"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.17863, 0.15989, 0.22264, 0.05738, 0.30335, 0.03627, 0.34859, 0.07094, 0.40483, 0.0016, 0.55523, 0, 0.58947, 0.0619, 0.66406, 0.04079, 0.7631, 0.09959, 0.78878, 0.17798, 0.86336, 0.17345, 0.94162, 0.21114, 0.99053, 0.29255, 1, 0.41073, 0.96974, 0.49967, 0.99053, 0.55847, 0.98564, 0.65796, 0.94406, 0.73635, 0.90983, 0.75746, 0.90005, 0.83585, 0.8169, 0.9067, 0.73498, 0.90368, 0.68484, 0.95494, 0.5968, 0.96398, 0.55156, 0.9278, 0.49409, 0.98509, 0.39138, 0.99715, 0.32169, 0.967, 0.29479, 0.91876, 0.20797, 0.94288, 0.11627, 0.87806, 0.07347, 0.77253, 0.0967, 0.71374, 0.0429, 0.67907, 0.00133, 0.59465, 0.01355, 0.50872, 0.07591, 0.47254, 0.03312, 0.35646, 0.05391, 0.24189, 0.1126, 0.18762, 0.30245, 0.30695, 0.54161, 0.23653, 0.77364, 0.36636, 0.64692, 0.5974, 0.40062, 0.6062, 0.28282, 0.47858], "triangles": [6, 41, 4, 6, 4, 5, 3, 4, 41, 1, 3, 0, 3, 1, 2, 40, 3, 41, 40, 0, 3, 8, 9, 7, 39, 37, 38, 40, 36, 37, 39, 0, 40, 40, 37, 39, 45, 36, 40, 42, 12, 13, 12, 42, 11, 41, 6, 42, 7, 9, 6, 9, 42, 6, 43, 41, 42, 42, 9, 10, 11, 42, 10, 14, 42, 13, 44, 40, 41, 44, 41, 43, 45, 40, 44, 15, 18, 14, 36, 34, 35, 33, 34, 36, 32, 36, 45, 33, 36, 32, 14, 43, 42, 15, 16, 18, 14, 18, 43, 16, 17, 18, 18, 21, 43, 32, 28, 30, 44, 32, 45, 31, 32, 30, 18, 20, 21, 19, 20, 18, 44, 28, 32, 24, 44, 43, 24, 43, 21, 26, 28, 44, 29, 30, 28, 22, 24, 21, 23, 24, 22, 28, 26, 27, 26, 44, 24, 24, 25, 26], "vertices": [-27.75, 28.38, -23.79, 35.86, -16.52, 37.4, -12.45, 34.87, -7.39, 39.93, 6.15, 40.05, 9.23, 35.53, 15.94, 37.07, 24.85, 32.78, 27.17, 27.06, 33.88, 27.39, 40.92, 24.64, 45.32, 18.7, 46.18, 10.07, 43.45, 3.58, 45.32, -0.72, 44.88, -7.98, 41.14, -13.7, 38.06, -15.24, 37.18, -20.97, 29.7, -26.14, 22.32, -25.92, 17.81, -29.66, 9.89, -30.32, 5.82, -27.68, 0.64, -31.86, -8.6, -32.74, -14.87, -30.54, -17.29, -27.02, -25.11, -28.78, -33.36, -24.05, -37.21, -16.34, -35.12, -12.05, -39.96, -9.52, -43.7, -3.36, -42.6, 2.91, -36.99, 5.56, -40.84, 14.03, -38.97, 22.39, -33.69, 26.35, -16.6, 17.64, 4.92, 22.78, 25.8, 13.31, 14.4, -3.56, -7.77, -4.2, -18.37, 5.12], "hull": 40, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 0, 78], "width": 90, "height": 73}}, "h": {"h": {"x": 19.43, "y": -11.1, "rotation": -160.51, "width": 64, "height": 76}}, "j1": {"j1": {"x": 15.39, "y": 0.14, "rotation": 158.2, "width": 33, "height": 22}}, "j2": {"j2": {"x": 15.98, "y": -1.15, "rotation": 40.79, "width": 23, "height": 30}}, "w": {"w": {"x": 8.87, "y": 1.29, "rotation": -51.34, "width": 21, "height": 25}}, "y1": {"y1": {"x": 10.52, "y": -0.07, "rotation": 28.57, "width": 37, "height": 28}}, "y2": {"y2": {"x": 12.66, "y": -0.43, "rotation": 152.1, "width": 35, "height": 27}}}}], "animations": {"animation": {"bones": {"bone": {"rotate": [{}, {"time": 0.2667, "angle": 7.3}, {"time": 0.3333, "angle": 9.56}, {"time": 0.4, "angle": -7.15}, {"time": 0.7667}], "translate": [{}, {"time": 0.3333, "y": -18.68}, {"time": 0.7667}]}, "bone2": {"rotate": [{}, {"time": 0.2667, "angle": 7.3}, {"time": 0.4, "angle": -7.15}, {"time": 0.7667}]}, "bone3": {"rotate": [{}, {"time": 0.2667, "angle": 7.31}, {"time": 0.4, "angle": -7.15}, {"time": 0.7667}]}, "bone4": {"rotate": [{}, {"time": 0.2667, "angle": 7.3}, {"time": 0.4, "angle": -15.41}, {"time": 0.7667}]}, "bone5": {"rotate": [{}, {"time": 0.2667, "angle": 62.27}, {"time": 0.4, "angle": -7.15}, {"time": 0.7667}]}, "bone6": {"rotate": [{}, {"time": 0.2, "angle": -16.67}, {"time": 0.3333, "angle": -54.32}, {"time": 0.4333, "angle": -7.15}, {"time": 0.7667}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 23.2}, {"time": 0.4667, "angle": -35.6}, {"time": 0.7667}]}, "bone8": {"rotate": [{}, {"time": 0.2667, "angle": 32.07}, {"time": 0.4, "angle": -28.53}, {"time": 0.7667}]}, "bone9": {"rotate": [{}, {"time": 0.2667, "angle": -60.4}, {"time": 0.4, "angle": 11.71}, {"time": 0.7667}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.3333, "offset": 1, "vertices": [1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00083, 0, 1.58646, 0, 1.58646, 0, 1.58646, 0, 0.00083, 0, 0.00083, 0, 0.00083, 0, 0.00083, 0, 0.00083, 0, 0.00083, 0, 0, 0, -1.58564, 0, -1.58564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.58646]}, {"time": 0.5, "vertices": [1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 1.25387, 0.63539, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.25387, -1.93838, 1.25387, -0.9626, 1.25387, -0.9626, 1.25387, -0.9626, 1.25387, -1.93838, 3.33623, -2.92356, 3.33623, -2.92356, 3.33623, -2.92356, 3.33623, -2.92356, 3.33623, -2.92356, 2.08236, -0.98518, 0, -0.97578, 0, -0.97578, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.25387, 0.63539, 1.25387, -0.34038, 1.25387, -0.34038, 1.25387, -0.34038, 1.25387, -1.93838, 0, 0, 1.25387, -0.34038]}, {"time": 0.7667}]}}}}}}