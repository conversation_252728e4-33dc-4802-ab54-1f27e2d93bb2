
import { _decorator, Node, Vec3, <PERSON><PERSON><PERSON><PERSON>, tween } from 'cc';
import {uiManager} from "./uiManager";
import {Constants} from "../game/Constants";
import {comm} from "./comm";
import {Public} from "../game/Public";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = dialogBase
 * DateTime = Thu Feb 24 2022 15:10:51 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = dialogBase.ts
 * FileBasenameNoExtension = dialogBase
 * URL = db://assets/script/framework/dialogBase.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('dialogBase')
export class dialogBase extends comm {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;
    @property(CCInteger)
    zIndex:number = 100
    @property(Node)
    AniNode:Node = null

    dialogPath:string = "" //预制体路径


    show(args){
        if(this.AniNode!=null){
            this.AniNode.scale = new Vec3(0.1,0.1,0.1)
            tween(this.AniNode).to(0.16, {scale:new Vec3(1,1,1)}).start()
        }
        // this.node.position = new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0)
    }


    close(){
        if(this.AniNode!=null){
            tween(this.AniNode).to(0.16, {scale:new Vec3(0.1,0.1,0.1)}).call(()=>{
                console.log("close---this.dialogPath----", this.dialogPath)
                uiManager.instance.hideDialog(this.dialogPath)
                this.AniNode.scale = new Vec3(1,1,1)
            }).start()
        }else{
            uiManager.instance.hideDialog(this.dialogPath)
        }
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
