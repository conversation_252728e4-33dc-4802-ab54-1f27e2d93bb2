var classJavaName = "com/anythink/cocosjs/ATBannerJSBridge";
var ATAndroidBannerJS = ATAndroidBannerJS || {

    loadBanner: function (placementId, settings) {
        console.log("Android-loadBanner:" + settings);
        jsb.reflection.callStaticMethod(classJavaName, "load", "(Ljava/lang/String;Ljava/lang/String;)V", placementId, settings);
    },

    setAdListener: function (listener) {
        console.log("Android-setAdListener");
        jsb.reflection.callStaticMethod(classJavaName, "setAdListener", "(Ljava/lang/String;)V", listener);
    },

    hasAdReady: function (placementId) {
        console.log("Android-hasAdReady");
        return jsb.reflection.callStaticMethod(classJavaName, "isAdReady", "(Ljava/lang/String;)Z", placementId);;
    },

    checkAdStatus: function (placementId) {
        console.log("Android-checkAdStatus:" + placementId);
        return jsb.reflection.callStaticMethod(classJavaName, "checkAdStatus", "(Ljava/lang/String;)Ljava/lang/String;", placementId);
    },

    showAdInPosition: function (placementId, position) {
        console.log("Android-showAdInPosistion");
        jsb.reflection.callStaticMethod(classJavaName, "showWithPosition", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, position, "");
    },

    showAdInPositionAndScenario: function (placementId, position, scenario) {
        console.log("Android-showAdInPositionAndScenario");
        jsb.reflection.callStaticMethod(classJavaName, "showWithPosition", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, position, scenario);
    },

    showAdInRectangle: function (placementId, showAdRect) {
        console.log("Android-showAdInRectangle");
        jsb.reflection.callStaticMethod(classJavaName, "showWithRect", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, showAdRect, "");
    },

    showAdInRectangleAndScenario: function (placementId, showAdRect, scenario) {
        console.log("Android-showAdInRectangleAndScenario");
        jsb.reflection.callStaticMethod(classJavaName, "showWithRect", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, showAdRect, scenario);
    },

    removeAd: function (placementId) {
        console.log("Android-removeAd");
        jsb.reflection.callStaticMethod(classJavaName, "remove", "(Ljava/lang/String;)V", placementId);
    },

    reShowAd: function (placementId) {
        console.log("Android-reShowAd");
        jsb.reflection.callStaticMethod(classJavaName, "reshow", "(Ljava/lang/String;)V", placementId);
    },

    hideAd: function (placementId) {
        console.log("Android-hideAd");
        jsb.reflection.callStaticMethod(classJavaName, "hide", "(Ljava/lang/String;)V", placementId);
    }

};

// module.exports = ATAndroidBannerJS;
export default ATAndroidBannerJS;