{"skeleton": {"hash": "7pQpr7NKO0I", "spine": "3.8-from-4.0.09", "x": -61.78, "y": 12.73, "width": 129, "height": 94, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/虎啸龙吟/猪"}, "bones": [{"name": "root"}, {"name": "bone3", "parent": "root", "x": 4.84, "y": 45.6}, {"name": "bone", "parent": "bone3", "length": 39.52, "rotation": 1.21, "x": -1.52, "y": 10.26}, {"name": "bone2", "parent": "bone", "length": 44.11, "rotation": 170.66, "x": -7.48, "y": 0.3}, {"name": "bone4", "parent": "bone2", "length": 14.88, "rotation": 70.37, "x": 11.35, "y": 21.51}, {"name": "bone5", "parent": "bone2", "length": 16, "rotation": 49.97, "x": 20.57, "y": 17.78}, {"name": "bone6", "parent": "bone", "length": 17.66, "rotation": -37.27, "x": 26.91, "y": -19.29}, {"name": "bone7", "parent": "bone", "length": 14.67, "rotation": -60.52, "x": 18.37, "y": -23.13}, {"name": "bone8", "parent": "bone", "length": 9.1, "rotation": 6.68, "x": 45.71, "y": 4.31}], "slots": [{"name": "w", "bone": "bone8", "attachment": "w"}, {"name": "j1", "bone": "bone5", "attachment": "j1"}, {"name": "j3", "bone": "bone7", "attachment": "j3"}, {"name": "body", "bone": "root", "attachment": "body"}, {"name": "j2", "bone": "bone4", "attachment": "j2"}, {"name": "j4", "bone": "bone6", "attachment": "j4"}], "skins": [{"name": "default", "attachments": {"body": {"body": {"type": "mesh", "uvs": [0.01286, 0.56279, 0.08106, 0.6263, 0.14306, 0.77176, 0.28875, 0.90494, 0.46235, 1, 0.74289, 1, 0.96763, 0.86806, 1, 0.66727, 1, 0.43781, 0.86224, 0.1899, 0.70879, 0.10386, 0.59409, 0.08747, 0.50575, 0, 0.4701, 0.04239, 0.3988, 0, 0.35695, 0, 0.2965, 0.13869, 0.1818, 0.21859, 0.08571, 0.35176, 0.00511, 0.35791, 0, 0.52591, 0.28927, 0.64208, 0.40481, 0.693, 0.4491, 0.46644, 0.45488, 0.31625, 0.58391, 0.4308, 0.74953, 0.38753, 0.84196, 0.4588, 0.87855, 0.66754, 0.87277, 0.76427, 0.69175, 0.805, 0.53384, 0.79736], "triangles": [3, 22, 4, 4, 22, 31, 3, 21, 22, 3, 2, 21, 22, 23, 31, 31, 23, 25, 2, 1, 21, 22, 21, 23, 1, 18, 21, 18, 17, 21, 24, 23, 17, 23, 21, 17, 24, 17, 16, 16, 13, 24, 1, 0, 18, 0, 20, 18, 18, 20, 19, 23, 24, 25, 14, 16, 15, 25, 24, 11, 11, 24, 13, 11, 13, 12, 13, 16, 14, 4, 31, 5, 31, 30, 5, 5, 29, 6, 5, 30, 29, 6, 29, 7, 7, 29, 28, 31, 25, 30, 29, 30, 28, 30, 26, 27, 30, 25, 26, 28, 30, 27, 7, 28, 8, 28, 27, 8, 8, 27, 9, 27, 26, 9, 25, 10, 26, 25, 11, 10, 26, 10, 9], "vertices": [1, 3, 55.82, 6.18, 1, 1, 3, 47.27, 10.54, 1, 1, 3, 38.42, 22.06, 1, 2, 2, -32.47, -27.17, 0.03595, 3, 20.2, 31.16, 0.96405, 2, 2, -12.68, -35.86, 0.32601, 3, -0.73, 36.52, 0.67399, 2, 2, 19.57, -36.54, 0.91194, 3, -32.67, 31.96, 0.08806, 1, 2, 45.65, -25.61, 1, 1, 2, 49.74, -8.22, 1, 1, 2, 50.16, 11.74, 1, 2, 2, 34.78, 33.63, 0.95798, 3, -36.29, -39.75, 0.04202, 2, 2, 17.29, 41.49, 0.795, 3, -17.76, -44.67, 0.205, 2, 2, 4.14, 43.19, 0.56282, 3, -4.5, -44.21, 0.43718, 2, 2, -5.86, 51.01, 0.39585, 3, 6.63, -50.31, 0.60415, 2, 2, -10.04, 47.41, 0.34575, 3, 10.17, -46.08, 0.65425, 2, 2, -18.16, 51.27, 0.24889, 3, 18.8, -48.57, 0.75111, 2, 2, -22.97, 51.37, 0.23188, 3, 23.57, -47.89, 0.76812, 2, 2, -30.17, 39.46, 0.13537, 3, 28.74, -34.96, 0.86463, 2, 2, -43.51, 32.78, 0.02866, 3, 40.82, -26.21, 0.97134, 2, 2, -54.8, 21.43, 0.00067, 3, 50.12, -13.18, 0.99933, 1, 3, 59.22, -11.34, 1, 1, 3, 57.74, 3.21, 1, 2, 2, -31.93, -4.31, 0.06928, 3, 23.37, 8.51, 0.93072, 2, 2, -18.74, -9.02, 0.22459, 3, 9.59, 11.02, 0.77541, 2, 2, -13.23, 10.58, 0.31356, 3, 7.34, -9.21, 0.68644, 2, 2, -12.29, 23.63, 0.32297, 3, 8.53, -22.24, 0.67703, 2, 2, 2.34, 13.35, 0.48276, 3, -7.57, -14.48, 0.51724, 2, 2, 21.46, 16.72, 0.71288, 3, -25.89, -20.9, 0.28712, 2, 2, 31.95, 10.29, 0.81884, 3, -37.29, -16.26, 0.18116, 2, 2, 35.78, -7.95, 0.89605, 3, -44.03, 1.12, 0.10395, 2, 2, 34.94, -16.35, 0.91928, 3, -44.56, 9.55, 0.08072, 2, 2, 14.05, -19.46, 0.76833, 3, -24.45, 16, 0.23167, 2, 2, -4.09, -18.41, 0.46765, 3, -6.38, 17.91, 0.53235], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 115, "height": 87}}, "j1": {"j1": {"type": "mesh", "uvs": [0, 0.71791, 0.25833, 0.7562, 0.35284, 0.97223, 0.49756, 0.99684, 1, 0.5593, 1, 0.28858, 0.76336, 0, 0.55367, 0, 0.20517, 0.25576, 0, 0.64955], "triangles": [7, 6, 5, 1, 0, 9, 4, 1, 8, 1, 9, 8, 5, 8, 7, 5, 4, 8, 3, 2, 1, 4, 3, 1], "vertices": [24.09, -4.18, 19.96, 0.89, 22.09, 6.82, 19.84, 9.72, 2.6, 9.3, -2.27, 3.86, -3.06, -5.9, 0.84, -9.39, 11.94, -10.06, 22.86, -5.56], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 25, "height": 27}}, "j2": {"j2": {"type": "mesh", "uvs": [0.34538, 0.0185, 0, 0.75684, 0, 0.8816, 0.14455, 0.88415, 0.35424, 0.79503, 0.43694, 0.98344, 0.54326, 1, 0.72637, 0.90961, 1, 0.44877, 1, 0.1738, 0.70275, 0.01086], "triangles": [10, 9, 8, 8, 4, 0, 8, 0, 10, 1, 0, 4, 3, 2, 1, 4, 3, 1, 7, 4, 8, 6, 5, 4, 7, 6, 4], "vertices": [-0.28, -9.97, 22.69, -7.64, 25.9, -5.96, 24.28, -2.72, 19.55, 0.71, 23.42, 5.09, 22.61, 7.66, 18.16, 10.49, 3.14, 10.32, -3.91, 6.61, -4.63, -2.17], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 25, "height": 29}}, "j3": {"j3": {"type": "mesh", "uvs": [0, 0.43802, 0.34364, 1, 0.48124, 0.99589, 0.61884, 0.82908, 0.90411, 0.92205, 1, 0.87556, 1, 0.17003, 0.75309, 0, 0.38055, 0, 0, 0.30403], "triangles": [0, 9, 8, 6, 3, 8, 6, 8, 7, 0, 8, 3, 3, 6, 5, 4, 3, 5, 3, 1, 0, 2, 1, 3], "vertices": [1.96, -6.92, 18.87, -8.16, 20.31, -5.5, 17.99, -0.6, 23.35, 3.52, 23.34, 5.97, 6.96, 15.7, 0.24, 13.37, -3.94, 6.32, -1.15, -5.07], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 22, "height": 27}}, "j4": {"j4": {"type": "mesh", "uvs": [0, 0.51172, 0.50524, 0.93701, 0.75295, 1, 0.78867, 0.76276, 0.98874, 0.74504, 0.96731, 0.50581, 0.641, 0, 0.36948, 0, 0.1051, 0.20752, 0, 0.4054], "triangles": [0, 9, 8, 3, 6, 5, 3, 5, 4, 3, 1, 7, 3, 7, 6, 8, 7, 1, 0, 8, 1, 2, 1, 3], "vertices": [-1.16, -10.99, 17.76, -10.36, 24.9, -7.12, 22.3, -1.67, 27.05, 2.34, 23, 6.78, 7.37, 11.05, 0.57, 6.1, -3, -2.92, -2.72, -8.84], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 31, "height": 25}}, "w": {"w": {"x": 4.93, "y": -6.63, "rotation": -7.88, "width": 25, "height": 21}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{}, {"time": 0.2, "angle": 3.21}, {"time": 0.2667, "angle": -1.73}, {"time": 0.3333, "angle": -9.75}, {"time": 0.6667}], "translate": [{}, {"time": 0.2667, "y": -12.89}, {"time": 0.3333, "y": -11.96}, {"time": 0.6667}]}, "bone": {"rotate": [{}, {"time": 0.2, "angle": 7.46}, {"time": 0.3333, "angle": 5.52}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.2, "angle": 6.18}, {"time": 0.3333, "angle": 6.74}, {"time": 0.6667}]}, "bone4": {"rotate": [{}, {"time": 0.2, "angle": 11.34}, {"time": 0.2667, "angle": -10.58}, {"time": 0.3333, "angle": 46.87}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.2, "angle": -10.35}, {"time": 0.2667, "angle": 14.14}, {"time": 0.3333, "angle": 74.5}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.2667, "angle": -73.72}, {"time": 0.3333, "angle": -52.92}, {"time": 0.6667}]}, "bone7": {"rotate": [{}, {"time": 0.2667, "angle": -62.85}, {"time": 0.3333, "angle": -17.11}, {"time": 0.6667}]}, "bone8": {"rotate": [{}, {"time": 0.2, "angle": 29.84}, {"time": 0.3333, "angle": -27.88}, {"time": 0.5333, "angle": 9.7}, {"time": 0.6667}]}}, "deform": {"default": {"body": {"body": [{}, {"time": 0.2, "offset": 4, "vertices": [1.70887, 2.09467, -1.82171, -1.99734, 1.70887, 2.09467, -0.03412, -1.32836, -0.02939, 1.32846, -0.03412, -1.32836, -0.02939, 1.32846, -0.03412, -1.32836, 0, 0, 0.03411, 1.32832, -2.78553, -2.54252, 2.64119, 2.69213, -2.78553, -2.54252, 2.64119, 2.69213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.85204, -3.17812, 1.68274, 3.27553, -1.85204, -3.17812, 1.68274, 3.27553, -0.03033, -1.18078, -0.02613, 1.18086, 0, 0, 0, 0, -0.03033, -1.18078, -0.02613, 1.18086, -0.03033, -1.18078, -0.02613, 1.18086, -0.03033, -1.18078, -0.02613, 1.18086, 0, 0, 0, 0, -0.03033, -1.18078, -0.02613, 1.18086, -0.03033, -1.18078, -0.02613, 1.18086, -0.03033, -1.18078, -0.02613, 1.18086]}, {"time": 0.3, "offset": 10, "vertices": [-0.05118, -1.99254, -0.04409, 1.99269, -0.13078, -5.09208, -0.11268, 5.09245, -0.05118, -1.99254, 0, 0, 0.05117, 1.99249, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.04549, -1.77117, -0.03919, 1.77129, -0.04549, -1.77117, -0.03919, 1.77129, -0.04549, -1.77117, -0.03919, 1.77129, 0, 0, 0, 0, -0.04549, -1.77117, -0.03919, 1.77129, -0.04549, -1.77117, -0.03919, 1.77129, -0.04549, -1.77117, -0.03919, 1.77129, 0, 0, 0, 0, -0.04549, -1.77117, -0.03919, 1.77129, -0.15353, -5.97767, -0.13227, 5.9781, -0.1251, -4.87069, -0.10778, 4.87104]}, {"time": 0.4667, "offset": 10, "vertices": [5.06436, -0.96823, -5.08368, 0.49909, 2.18612, -1.03527, -2.2239, 0.83136, 2.18612, -1.03527, 0, 0, 0.02791, 1.08681, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.02481, -0.96609, -0.02138, 0.96616, -0.02481, -0.96609, -0.02138, 0.96616, -0.02481, -0.96609, -0.02138, 0.96616, 0, 0, 0, 0, -0.02481, -0.96609, -0.02138, 0.96616, -0.02481, -0.96609, -0.02138, 0.96616, -0.02481, -0.96609, -0.02138, 0.96616, 0, 0, 0, 0, 2.18922, -0.91452, -2.22122, 0.7106, -0.02481, -0.96609, -0.02138, 0.96616, 2.18922, -0.91452, -2.22122, 0.7106]}, {"time": 0.6667}]}}}}}}