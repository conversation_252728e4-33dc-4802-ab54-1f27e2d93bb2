{"skeleton": {"hash": "Jb+lJkq63og", "spine": "3.8-from-4.0.09", "x": -98.57, "y": -8.2, "width": 194.74, "height": 274.5, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/长驱直入"}, "bones": [{"name": "root", "rotation": -0.07}, {"name": "bone", "parent": "root", "length": 146, "rotation": 90, "x": 1.76, "y": 71.13}, {"name": "bone2", "parent": "bone", "length": 14.87, "rotation": 180, "x": 167.68, "y": 86.18}, {"name": "bone3", "parent": "bone2", "length": 39.93, "x": 20.62, "y": 0.25}, {"name": "bone4", "parent": "bone3", "length": 4.85, "x": 42.19, "y": 0.58}, {"name": "bone5", "parent": "bone4", "length": 10.93, "rotation": 0.43, "x": 6.74, "y": 0.33}, {"name": "bone6", "parent": "bone5", "length": 6.9, "rotation": -0.43, "x": 12.24, "y": -0.09}, {"name": "bone7", "parent": "bone", "length": 18.91, "rotation": 180, "x": 170.46, "y": -78.33}, {"name": "bone8", "parent": "bone7", "length": 41.77, "rotation": 0.87, "x": 21.96}, {"name": "bone9", "parent": "bone8", "length": 4.95, "rotation": -0.87, "x": 44.18, "y": -0.04}, {"name": "bone10", "parent": "bone9", "length": 7.74, "x": 7.11, "y": 0.38}, {"name": "bone11", "parent": "bone10", "length": 7.24, "rotation": -1.01, "x": 10.54}, {"name": "bone12", "parent": "bone", "length": 49.9, "rotation": 135.74, "x": 1.01, "y": 28.93}, {"name": "bone13", "parent": "bone12", "length": 28.86, "rotation": 65.17, "x": 51.44, "y": 2.76}, {"name": "bone14", "parent": "bone13", "length": 25.02, "rotation": -117.86, "x": 26.49, "y": -3.64}, {"name": "bone15", "parent": "bone", "length": 50.71, "rotation": -130.11, "x": 0.41, "y": -23.75}, {"name": "bone16", "parent": "bone15", "length": 30.95, "rotation": -70.69, "x": 51.34, "y": -1.08}, {"name": "bone17", "parent": "bone16", "length": 29.5, "rotation": 122.44, "x": 30.2, "y": 2.77}, {"name": "1", "parent": "root", "x": -47.05, "y": 7.38, "color": "ff3f00ff"}, {"name": "2", "parent": "root", "x": -83.49, "y": 6.08, "color": "ff3f00ff"}, {"name": "3", "parent": "root", "x": 43.4, "y": 7.05, "color": "ff3f00ff"}, {"name": "4", "parent": "root", "x": 81.14, "y": 8.35, "color": "ff3f00ff"}, {"name": "bone18", "parent": "bone", "x": 41.58, "y": 31.07}], "slots": [{"name": "le", "bone": "root", "attachment": "le"}, {"name": "rl", "bone": "root", "attachment": "rl"}, {"name": "m", "bone": "bone", "attachment": "m"}, {"name": "d2", "bone": "root", "attachment": "d2"}, {"name": "d1", "bone": "root", "attachment": "d1"}], "ik": [{"name": "1", "bones": ["bone12", "bone13"], "target": "1"}, {"name": "2", "order": 1, "bones": ["bone14"], "target": "2"}, {"name": "3", "order": 2, "bones": ["bone15", "bone16"], "target": "3", "bendPositive": false}, {"name": "4", "order": 3, "bones": ["bone17"], "target": "4"}], "skins": [{"name": "default", "attachments": {"d1": {"d1": {"type": "mesh", "uvs": [0.42713, 0.00628, 0.58877, 0.00628, 0.60086, 0.12665, 0.58877, 0.2214, 0.72473, 0.22478, 0.7776, 0.24206, 0.81234, 0.28363, 0.91053, 0.32424, 0.97247, 0.36001, 0.98002, 0.59639, 0.8773, 0.63651, 0.835, 0.7008, 0.79119, 0.72594, 0.60539, 0.73319, 0.60539, 0.74673, 0.66128, 0.75253, 0.76276, 0.78632, 0.77718, 0.96078, 0.67045, 0.99493, 0.42527, 0.9977, 0.32719, 0.95708, 0.32142, 0.79001, 0.39354, 0.75124, 0.45411, 0.74755, 0.45411, 0.73094, 0.30412, 0.72263, 0.17431, 0.68017, 0.18585, 0.64509, 0.05316, 0.57955, 0.05028, 0.3451, 0.20316, 0.29433, 0.21469, 0.26202, 0.307, 0.21956, 0.40796, 0.22233, 0.41084, 0.02387], "triangles": [20, 16, 17, 18, 20, 17, 19, 20, 18, 23, 16, 20, 15, 16, 14, 27, 24, 25, 11, 13, 24, 12, 13, 11, 23, 24, 13, 14, 23, 13, 23, 20, 21, 16, 23, 14, 23, 21, 22, 8, 28, 29, 7, 30, 6, 8, 30, 7, 29, 30, 8, 8, 27, 28, 33, 30, 31, 6, 3, 5, 3, 6, 30, 9, 27, 8, 10, 27, 9, 25, 26, 27, 24, 27, 10, 10, 11, 24, 34, 0, 1, 2, 34, 1, 34, 2, 33, 3, 33, 2, 31, 32, 33, 3, 30, 33, 5, 3, 4], "vertices": [1, 7, -3.97, -2.58, 1, 1, 7, -3.97, 2.59, 1, 1, 7, 8.07, 2.98, 1, 2, 7, 17.54, 2.59, 0.86652, 8, -4.38, 2.66, 0.13348, 2, 7, 17.88, 6.94, 0.41378, 8, -3.97, 7, 0.58622, 2, 7, 19.61, 8.63, 0.30874, 8, -2.22, 8.67, 0.69126, 2, 7, 23.77, 9.74, 0.09651, 8, 1.95, 9.72, 0.90349, 2, 7, 27.83, 12.89, 0.00739, 8, 6.06, 12.8, 0.99261, 1, 8, 9.67, 14.72, 1, 2, 8, 33.31, 14.61, 0.98259, 9, -11.09, 14.48, 0.01741, 2, 8, 37.27, 11.26, 0.89979, 9, -7.08, 11.19, 0.10021, 2, 8, 43.68, 9.81, 0.57102, 9, -0.65, 9.83, 0.42898, 2, 8, 46.17, 8.37, 0.452, 9, 1.86, 8.43, 0.548, 3, 8, 46.8, 2.41, 0.06549, 9, 2.59, 2.49, 0.90084, 10, -4.52, 2.11, 0.03367, 3, 8, 48.16, 2.39, 0.00527, 9, 3.94, 2.49, 0.75182, 10, -3.17, 2.11, 0.24291, 2, 9, 4.52, 4.28, 0.39645, 10, -2.59, 3.9, 0.60355, 3, 9, 7.9, 7.52, 0.03589, 10, 0.79, 7.14, 0.96056, 11, -9.87, 6.97, 0.00355, 2, 10, 18.24, 7.6, 0.00139, 11, 7.57, 7.74, 0.99861, 1, 11, 11.04, 4.38, 1, 1, 11, 11.46, -3.46, 1, 2, 10, 17.87, -6.8, 0.00176, 11, 7.45, -6.67, 0.99824, 3, 9, 8.27, -6.6, 0.2412, 10, 1.16, -6.98, 0.75261, 11, -9.25, -7.14, 0.0062, 2, 9, 4.39, -4.29, 0.66692, 10, -2.71, -4.67, 0.33308, 3, 8, 48.17, -2.45, 0.0008, 9, 4.02, -2.35, 0.88759, 10, -3.08, -2.73, 0.11161, 3, 8, 46.51, -2.43, 0.06803, 9, 2.36, -2.35, 0.93103, 10, -4.75, -2.73, 0.00094, 2, 8, 45.6, -7.21, 0.49461, 9, 1.53, -7.15, 0.50539, 2, 8, 41.29, -11.3, 0.77312, 9, -2.71, -11.31, 0.22688, 2, 8, 37.79, -10.88, 0.89451, 9, -6.22, -10.94, 0.10549, 2, 8, 31.17, -15.02, 0.99731, 9, -12.77, -15.18, 0.00269, 2, 7, 29.91, -14.64, 0.00165, 8, 7.73, -14.76, 0.99835, 2, 7, 24.84, -9.75, 0.0939, 8, 2.73, -9.79, 0.9061, 2, 7, 21.61, -9.38, 0.24965, 8, -0.5, -9.37, 0.75035, 2, 7, 17.36, -6.43, 0.54721, 8, -4.7, -6.36, 0.45279, 2, 7, 17.64, -3.2, 0.83241, 8, -4.37, -3.13, 0.16759, 1, 7, -2.21, -3.1, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68], "width": 32, "height": 100}}, "d2": {"d2": {"type": "mesh", "uvs": [0.394, 0, 0.58299, 0, 0.5876, 0.1161, 0.5876, 0.21327, 0.67979, 0.21953, 0.78581, 0.2689, 0.78581, 0.28614, 0.91949, 0.35823, 0.93331, 0.53455, 0.90335, 0.60899, 0.8319, 0.64661, 0.80655, 0.69519, 0.76276, 0.7234, 0.58299, 0.73359, 0.58299, 0.74691, 0.67749, 0.76807, 0.72819, 0.81743, 0.7305, 0.96397, 0.68671, 0.99218, 0.55994, 0.99688, 0.35943, 0.98748, 0.29029, 0.95692, 0.29029, 0.82449, 0.33408, 0.75474, 0.41013, 0.74612, 0.42857, 0.73045, 0.32025, 0.72967, 0.20271, 0.69832, 0.16122, 0.65131, 0.04598, 0.57765, 0.03907, 0.35432, 0.19349, 0.2689, 0.22345, 0.22737, 0.38478, 0.22267, 0.38939, 0.11923], "triangles": [21, 16, 17, 19, 20, 21, 21, 17, 19, 18, 19, 17, 22, 16, 21, 24, 22, 23, 14, 22, 24, 16, 22, 14, 15, 16, 14, 10, 11, 26, 11, 25, 26, 11, 13, 25, 12, 13, 11, 25, 13, 14, 24, 25, 14, 4, 5, 6, 30, 8, 29, 6, 3, 4, 31, 32, 33, 6, 31, 33, 6, 30, 31, 8, 30, 7, 7, 30, 6, 29, 8, 28, 9, 10, 8, 10, 28, 8, 28, 26, 27, 28, 10, 26, 0, 1, 2, 34, 0, 2, 34, 2, 3, 33, 34, 3, 6, 33, 3], "vertices": [1, 2, -7.37, -2.35, 1, 1, 2, -7.37, 4.08, 1, 1, 2, 4.24, 4.24, 1, 2, 2, 13.96, 4.24, 0.82828, 3, -6.67, 3.99, 0.17172, 2, 2, 14.58, 7.37, 0.52547, 3, -6.04, 7.12, 0.47453, 2, 2, 19.52, 10.97, 0.17364, 3, -1.1, 10.73, 0.82636, 2, 2, 21.24, 10.97, 0.11003, 3, 0.62, 10.73, 0.88997, 2, 2, 28.45, 15.52, 0.00066, 3, 7.83, 15.27, 0.99934, 2, 3, 25.46, 15.74, 0.94242, 4, -16.73, 15.17, 0.05758, 2, 3, 32.91, 14.72, 0.80388, 4, -9.28, 14.15, 0.19612, 2, 3, 36.67, 12.3, 0.62237, 4, -5.52, 11.72, 0.37763, 2, 3, 41.53, 11.43, 0.32333, 4, -0.66, 10.86, 0.67667, 2, 3, 44.35, 9.94, 0.20836, 4, 2.16, 9.37, 0.79164, 3, 3, 45.36, 3.83, 0.01548, 4, 3.18, 3.26, 0.87747, 5, -3.54, 2.96, 0.10706, 3, 3, 46.7, 3.83, 0.00046, 4, 4.51, 3.26, 0.58827, 5, -2.21, 2.95, 0.41127, 2, 4, 6.63, 6.47, 0.0599, 5, -0.07, 6.14, 0.9401, 2, 5, 4.88, 7.83, 0.98015, 6, -7.42, 7.87, 0.01985, 2, 5, 19.54, 7.8, 0.0257, 6, 7.24, 7.94, 0.9743, 2, 5, 22.35, 6.29, 0.00108, 6, 10.06, 6.45, 0.99892, 1, 6, 10.53, 2.15, 1, 1, 6, 9.59, -4.67, 1, 2, 5, 18.72, -7.16, 0.00511, 6, 6.53, -7.02, 0.99489, 3, 4, 12.27, -6.69, 0.03383, 5, 5.48, -7.06, 0.91057, 6, -6.71, -7.02, 0.05559, 2, 4, 5.29, -5.21, 0.44929, 5, -1.49, -5.52, 0.55071, 3, 3, 46.62, -2.04, 0.00758, 4, 4.43, -2.62, 0.76563, 5, -2.33, -2.93, 0.22679, 2, 3, 45.05, -1.42, 0.09462, 4, 2.86, -1.99, 0.90538, 2, 3, 44.97, -5.1, 0.46035, 4, 2.79, -5.68, 0.53965, 2, 3, 41.84, -9.1, 0.76173, 4, -0.35, -9.67, 0.23827, 2, 3, 37.14, -10.51, 0.94102, 4, -5.05, -11.08, 0.05898, 1, 3, 29.77, -14.43, 1, 2, 2, 28.06, -14.41, 0.00155, 3, 7.44, -14.66, 0.99845, 2, 2, 19.52, -9.16, 0.18974, 3, -1.1, -9.41, 0.81026, 2, 2, 15.37, -8.15, 0.38439, 3, -5.26, -8.39, 0.61561, 2, 2, 14.9, -2.66, 0.81202, 3, -5.73, -2.91, 0.18798, 1, 2, 4.55, -2.5, 1], "hull": 35, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 0, 68], "width": 34, "height": 100}}, "le": {"le": {"type": "mesh", "uvs": [0.63006, 0.00876, 0.4754, 0.17416, 0.40158, 0.22208, 0.41564, 0.24681, 0.38928, 0.29782, 0.29789, 0.33492, 0.31195, 0.3782, 0.34885, 0.37047, 0.26625, 0.46167, 0.24692, 0.50805, 0.26935, 0.5519, 0.34272, 0.64414, 0.34226, 0.68308, 0.35656, 0.70863, 0.37593, 0.692, 0.4096, 0.72729, 0.4096, 0.75325, 0.38239, 0.7565, 0.29983, 0.70417, 0.28415, 0.67659, 0.27862, 0.62832, 0.24218, 0.57964, 0.18868, 0.55246, 0.127, 0.58735, 0.07258, 0.61209, 0.07581, 0.66199, 0.06059, 0.69484, 0.03614, 0.7135, 0.03338, 0.75123, 0.02, 0.77029, 0.00155, 0.78489, 0.01097, 0.85966, 0, 0.92339, 0.05815, 0.98267, 0.14914, 0.98267, 0.2098, 0.96636, 0.26541, 0.996, 0.42717, 1, 0.52659, 0.95599, 0.61253, 0.96192, 0.68498, 0.86262, 0.6159, 0.75295, 0.66308, 0.73961, 0.59736, 0.68923, 0.57546, 0.63736, 0.62938, 0.63587, 0.61421, 0.59289, 0.55355, 0.56918, 0.52153, 0.53806, 0.55187, 0.49953, 0.58894, 0.5099, 0.61421, 0.44173, 0.65128, 0.42987, 0.66982, 0.45803, 0.69341, 0.378, 0.93268, 0.23771, 0.9984, 0.16213, 0.97818, 0.09692, 0.84675, 0.01393, 0.72879, 0], "triangles": [25, 24, 23, 31, 30, 29, 23, 22, 19, 21, 19, 22, 20, 19, 21, 19, 25, 23, 35, 18, 17, 25, 19, 18, 18, 26, 25, 18, 28, 26, 28, 27, 26, 18, 35, 28, 31, 29, 28, 35, 31, 28, 33, 32, 31, 31, 34, 33, 35, 34, 31, 36, 35, 17, 37, 16, 38, 17, 16, 37, 36, 17, 37, 44, 47, 46, 44, 46, 45, 48, 11, 10, 14, 11, 48, 14, 48, 47, 14, 47, 44, 12, 11, 14, 13, 12, 14, 15, 14, 44, 15, 44, 43, 41, 43, 42, 16, 15, 43, 16, 43, 41, 38, 16, 41, 38, 41, 40, 39, 38, 40, 55, 58, 57, 55, 57, 56, 3, 2, 1, 7, 5, 4, 54, 0, 59, 55, 54, 59, 55, 59, 58, 1, 0, 54, 3, 1, 54, 3, 51, 4, 6, 5, 7, 3, 54, 51, 51, 7, 4, 51, 54, 52, 53, 52, 54, 49, 7, 51, 50, 49, 51, 48, 7, 49, 8, 7, 48, 10, 8, 48, 9, 8, 10], "vertices": [1, 12, 8.76, -16.59, 1, 1, 12, 26.47, -15.1, 1, 1, 12, 33.08, -16.18, 1, 1, 12, 33.83, -14.01, 1, 1, 12, 38.21, -12.44, 1, 1, 12, 45.07, -15.07, 1, 1, 12, 46.92, -11.82, 1, 1, 12, 44.58, -10.34, 1, 2, 12, 54.21, -9.38, 0.87512, 13, -9.85, -7.62, 0.12488, 2, 12, 57.96, -7.7, 0.73851, 13, -6.76, -10.31, 0.26149, 2, 12, 59.42, -3.99, 0.53578, 13, -2.78, -10.08, 0.46422, 2, 12, 61.16, 5.19, 0.01939, 13, 6.29, -7.81, 0.98061, 3, 12, 63.5, 7.42, 0.00054, 13, 9.29, -8.99, 0.99749, 14, 12.77, -12.7, 0.00197, 2, 13, 11.65, -8.77, 0.99769, 14, 11.48, -10.73, 0.00231, 2, 13, 10.86, -6.96, 0.98756, 14, 10.24, -12.27, 0.01244, 2, 13, 14.48, -5.71, 0.84704, 14, 7.45, -9.66, 0.15296, 2, 13, 16.49, -6.48, 0.53479, 14, 7.19, -7.52, 0.46521, 2, 13, 16.03, -8.43, 0.21748, 14, 9.13, -7.01, 0.78252, 2, 13, 9.82, -12.51, 0.00645, 14, 15.63, -10.59, 0.99355, 2, 13, 7.28, -12.76, 0.00056, 14, 17.05, -12.73, 0.99944, 1, 14, 17.93, -16.65, 1, 1, 14, 21.06, -20.34, 1, 1, 14, 25.21, -22.11, 1, 1, 14, 29.33, -18.69, 1, 1, 14, 33.03, -16.17, 1, 1, 14, 32.29, -12.09, 1, 1, 14, 33.06, -9.25, 1, 1, 14, 34.65, -7.49, 1, 1, 14, 34.47, -4.36, 1, 1, 14, 35.25, -2.67, 1, 1, 14, 36.44, -1.31, 1, 1, 14, 35, 4.77, 1, 1, 14, 35.16, 10.12, 1, 1, 14, 30.35, 14.49, 1, 1, 14, 23.76, 13.68, 1, 1, 14, 19.52, 11.81, 1, 1, 14, 15.2, 13.76, 1, 2, 13, 36.08, -12.59, 0.01595, 14, 3.43, 12.66, 0.98405, 2, 13, 35.26, -4.5, 0.45091, 14, -3.33, 8.15, 0.54909, 2, 13, 37.95, 1.18, 0.911, 14, -9.61, 7.88, 0.089, 1, 13, 32.14, 9.06, 1, 1, 13, 21.84, 7.6, 1, 1, 13, 22.03, 11.21, 1, 1, 13, 16.42, 8.22, 1, 2, 12, 48.9, 16.97, 0.00079, 13, 11.82, 8.27, 0.99921, 1, 13, 13.11, 11.99, 1, 2, 12, 44.29, 16.42, 0.0021, 13, 9.39, 12.23, 0.9979, 2, 12, 45.97, 11.87, 0.06679, 13, 5.97, 8.79, 0.93321, 2, 12, 45.75, 8.39, 0.33538, 13, 2.72, 7.53, 0.66462, 2, 12, 41.91, 7.75, 0.81807, 13, 0.52, 10.74, 0.18193, 2, 12, 40.64, 10.29, 0.90269, 13, 2.29, 12.96, 0.09731, 2, 12, 35.3, 7.66, 0.98356, 13, -2.34, 16.7, 0.01644, 2, 12, 32.71, 8.91, 0.99877, 13, -2.29, 19.58, 0.00123, 1, 12, 33.44, 11.51, 1, 1, 12, 27.48, 8.11, 1, 1, 12, 6.95, 12.49, 1, 1, 12, -0.89, 11.55, 1, 1, 12, -3.74, 6.71, 1, 1, 12, -1.98, -4.97, 1, 1, 12, 3.21, -11.94, 1], "hull": 60, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 0, 118], "width": 73, "height": 83}}, "m": {"m": {"x": 88.55, "y": 2.92, "rotation": -90, "width": 194, "height": 213}}, "rl": {"rl": {"type": "mesh", "uvs": [0.17961, 0.00808, 0.36911, 0.10279, 0.40604, 0.08018, 0.43423, 0.09573, 0.41673, 0.13813, 0.48768, 0.18619, 0.53821, 0.16923, 0.56348, 0.18619, 0.53432, 0.23567, 0.55765, 0.25404, 0.60624, 0.26677, 0.65386, 0.32755, 0.66429, 0.4064, 0.6536, 0.43467, 0.66041, 0.51807, 0.68081, 0.55341, 0.68081, 0.58592, 0.65166, 0.58875, 0.65263, 0.63822, 0.66332, 0.6665, 0.69151, 0.62126, 0.71191, 0.53362, 0.69734, 0.47991, 0.69345, 0.38944, 0.73621, 0.33431, 0.79743, 0.33996, 0.847, 0.38378, 0.84603, 0.42336, 0.8781, 0.43043, 0.90239, 0.45587, 0.90433, 0.48556, 0.93738, 0.48556, 0.94612, 0.54917, 0.96362, 0.57744, 0.97236, 0.61561, 0.98694, 0.64105, 0.98888, 0.71738, 0.9607, 0.76545, 0.91794, 0.77393, 0.82659, 0.89267, 0.76245, 0.93931, 0.69636, 0.94214, 0.65458, 0.98031, 0.58946, 0.98313, 0.53893, 0.93225, 0.51852, 0.85591, 0.53407, 0.76827, 0.50686, 0.78948, 0.49131, 0.75414, 0.50686, 0.70608, 0.50491, 0.61137, 0.46993, 0.64529, 0.4573, 0.61561, 0.49908, 0.54069, 0.48353, 0.48697, 0.44952, 0.45729, 0.45049, 0.49121, 0.42522, 0.49687, 0.39121, 0.42478, 0.35428, 0.4163, 0.3572, 0.45729, 0.33485, 0.45729, 0.31347, 0.40923, 0.15855, 0.39933, 0.0293, 0.36965, 0.00403, 0.34138, 0.0157, 0.25939, 0.0672, 0.10531, 0.14009, 0.03181], "triangles": [27, 25, 26, 22, 23, 24, 22, 27, 21, 32, 34, 38, 34, 32, 33, 34, 36, 38, 36, 34, 35, 32, 38, 30, 32, 30, 31, 36, 37, 38, 25, 27, 22, 30, 38, 21, 28, 30, 27, 29, 30, 28, 30, 21, 27, 25, 22, 24, 21, 38, 20, 38, 39, 20, 39, 19, 20, 40, 19, 39, 40, 41, 19, 41, 42, 46, 13, 11, 12, 17, 14, 15, 10, 13, 53, 14, 50, 13, 16, 17, 15, 53, 13, 50, 14, 17, 50, 52, 53, 50, 50, 17, 18, 51, 52, 50, 49, 50, 18, 46, 49, 18, 46, 18, 19, 48, 49, 46, 47, 48, 46, 44, 46, 43, 45, 46, 44, 19, 41, 46, 42, 43, 46, 4, 2, 3, 1, 2, 4, 8, 5, 6, 8, 6, 7, 63, 64, 66, 65, 66, 64, 63, 68, 0, 63, 0, 1, 67, 68, 63, 63, 66, 67, 62, 63, 1, 62, 1, 4, 59, 62, 4, 58, 59, 4, 5, 58, 4, 55, 58, 5, 13, 10, 11, 61, 62, 59, 61, 59, 60, 8, 55, 5, 9, 54, 55, 9, 55, 8, 57, 58, 55, 57, 55, 56, 10, 53, 54, 9, 10, 54], "vertices": [1, 15, 6.35, 12.33, 1, 1, 15, 25.56, 11.32, 1, 1, 15, 28.56, 13.73, 1, 1, 15, 31.44, 13.48, 1, 1, 15, 30.6, 10.33, 1, 1, 15, 38.02, 9.15, 1, 1, 15, 42.38, 11.56, 1, 1, 15, 45.02, 11.15, 1, 1, 15, 43.22, 7.24, 1, 1, 15, 45.71, 6.69, 1, 1, 15, 50.42, 7.17, 1, 2, 15, 55.92, 4.56, 0.6737, 16, -3.81, 6.19, 0.3263, 2, 15, 58.31, -0.16, 0.15749, 16, 1.44, 6.89, 0.84251, 2, 15, 57.84, -2.24, 0.04843, 16, 3.24, 5.75, 0.95157, 1, 16, 8.78, 6.08, 1, 1, 16, 11.22, 7.9, 1, 2, 16, 13.36, 7.77, 0.99944, 17, 13.25, 11.53, 0.00056, 2, 16, 13.38, 4.97, 0.98476, 17, 10.87, 13.01, 0.01524, 2, 16, 16.65, 4.87, 0.84068, 17, 9.04, 10.31, 0.15932, 2, 16, 18.57, 5.79, 0.55991, 17, 8.78, 8.2, 0.44009, 2, 16, 15.75, 8.66, 0.13738, 17, 12.72, 9.03, 0.86262, 2, 16, 10.09, 10.96, 0.0126, 17, 17.69, 12.58, 0.9874, 2, 16, 6.47, 9.77, 0.0013, 17, 18.63, 16.27, 0.9987, 1, 17, 21.82, 21.33, 1, 1, 17, 27.28, 21.88, 1, 1, 17, 31.83, 18.14, 1, 1, 17, 34, 13.01, 1, 1, 17, 32.39, 10.95, 1, 1, 17, 34.62, 8.77, 1, 1, 17, 35.52, 6.04, 1, 1, 17, 34.53, 4.34, 1, 1, 17, 37.1, 2.49, 1, 1, 17, 35.33, -1.41, 1, 1, 17, 35.6, -3.91, 1, 1, 17, 34.8, -6.44, 1, 1, 17, 34.96, -8.62, 1, 1, 17, 32.16, -12.81, 1, 1, 17, 28.11, -13.8, 1, 1, 17, 24.45, -11.86, 1, 1, 17, 12.75, -13.08, 1, 1, 17, 5.96, -11.97, 1, 2, 16, 36.92, 7.88, 0.08281, 17, 0.71, -8.41, 0.91719, 2, 16, 39.2, 3.73, 0.38803, 17, -4.02, -8.11, 0.61197, 2, 16, 39.02, -2.52, 0.82643, 17, -9.2, -4.6, 0.17357, 2, 16, 35.38, -7.17, 0.99448, 17, -11.17, 0.96, 0.00552, 1, 16, 30.24, -8.83, 1, 1, 16, 24.55, -7, 1, 1, 16, 25.79, -9.69, 1, 1, 16, 23.38, -11.04, 1, 2, 15, 49.22, -23.33, 0.0001, 16, 20.3, -9.36, 0.9999, 2, 15, 47.32, -17.38, 0.02718, 16, 14.05, -9.18, 0.97282, 2, 15, 44.7, -20.45, 0.02053, 16, 16.09, -12.67, 0.97947, 2, 15, 43, -18.9, 0.02139, 16, 14.06, -13.76, 0.97861, 2, 15, 45.5, -13.04, 0.15042, 16, 9.36, -9.47, 0.84958, 2, 15, 43.09, -10.05, 0.50199, 16, 5.73, -10.75, 0.49801, 2, 15, 39.41, -9.06, 0.85615, 16, 3.58, -13.89, 0.14385, 2, 15, 40.12, -11.19, 0.9021, 16, 5.82, -13.93, 0.0979, 2, 15, 37.89, -12.21, 0.91265, 16, 6.05, -16.37, 0.08735, 2, 15, 33.44, -8.53, 0.9758, 16, 1.11, -19.36, 0.0242, 2, 15, 29.88, -8.97, 0.99837, 16, 0.35, -22.86, 0.00163, 1, 15, 30.89, -11.49, 1, 1, 15, 28.83, -12.08, 1, 1, 15, 25.98, -9.6, 1, 1, 15, 11.5, -13.05, 1, 1, 15, -0.97, -14.58, 1, 1, 15, -3.81, -13.45, 1, 1, 15, -4.22, -7.94, 1, 1, 15, -2.26, 3.2, 1, 1, 15, 3.13, 9.78, 1], "hull": 69, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 0, 136], "width": 96, "height": 66}}}}], "animations": {"jump": {"bones": {"bone": {"translate": [{}, {"time": 0.1, "y": -14.73}, {"time": 0.1333, "y": 4.9}, {"time": 0.3667, "y": 138.35}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.3667, "angle": -9.04}, {"time": 0.6, "angle": 5.13}, {"time": 0.6667}]}, "bone3": {"rotate": [{}, {"time": 0.4, "angle": -9.04}, {"time": 0.6, "angle": 5.13}, {"time": 0.6667}]}, "bone4": {"rotate": [{}, {"time": 0.4333, "angle": -9.04}, {"time": 0.6, "angle": 5.13}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.5, "angle": -9.04}, {"time": 0.5667, "angle": 5.13}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.3667, "angle": -9.04}, {"time": 0.6, "angle": 8.43}, {"time": 0.6667}]}, "bone7": {"rotate": [{}, {"time": 0.4, "angle": 8.96}, {"time": 0.5333, "angle": -6.15}, {"time": 0.6667}]}, "bone8": {"rotate": [{}, {"time": 0.4333, "angle": 8.96}, {"time": 0.6333, "angle": -6.15}, {"time": 0.6667}]}, "bone9": {"rotate": [{}, {"time": 0.4667, "angle": 8.96}, {"time": 0.6333, "angle": -6.15}, {"time": 0.6667}]}, "bone10": {"rotate": [{}, {"time": 0.5, "angle": 8.96}, {"time": 0.6333, "angle": -6.15}, {"time": 0.6667}]}, "bone11": {"rotate": [{}, {"time": 0.4333, "angle": 4.97}, {"time": 0.6, "angle": -13.81}, {"time": 0.6667}]}, "bone12": {"rotate": [{"angle": 0.83}, {"time": 0.3667, "angle": -73.98}, {"time": 0.5, "angle": 3.32}, {"time": 0.6667, "angle": 0.83}], "translate": [{}, {"time": 0.3667, "x": -6.94, "y": 9.25}, {"time": 0.6667}]}, "bone13": {"rotate": [{"angle": 6.11}, {"time": 0.1667, "angle": 45.85}, {"time": 0.3667, "angle": -56.39}, {"time": 0.6667, "angle": 6.11}]}, "bone14": {"rotate": [{"angle": 3.02}, {"time": 0.1667, "angle": 28.4}, {"time": 0.3667, "angle": 45.82}, {"time": 0.6667, "angle": 3.02}]}, "bone15": {"rotate": [{"angle": -6.81}, {"time": 0.1667, "angle": 12.22}, {"time": 0.3667, "angle": 63.72}, {"time": 0.5, "angle": -15.17}, {"time": 0.6667, "angle": -6.81}], "translate": [{}, {"time": 0.3667, "x": -3.23, "y": -6.02}, {"time": 0.6667}]}, "bone16": {"rotate": [{"angle": -4.36}, {"time": 0.1667, "angle": -39.82}, {"time": 0.3667, "angle": 49.33}, {"time": 0.6667, "angle": -4.36}]}, "bone17": {"rotate": [{"angle": 3.03}, {"time": 0.1667, "angle": -5.95}, {"time": 0.3667, "angle": -50.23}, {"time": 0.6667, "angle": 3.03}]}}, "ik": {"1": [{"time": 0.1}, {"time": 0.1333, "mix": 0}], "2": [{"time": 0.1}, {"time": 0.1333, "mix": 0}], "3": [{"time": 0.1, "bendPositive": false}, {"time": 0.1333, "mix": 0, "bendPositive": false}], "4": [{"time": 0.1}, {"time": 0.1333, "mix": 0}]}}, "run": {"bones": {"bone": {"rotate": [{}, {"time": 0.2, "angle": 7.61}, {"time": 0.3333}, {"time": 0.5, "angle": -16.15}, {"time": 0.6667}], "translate": [{}, {"time": 0.2, "y": -8.86}, {"time": 0.3333}, {"time": 0.5, "y": -10.63}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.2, "angle": -12.94}, {"time": 0.5333, "angle": 12.79}, {"time": 0.6667}]}, "bone3": {"rotate": [{}, {"time": 0.2333, "angle": -12.94}, {"time": 0.5667, "angle": 12.79}, {"time": 0.6667}]}, "bone4": {"rotate": [{}, {"time": 0.2667, "angle": -12.94}, {"time": 0.5667, "angle": 12.79}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.3, "angle": -12.94}, {"time": 0.6, "angle": 12.79}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.3333, "angle": -12.94}, {"time": 0.6333, "angle": 12.79}, {"time": 0.6667}]}, "bone7": {"rotate": [{}, {"time": 0.2, "angle": -9.59}, {"time": 0.4667, "angle": 13.02}, {"time": 0.6667}]}, "bone8": {"rotate": [{}, {"time": 0.2, "angle": -21.67}, {"time": 0.3333}, {"time": 0.4333, "angle": 8.66}, {"time": 0.6667}]}, "bone9": {"rotate": [{}, {"time": 0.2333, "angle": -12.83}, {"time": 0.4333, "angle": 0.21}, {"time": 0.6667}]}, "bone10": {"rotate": [{}, {"time": 0.2333, "angle": -17.48}, {"time": 0.4667, "angle": 23.53}, {"time": 0.5333, "angle": 12.89}, {"time": 0.6667}]}, "bone11": {"rotate": [{}, {"time": 0.2333, "angle": -22.52}, {"time": 0.5, "angle": 12.2}, {"time": 0.6667}]}, "bone12": {"rotate": [{"angle": 0.83}]}, "bone13": {"rotate": [{"angle": 6.11}]}, "bone14": {"rotate": [{"angle": 3.02}]}, "bone15": {"rotate": [{"angle": -6.81}]}, "bone16": {"rotate": [{"angle": -4.36}]}, "bone17": {"rotate": [{"angle": 3.03}]}, "1": {"translate": [{"time": 0.3333}, {"time": 0.5, "x": -28.87, "y": 51.43}, {"time": 0.6667}]}, "2": {"translate": [{"time": 0.3333}, {"time": 0.5, "x": -28.87, "y": 68.03}, {"time": 0.6667}]}, "3": {"translate": [{}, {"time": 0.2, "x": 36.22, "y": 48.57}, {"time": 0.3333}]}, "4": {"translate": [{}, {"time": 0.2, "x": 36.22, "y": 66.99}, {"time": 0.3333}]}}}, "standBy": {"bones": {"bone": {"translate": [{}, {"time": 0.3333, "y": -11.88}, {"time": 0.6667}]}, "bone2": {"rotate": [{}, {"time": 0.2333, "angle": -2.41}, {"time": 0.6667}]}, "bone4": {"rotate": [{}, {"time": 0.2333, "angle": -8.42}, {"time": 0.6667}]}, "bone5": {"rotate": [{}, {"time": 0.2667, "angle": -4.88}, {"time": 0.6667}]}, "bone6": {"rotate": [{}, {"time": 0.3, "angle": -10.73}, {"time": 0.6667}]}, "bone8": {"rotate": [{}, {"time": 0.2333, "angle": 4.31}, {"time": 0.6667}]}, "bone9": {"rotate": [{}, {"time": 0.2667, "angle": 11.48}, {"time": 0.6667}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "angle": 2.05}, {"time": 0.6667}]}, "bone11": {"rotate": [{}, {"time": 0.4, "angle": 10.21}, {"time": 0.6667}]}}}}}