{"skeleton": {"hash": "vi5OL7NZKPU", "spine": "3.8-from-4.0.09", "x": -114.2, "y": -1.7, "width": 224, "height": 359.68, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/火冒三丈"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 49.59, "rotation": 95.02, "x": -0.73, "y": 14.27}, {"name": "bone2", "parent": "bone", "length": 65.86, "rotation": 5.92, "x": 70.6, "y": 36.82}, {"name": "bone3", "parent": "bone", "length": 69.64, "rotation": -17.42, "x": 59.55, "y": -47.02}, {"name": "bone4", "parent": "bone2", "length": 119.26, "rotation": -10.8, "x": 116.4, "y": -68.8}], "slots": [{"name": "s", "bone": "bone4", "attachment": "s"}, {"name": "x", "bone": "bone", "attachment": "x"}], "skins": [{"name": "default", "attachments": {"s": {"s": {"type": "mesh", "uvs": [0, 0.43683, 0.01794, 0.51963, 0.06067, 0.59911, 0.1333, 0.66314, 0.24725, 0.72607, 0.26007, 0.81217, 0.26861, 0.8751, 0.28997, 0.95458, 0.30991, 0.99432, 0.3384, 0.99101, 0.32843, 0.94685, 0.32273, 0.9038, 0.29994, 0.78899, 0.28428, 0.72165, 0.40249, 0.69626, 0.52925, 0.69074, 0.64177, 0.70068, 0.73862, 0.72386, 0.71298, 0.82652, 0.68165, 0.93581, 0.66741, 1, 0.70729, 1, 0.74147, 0.88172, 0.76853, 0.79451, 0.78135, 0.73711, 0.90669, 0.65873, 0.98787, 0.52957, 1, 0.44677, 1, 0.34004, 1, 0.25353, 0.95084, 0.14866, 0.82123, 0.0482, 0.67026, 0.01177, 0.52498, 0, 0.36404, 0, 0.186, 0.07138, 0.08345, 0.16301, 0.00654, 0.26899, 0, 0.32198], "triangles": [36, 15, 14, 15, 36, 35, 27, 16, 15, 27, 15, 35, 32, 27, 33, 33, 27, 35, 35, 34, 33, 0, 36, 14, 32, 31, 27, 31, 28, 27, 37, 36, 38, 31, 30, 28, 36, 0, 38, 30, 29, 28, 20, 19, 21, 21, 19, 22, 19, 18, 22, 22, 18, 23, 18, 17, 23, 23, 17, 24, 24, 17, 25, 17, 16, 25, 25, 16, 27, 26, 25, 27, 9, 8, 10, 8, 7, 10, 7, 11, 10, 7, 6, 11, 6, 12, 11, 6, 5, 12, 12, 4, 13, 12, 5, 4, 4, 3, 13, 13, 3, 14, 0, 14, 3, 1, 0, 3, 3, 2, 1], "vertices": [2, 2, 161.03, 40.77, 0.45604, 4, 23.31, 115.99, 0.54396, 2, 2, 136.77, 41.37, 0.58363, 4, -0.63, 112.03, 0.41637, 2, 2, 112.4, 36.33, 0.71404, 4, -23.62, 102.52, 0.28596, 2, 2, 91.15, 23.87, 0.83736, 4, -42.17, 86.3, 0.16264, 3, 2, 68.44, 2.26, 0.98978, 3, 47.39, 112.5, 2e-05, 4, -60.42, 60.82, 0.0102, 1, 2, 43.47, 4.17, 1, 1, 2, 25.25, 5.74, 1, 2, 2, 1.79, 5.41, 1, 3, -15.06, 88.98, 0, 1, 2, -10.34, 3.2, 1, 1, 2, -10.61, -3.25, 1, 3, 2, 2.34, -3.48, 1, 3, -11.03, 81.05, 0, 4, -124.27, 42.8, 0, 3, 2, 14.8, -4.59, 1, 3, 0.85, 84.96, 0, 4, -111.83, 44.04, 0, 3, 2, 48.35, -5.87, 1, 3, 32.16, 97.07, 0, 4, -78.63, 49.06, 0, 3, 2, 68.12, -6.12, 0.95453, 3, 50.42, 104.67, 0.00107, 4, -59.16, 52.52, 0.0444, 3, 2, 70.3, -33.52, 0.52082, 3, 63.27, 80.39, 0.03869, 4, -51.89, 26.02, 0.44049, 3, 2, 66.47, -61.7, 0.1604, 3, 70.92, 52.99, 0.22494, 4, -50.37, -2.37, 0.61465, 3, 2, 58.87, -85.9, 0.02936, 3, 73.52, 27.76, 0.58691, 4, -53.31, -27.57, 0.38373, 3, 2, 48.17, -105.92, 0.00107, 3, 71.64, 5.13, 0.9584, 4, -60.06, -49.25, 0.04053, 1, 3, 41.42, 4.38, 1, 2, 3, 9.07, 4.45, 1, 4, -121.28, -36.33, 0, 1, 3, -9.73, 3.59, 1, 1, 3, -7.82, -5.14, 1, 1, 3, 27.21, -5.28, 1, 1, 3, 53.13, -5.79, 1, 2, 3, 69.95, -5.03, 0.99745, 4, -63.91, -58.81, 0.00255, 2, 3, 98.1, -27.59, 0.81989, 4, -41.33, -86.94, 0.18011, 2, 3, 138.46, -37.35, 0.55665, 4, -4.05, -105.22, 0.44335, 2, 3, 162.41, -34.86, 0.41868, 4, 19.87, -108, 0.58132, 2, 3, 192.54, -28.24, 0.28068, 4, 50.71, -108.08, 0.71932, 2, 3, 216.96, -22.88, 0.16883, 4, 75.71, -108.14, 0.83117, 2, 3, 244.2, -5.62, 0.09637, 4, 106.05, -97.21, 0.90363, 2, 3, 266.32, 28.97, 0.03661, 4, 135.16, -68.25, 0.96339, 2, 3, 269.35, 64.26, 0.00906, 4, 145.77, -34.46, 0.99094, 3, 2, 262.65, -98.66, 0.00017, 3, 265.69, 96.77, 0.00022, 4, 149.25, -1.93, 0.9996, 2, 2, 269.49, -63.26, 0.01113, 4, 149.35, 34.12, 0.98887, 2, 2, 256.81, -20.19, 0.06375, 4, 128.82, 74.06, 0.93625, 2, 2, 235.17, 7.39, 0.14193, 4, 102.4, 97.09, 0.85807, 2, 2, 208.37, 30.12, 0.24973, 4, 71.81, 114.4, 0.75027, 2, 2, 193.62, 34.46, 0.30028, 4, 56.5, 115.91, 0.69972], "hull": 39, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76, 54, 56, 56, 58], "width": 224, "height": 289}}, "x": {"x": {"type": "mesh", "uvs": [0.18586, 0.00877, 0.15932, 0.10362, 0.16219, 0.1398, 0.12038, 0.16222, 0.08437, 0.22951, 0.08501, 0.2968, 0.09723, 0.33221, 0.12231, 0.35464, 0.0604, 0.47828, 0.01927, 0.62209, 0, 0.74074, 0, 0.86118, 0.02808, 0.93129, 0.07607, 0.95825, 0.12994, 0.93488, 0.17401, 0.83601, 0.19948, 0.74074, 0.20535, 0.83242, 0.24355, 0.9205, 0.30819, 0.97982, 0.76949, 0.98701, 0.80083, 0.90252, 0.81454, 0.73534, 0.82825, 0.90432, 0.8831, 0.996, 0.9497, 0.996, 0.99475, 0.84859, 1, 0.64906, 0.98692, 0.5394, 0.92717, 0.43334, 0.97516, 0.35065, 0.9732, 0.24818, 0.93599, 0.18527, 0.89779, 0.19425, 0.86841, 0.20864, 0.86645, 0.13313, 0.83217, 0.06842, 0.74598, 0.04505, 0.55598, 0.05044, 0.39732, 0.03426, 0.24845, 0], "triangles": [40, 2, 1, 7, 6, 5, 5, 4, 7, 7, 3, 2, 7, 4, 3, 33, 30, 29, 31, 33, 32, 30, 33, 31, 34, 33, 29, 37, 34, 22, 36, 34, 37, 35, 34, 36, 22, 34, 29, 22, 29, 28, 22, 28, 27, 0, 40, 1, 39, 2, 40, 2, 39, 7, 16, 7, 39, 8, 7, 16, 9, 8, 16, 15, 10, 9, 16, 39, 38, 18, 17, 16, 16, 15, 9, 26, 22, 27, 11, 13, 12, 37, 22, 38, 26, 23, 22, 16, 19, 18, 13, 11, 10, 15, 14, 10, 10, 14, 13, 38, 19, 16, 21, 20, 22, 22, 19, 38, 20, 19, 22, 24, 23, 26, 25, 24, 26], "vertices": [65.94, 41.9, 58.81, 46.39, 55.93, 46.23, 54.69, 52.42, 49.85, 58.09, 44.55, 58.46, 41.61, 56.94, 39.52, 53.47, 30.58, 63.27, 19.79, 70.21, 10.69, 73.81, 1.22, 74.65, -4.66, 71.07, -7.39, 64.33, -6.24, 56.39, 0.99, 49.34, 8.16, 45, 0.87, 44.78, -6.55, 39.88, -12.04, 30.95, -18.46, -35.63, -12.21, -40.74, 0.77, -43.88, -12.7, -44.69, -20.61, -51.98, -21.45, -61.6, -10.43, -69.13, 5.21, -71.27, 14.01, -70.13, 23.11, -62.24, 29.01, -69.74, 37.1, -70.17, 42.52, -65.23, 42.3, -59.65, 41.54, -55.3, 47.51, -55.54, 53.04, -51.04, 55.97, -38.75, 57.96, -11.27, 61.24, 11.53, 65.83, 32.8], "hull": 41, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 0, 80], "width": 145, "height": 79}}}}], "animations": {"rqq": {"bones": {"bone2": {"rotate": [{}, {"time": 0.3333, "angle": 1.06}, {"time": 1}]}, "bone3": {"rotate": [{}, {"time": 0.3333, "angle": -2.04}, {"time": 1}]}, "bone": {"rotate": [{}, {"time": 0.4333, "angle": 0.52}, {"time": 1}]}}, "deform": {"default": {"s": {"s": [{}, {"time": 0.4667, "vertices": [0.52758, 2.72808, 0.0071, 2.77862, 0.52758, 2.72808, 0.0071, 2.77862, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47708, -2.17111, -0.00568, -2.22289, 1.54772, -5.77084, 0, -5.97476, 0.47708, -2.17111, -0.00568, -2.22289, 0.47708, -2.17111, -0.00568, -2.22289, 0.47708, -2.17111, -0.00568, -2.22289, 3.25662, 0.71558, 3.33432, -0.00853, 3.27366, -0.63312, 3.25662, 0.71558, 3.33432, -0.00853, 2.72804, -0.5276, 2.77859, -0.00711, 0.52758, 2.72808, 0.0071, 2.77862, 0.52758, 2.72808, 0.0071, 2.77862, 0.52758, 2.72808, 0.0071, 2.77862, 0.52758, 2.72808, 0.0071, 2.77862]}, {"time": 1}]}, "x": {"x": [{}, {"time": 0.4333, "offset": 7, "vertices": [-2.86454, 0, -2.86454, 0, -2.86454, 0, -2.86454, 0, 0, 4.29681, 0, 4.29681, 0, 5.53613, 0, 5.16422, 0, 3.37388, 0, 5.16422, 0, 5.16422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.07422, 0, 4.65486, 0, 6.36254, 0, 2.78186, 0, 2.78186, 0, 1.07419, 2.17603, 1.07419, 2.17603, 1.07419, 0, 1.07419, 0, 1.07419, 0, 1.07419]}, {"time": 1}]}}}}}}