
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, UITransform, sp, EventTouch, Sprite, SpriteFrame} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level5')
export class Level5 extends LevelDialogBase {

    @property(Node)
    FishNode:Node = null
    @property(Node)
    FishTankNode:Node = null
    @property(Node)
    WaterLabel:Node = null

    isTouchWater:boolean = false
    waterPosition:Vec3 = null

    isHasWater:boolean = false
    isFishInTank:boolean = false

    start(){
        super.start()
        //鱼
        this.FishNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onFishContact, this);
        //缸
        this.FishTankNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onFishTankContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        // this.waterPosition = this.WaterLabel.position  //设置水的初始位置
    }

    checkWin(){
        if(this.isHasWater){
            this.FishTankNode.getChildByName("WaterTank").active = false
            this.FishTankNode.getChildByName("WaterTank2").active = true
            this.FishTankNode.getChildByName("FishNode2").getComponent(sp.Skeleton).setAnimation(1,"water", true)
        }
        if(this.isHasWater && this.isFishInTank){
            this.showKey()
        }
    }

    onFishContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            //鱼进入鱼缸
            if(otherCollider.node.name=="FishTankNode"){
                tween(this.FishTankNode).delay(0.05).call(()=>{
                    this.FishNode.active = false
                    this.FishTankNode.getChildByName("FishNode2").active=true
                    this.isFishInTank = true
                    this.checkWin()
                }).start()
            }
        })
    }

    onFishTankContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){

    }

    touchStart(event:EventTouch){
        super.touchStart(event)

        let pos = event.getUILocation()
        const waterNode = this.WaterLabel.getComponent(UITransform)!;
        let wordPos = waterNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let dis = Public.GetDistance2D(wordPos, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(dis<60){
            this.WaterLabel.getChildByName("WaterLabel").active=true
            this.isTouchWater = true
            console.log("self, touch water begin-------", this.waterPosition)
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.isTouchWater==true){
            let pos = this.WaterLabel.position
            console.log("event---touch water move", event.getDeltaX(), event.getDeltaY())
            let ePos = event.getUIDelta()
            this.WaterLabel.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }

    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.isTouchWater==true){
            console.log("event---touch water end", this.waterPosition)
            this.isTouchWater = false
            //判断和鱼缸的距离，进入鱼缸
            let waterLabelPos = this.WaterLabel.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let fishTankPos = this.FishTankNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(fishTankPos, waterLabelPos)
            if(dis<60){
                //水进入鱼缸

                this.WaterLabel.active = false
                this.isHasWater = true
                this.checkWin()
            }
        }
    }


}
