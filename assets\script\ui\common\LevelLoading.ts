// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import { _decorator, Component , assetManager, tween} from "cc";
import {dialogBase} from "../../framework/dialogBase";
import {Public} from "../../game/Public";
import {uiManager} from "../../framework/uiManager";
import {Constants} from "../../game/Constants";
import {AdCtl} from "../../channel/AdCtl";
import {resourceUtil} from "../../framework/resourceUtil";
const { ccclass, property } = _decorator;

@ccclass("LevelLoading")
export class LevelLoading extends dialogBase {

    show(params){
        super.show(params)

        let levelName = Public.GetRightLevelPath()
        let bundleName = levelName.split("|")[0]
        // let pathName = levelName.split("|")[1]
        let time1 = new Date().getTime()
        this.loadLevel(bundleName,  ()=>{
            let go = ()=>{
                uiManager.instance.hideDialog(Constants.Dialogs.levelLoading)
                console.log("------xxxxxxxx levelName----", levelName, timeDelta)
                uiManager.instance.showDialog(levelName)
            }
            //如果加载时间小于0.8秒，那么就等0.8秒，如果超过0.8秒那么就不等。
            let timeDelta = new Date().getTime() - time1
            if(timeDelta<800){
                this.delayTime(0.8).then(()=>{
                    go()
                })
            }else{
                go()
            }
        })
    }

    async loadLevel(name:string, cb:Function){
        await this.loadBundle('levelCommon')
        console.log("bundle levelCommon 加载完毕", )
        // await AdCtl.instance.InitCreateBannerAd()
        // console.log("ad banner广告加载完毕")
        await AdCtl.instance.InitCreateCustomLevelUI()
        console.log("ad 关卡的，加载完毕")
        await this.loadBundle(name)
        console.log("bundle 加载完毕", name)

        let arrPreload = Constants.PrefabsLevel
        let cur = 0;
        for(let i=0; i<arrPreload.length; i++){
            cur++;
            await resourceUtil.getUIPrefabResAsync(arrPreload[i])
        }

        cb && cb()
    }

    async loadBundle(name){
        return new Promise(resolve=>{
            if(Public.IsLoadBundle(name)==true){
                console.log("bundle 已经加载了，不需要重复加载", name)
                resolve()
                return
            }
            assetManager.loadBundle(name, (err,bundle)=>{
                resolve(bundle);
                if (err) {
                    // this.showSubPackageError();
                    return console.error(err);
                }
                // if (err) {
                //     console.log('加载bundle错误:',name,err)
                //     return;
                // }
                // console.log("加载完成")
            })
        })
    }
}
