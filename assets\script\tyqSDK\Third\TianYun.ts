import { sys } from "cc";

export default class TianYun {

    static getInstance () {

        if (this._instance == null) {
            this._instance = new this();
        }
        return this._instance;
    }
    static _instance: TianYun = null;

    constructor () {

    }

    private _AndroidClass = "org/cocos2dx/javascript/TianYun";
    private _IosClass = "";
    public sendToTAQ (type: number = 1) {
        if (sys.os === sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this._AndroidClass, "sendToTAQ", "(I)V", type);
        }
    }
    public event (event_id: string) {
        if (sys.os === sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this._AndroidClass, "event", "(Ljava/lang/String;)V", event_id);
        }
    }

    public isShowIllegal () {
        if (sys.os === sys.OS.ANDROID) {
            return jsb.reflection.callStaticMethod(this._AndroidClass, "isShowIllegal", "()Z");
        }
    }
    public startGame () {
        if (sys.os === sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this._AndroidClass, "startGame", "()V");
        }
    }
    public endGame (isWin: boolean) {
        if (sys.os === sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this._AndroidClass, "endGame", "(Z)V", isWin);
        }
    }
    public onRewardedVideoAdPlayStart () {
        if (sys.os === sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this._AndroidClass, "onRewardedVideoAdPlayStart", "()V");
        }
    }
}
