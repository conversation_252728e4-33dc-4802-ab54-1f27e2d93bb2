//额外一些方法


import { sys } from "cc";
import { JSB } from "cc/env";

export default class tyqExtend {


    public static getOAID() {
        let oaid = "";
        if (JSB && sys.os == sys.OS.ANDROID) {
            oaid = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/TyqHelper", "getOAID", "()Ljava/lang/String;");
        }
        return oaid;
    }
    public static getPhoneModel() {
        let model = "";
        if (JSB && sys.os == sys.OS.ANDROID) {
            model = jsb.reflection.callStaticMethod("org/cocos2dx/javascript/TyqHelper", "getPhoneModel", "()Ljava/lang/String;");
        }
        return model;
    }
    public static downloadApk(url: string) {
        if (JSB && sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/TyqHelper", "downloadAPK", "(Ljava/lang/String;)V", url);
        }
    }
    public static dartClick(url: string, appid: string, openid: string, ads_id: number) {
        if (window.jsb && sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/TyqHelper", "dartClick", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;I)V", url, appid, openid, ads_id);
        }
    }
    public static copy(text: string) {
        if (window.jsb && sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod("org/cocos2dx/javascript/TyqHelper", "copyToClipboard", "(Ljava/lang/String;)V", text);
        }
    }
}