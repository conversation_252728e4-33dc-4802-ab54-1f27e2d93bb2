import { sys, Node, resources, instantiate, find, Prefab } from "cc";
import TyqEventMgr from "../tyq-event-mgr";
import MiFeedAd from "../view/MiFeedAd";


export default class MiAdMgr {
    public launchTime = 0;
    private androidClass = "org/cocos2dx/javascript/mi/MiAdMgr";
    private static _instance: MiAdMgr = null;
    public static getInstance() {
        if (this._instance == null) {
            this._instance = new this();
        }
        return this._instance;
    }
    public static get ins() {
        return this.getInstance();
    }
    public startGame() {
        this.launchTime = Date.now();
    }
    public getLaunchTime() {
        return (Date.now() - this.launchTime) / 1000;
    }

    //将要展现banner的节点
    private _showBannerNode: Node = null;
    //banner关闭回调
    private _bannerCloseCb: Function = null;

    //激励广告成功回调
    private rewardSuccess: Function = null;
    //激励广告取消回调
    private rewardCancel: Function = null;
    //激励广告加载播放失败回调
    private rewardFailCb: Function = null;
    //自渲染广告点击回调
    private feedClickCb: Function = null;

    public showBanner(node: Node) {
        this._showBannerNode = node;
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "showBanner", "()V");
        } else {
            console.error("please run on android");
        }

    }
    public destroyBanner() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "destroyBanner", "()V");
            this._showBannerNode = null;
        } else {
            console.error("please run on android");
        }
    }
    // public loadInterstitial() {
    //     if (sys.os == sys.OS_ANDROID) {
    //         jsb.reflection.callStaticMethod(this.androidClass, "loadInterstitial", "()V");
    //     } else {
    //         console.error("please run on android");
    //     }
    // }
    public showInterstitial() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "showInterstitial", "()V");
        } else {
            console.error("please run on android");
        }
    }
    public destroyInterstitial() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "destroyInterstitial", "()V");
        } else {
            console.error("please run on android");
        }
    }

    // public loadReward() {
    //     if (sys.os == sys.OS_ANDROID) {
    //         jsb.reflection.callStaticMethod(this.androidClass, "loadReward", "()V");
    //     } else {
    //         console.error("please run on android");
    //     }
    // }
    public showReward(des, cb: Function, cancel?: Function, fail?: Function) {
        // tyqSDK.collectClickEvent("广告" + des);
        this.rewardSuccess = cb;
        this.rewardCancel = cancel;
        this.rewardFailCb = fail;
        if (sys.os == sys.OS.ANDROID) {
            // tyqSDK.collectAdAction(1);
            jsb.reflection.callStaticMethod(this.androidClass, "showReward", "()V");
        } else {
            console.error("please run on android");
        }
    }
    private onRewardShow() {
        // tyqSDK.collectAdAction(2);
        TyqEventMgr.getInstance().onAdShow();
    }
    private onRewardResult(isSuccess: boolean) {
        //这里必须要延迟一帧执行
        setTimeout(() => {
            if (isSuccess) {
                // tyqSDK.collectAdAction(3);
                TyqEventMgr.getInstance().onAdSuccess();
                if (this.rewardSuccess) {
                    this.rewardSuccess();
                    this.rewardSuccess = null;
                }
            } else {
                // tyqSDK.collectAdAction(4);
                TyqEventMgr.getInstance().onAdCancel();
                if (this.rewardCancel) {
                    this.rewardCancel();
                    this.rewardCancel = null;
                }
            }
        }, 0);
    }
    private onRewardLoadFail() {
        console.log("视频加载或播放失败:");
        // tyqSDK.collectClickEvent("视频加载或播放失败");
        TyqEventMgr.ins.sendEvent("视频加载或播放失败");
        if (this.rewardFailCb) {
            this.rewardFailCb();
            this.rewardFailCb = null;
        }
    }
    // public loadNativeAd() {
    //     if (sys.os == sys.OS_ANDROID) {
    //         jsb.reflection.callStaticMethod(this.androidClass, "loadNativeAd", "()V");
    //     } else {
    //         console.error("please run on android");
    //     }
    // }
    public showNativeAd() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "showNativeAd", "()V");
        } else {
            console.error("please run on android");
        }
    }

    private onBannerLoad() {
        if (this._showBannerNode && this._showBannerNode.active == true) {
            this.showBanner(this._showBannerNode);
        }
    }
    public setBannerCloseCb(fun: Function) {
        this._bannerCloseCb = fun;
    }
    private onBannerClose() {
        console.log("关闭banner");
        if (this._bannerCloseCb) {
            this._bannerCloseCb();
            this._bannerCloseCb = null;
        }
        this._showBannerNode = null;
    }
    public showFeedAd() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "showFeedAd", "()V");
        } else {
            console.error("please run on android");
        }
    }
    public setFeedClickArea(left: number, top: number, w: number, h: number) {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "setFeedClickArea", "(IIII)V", left, top, w, h);
        } else {
            console.error("please run on android");
        }
    }
    public setFeedClickCb(cb: Function) {
        this.feedClickCb = cb;
    }
    private destroyFeed() {
        if (sys.os == sys.OS.ANDROID) {
            jsb.reflection.callStaticMethod(this.androidClass, "destroyFeedAd", "()V");
        } else {
            console.error("please run on android");
        }
    }
    public onCloseFeed() {
        this.destroyFeed();
        this.setFeedClickArea(0, 0, 0, 0);
        this.feedClickCb = null;
    }
    private onFeedLoad(str: string) {

        resources.load("tyqRes/prefab/MiFeedAd", Prefab, (err: Error, prefab: Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = instantiate(prefab);

            find("Canvas").addChild(node);
            node.setSiblingIndex(9999);
            node.getComponent(MiFeedAd).init(str);
        })
    }
    private onFeedClick() {
        if (this.feedClickCb) {
            this.feedClickCb();
            this.feedClickCb = null;
        }
    }
}


window["MiAdMgr"] = MiAdMgr;