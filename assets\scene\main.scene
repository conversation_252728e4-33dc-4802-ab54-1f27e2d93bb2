[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "main", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}], "_active": true, "_components": [], "_prefab": {"__id__": 87}, "autoReleaseAssets": false, "_globals": {"__id__": 88}, "_id": "e96d6ae0-6179-4ac3-8969-a9b662fb0e9c"}, {"__type__": "cc.Node", "_name": "core", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "61pedcRJZHlZoG2orKlqVN"}, {"__type__": "f3d36qkNslPB4TIGcZlp8vd", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "zIndex": 100, "AniNode": null, "_id": "70GSZ61FJLS4fbs1EAfy8I"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 7}, {"__id__": 47}, {"__id__": 52}, {"__id__": 56}, {"__id__": 60}, {"__id__": 81}], "_active": true, "_components": [{"__id__": 83}, {"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 830, "y": 360, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6fBTKAM9BJipWBL6z8kjmb"}, {"__type__": "cc.Node", "_name": "panelRoot", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2bT8VdS6dPZoVQy4PlO5xR"}, {"__type__": "cc.UITransform", "_name": "goldPos<UITransform>", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d0H06O4DZKRJhkgGtvF8mX"}, {"__type__": "cc.Node", "_name": "loading", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 8}, {"__id__": 12}, {"__id__": 15}, {"__id__": 25}, {"__id__": 28}, {"__id__": 31}, {"__id__": 36}], "_active": true, "_components": [{"__id__": 43}, {"__id__": 44}, {"__id__": 45}, {"__id__": 51}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0f9ZiPXf5OU5ORXtxWsnGm"}, {"__type__": "cc.Node", "_name": "BgUnit1", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}, {"__id__": 11}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "4eXwn0vUhGuqrBNfxiaZRh"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1980, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3dlNjhqXdC8I5uyCF6Th/8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "efd66976-d09e-4047-8ab7-df088901132a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "1aTjKZ5R5BgLMNOqlOqBiU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": -160, "_right": -160, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1600, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": "1dBOGpzY9ByIbXtQN1sZz7"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -186.025, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1615kedNlI24Zuz12xP+lz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1297, "height": 558}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "0b0/BJqWpBSao3UtHzKlYq"}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "loop": true, "_premultipliedAlpha": false, "_timeScale": 1, "_useTint": false, "_preCacheMode": 0, "_cacheMode": 0, "_defaultCacheMode": 0, "_debugBones": false, "_debugSlots": false, "_skeletonData": {"__uuid__": "d4c86189-1889-409b-b47c-8711194648d5", "__expectedType__": "sp.SkeletonData"}, "defaultSkin": "default", "defaultAnimation": "animation", "_sockets": [], "_debugMesh": false, "_id": "17u6mxkjdMnpRrEvBYqQdP"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 16}, {"__id__": 20}], "_active": false, "_components": [{"__id__": 23}, {"__id__": 24}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -225.938, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "88J63giQxMErxF/NlSiuUs"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 17}, {"__id__": 18}, {"__id__": 19}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -17.785, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "22GK+334ZIjLO2NZutEHRl"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 22.25, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "44L5p5ILlOmobvKOL8+/4d"}, {"__type__": "4b7cfRS5bdP8qo8rorNgln5", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_id": "f8o4VJ0sVORrPSrfApFI/T"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "0", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "0cNK63n/ZMEqxOBllSS08/"}, {"__type__": "cc.Node", "_name": "percent", "_objFlags": 0, "_parent": {"__id__": 15}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 11.125, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "88psXnmJREmor8bwWxGByj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 35.57, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "84ItVpx3hInZcGTFFyHSZx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 5, "g": 5, "b": 5, "a": 255}, "_string": "%", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 40, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "68cOXy2AxDoKuqFjT0PFrQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 57.82, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e2JEt79llLNqbORjKlZlzO"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "781fff15tMPaxmniW/EdDL"}, {"__type__": "cc.Node", "_name": "words", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -307.611, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ba8hDuqRZNrrdLEdbDNsNJ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 512, "height": 81}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "5ectdK135MapDEyowgjtTv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2528eb02-3e14-48e6-abfa-b2d8147174fa@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e2wL8WtdhGAaKyv1Eq88ef"}, {"__type__": "cc.Node", "_name": "bottle", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -567.886, "y": -206.297, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2bqMxF/1tFp5LQmqsRoC0C"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 139, "height": 89}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "6bkBukkfNKiZIAd2qOI2bz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "046c4dd5-aeb3-4d35-954f-369df5eb9938@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "44x9q1yOFNMoFRqNrhnz6L"}, {"__type__": "cc.Node", "_name": "share", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": false, "_components": [{"__id__": 32}, {"__id__": 33}, {"__id__": 34}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 567.301, "y": 11.876, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "468T1YYJdBQYvv6zFiFqbB"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 136, "height": 119}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1eNAb7zoxGpIGZwZrWWk/y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "71387ac3-26ea-4573-80b0-da05bf635858@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e5ZlEb8ItIDoEkPMrT8Owb"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 35}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "71387ac3-26ea-4573-80b0-da05bf635858@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "15cE5Pb/BCSrvCt1ZAtpsc"}, {"__type__": "cc.ClickEvent", "target": null, "component": "", "_componentId": "", "handler": "", "customEventData": ""}, {"__type__": "cc.Node", "_name": "BeginNode", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 37}], "_active": false, "_components": [{"__id__": 42}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2cYgEwrfBLq5azxrVmj0oz"}, {"__type__": "cc.Node", "_name": "BtnBg1", "_objFlags": 0, "_parent": {"__id__": 36}, "_children": [], "_active": true, "_components": [{"__id__": 38}, {"__id__": 39}, {"__id__": 40}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -264.212, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ce8ccnGr1LxKE2156WRdJy"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 237, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1dqFNxRKdGTIFs77ajJInJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "68a541d1-f441-49fe-99b2-1ac43e78fd6c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d1QP96n69J8I0fpXb+qfgF"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 37}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 41}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "68a541d1-f441-49fe-99b2-1ac43e78fd6c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": "613FkcYjxKcIDwKFNGwIEk"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 4}, "component": "", "_componentId": "daea1EBoy1Ms6PBdAupQWnn", "handler": "OnClickStartGame", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 36}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "94YBNV9IlJmJZ/rfmMJFUD"}, {"__type__": "cc.UITransform", "_name": "loading<UITransform>", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "02ZFZobbJCzpvitmvup0iy"}, {"__type__": "cc.Widget", "_name": "loading<Widget>", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 1, "_lockFlags": 0, "_id": "b88dS6/XZElITXDgrdsXxw"}, {"__type__": "99c5fx0klFHq52oGPdZL7Z5", "_name": "loading<loading>", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "lbProgress": {"__id__": 18}, "lbTips": {"__id__": 46}, "beginNode": {"__id__": 36}, "words": {"__id__": 25}, "bottle": {"__id__": 28}, "_id": "5fScaBtBBILYOEMdXBWATT"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 50}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "正在加载中...", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 32, "_fontSize": 32, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "fb9s+2aIhE84ICOPRmNtXP"}, {"__type__": "cc.Node", "_name": "tips", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": false, "_components": [{"__id__": 48}, {"__id__": 46}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -275.858, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d010hMb1VO3K4xZf4yBYrX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "__prefab": {"__id__": 49}, "_contentSize": {"__type__": "cc.Size", "width": 186.67, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c1ewI1NuhGYZ7OhNU3w+zs"}, {"__type__": "cc.CompPrefabInfo", "fileId": "64daaEp6ZN9ZvTcZ+We0nx"}, {"__type__": "cc.CompPrefabInfo", "fileId": "b1Dxx9oIFAfZ9DqvrGhziM"}, {"__type__": "cc.Sprite", "_name": "loading<Sprite>", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": "0agEABCcpGG6wb5N+KoGrv"}, {"__type__": "cc.Node", "_name": "version", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 54}, {"__id__": 55}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 662.0149999999999, "y": -333.415, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "eb/cAk4I9EkI9Hu3u00UKw"}, {"__type__": "cc.UITransform", "_name": "version<UITransform>", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f59XVsuddDQKKMY9UXLEnC"}, {"__type__": "cc.Label", "_name": "version<Label>", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "当前版本 1.0.0", "_horizontalAlign": 2, "_verticalAlign": 1, "_actualFontSize": 21, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 30, "_overflow": 2, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "9bEQgmqVFLgpZ3mXa9y7l+"}, {"__type__": "cc.Widget", "_name": "version<Widget>", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": null, "_alignFlags": 36, "_target": null, "_left": 0, "_right": 17.98500000000007, "_top": 0, "_bottom": 7.68500000000002, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": "38m/2KyB9LZIYi3Dn2HNRT"}, {"__type__": "cc.Node", "_name": "tip", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": false, "_components": [{"__id__": 57}, {"__id__": 58}, {"__id__": 59}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -314.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3aThmZXo1GyrLV3tQjQzOQ"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 253.37, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "43N5yjgsBAkIvwArBCZUw1"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "小提示：请用 login 场景运行", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "3dvgDoTspObKUZRKYnH2Pi"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 20, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "f9u4Ag0ftF0LFEX30Ymzi8"}, {"__type__": "cc.Node", "_name": "Strength", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [{"__id__": 61}, {"__id__": 64}, {"__id__": 67}, {"__id__": 72}, {"__id__": 76}], "_active": true, "_components": [{"__id__": 79}, {"__id__": 80}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -190, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6fVEOrVl1PE7wuWF5yEHNn"}, {"__type__": "cc.Node", "_name": "heartBg", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 62}, {"__id__": 63}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -479.858, "y": 293.706, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "23IwSKczBBTpFEPjkI9LB+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 150, "height": 42}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1aux2WE39Ly5JDHI8dlumg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@cea8c", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4c1MT1fn5NTr7LamgDoR1Q"}, {"__type__": "cc.Node", "_name": "heart", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 65}, {"__id__": 66}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -549.716, "y": 293.782, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5fhvjS20xEA4vguIq6+Ja/"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f2qgygUONH+43wkP3SJ++M"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@34afd", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "9bZFPWAa9MjIeHc8uB4vRu"}, {"__type__": "cc.Node", "_name": "heartPlus", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 68}, {"__id__": 69}, {"__id__": 70}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -429.08, "y": 295.008, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "0fg5SqTv9CCptpXOp/GeVC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 51}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "27VnFIdVVNUZV3183ur1PE"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@27233", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "30fr04qMxEjrNJsU02ackN"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 71}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@27233", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": null, "_id": "f4XK8Pt/hKA4J7yv9qjkqc"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 4}, "component": "", "_componentId": "daea1EBoy1Ms6PBdAupQWnn", "handler": "OnClickPrompt", "customEventData": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_components": [{"__id__": 73}, {"__id__": 74}, {"__id__": 75}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -490.149, "y": 296.339, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dd3QTfoKpNHqvjcv7ebYlW"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 4, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8fBK9uCtZIBrzBdNA1c+3n"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "42lGAtQ3xLjosQ+SXqCtrW"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "__prefab": null, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": "d9gJdb3TxETYX0ipueAHP8"}, {"__type__": "cc.Node", "_name": "TimePrompt", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": false, "_components": [{"__id__": 77}, {"__id__": 78}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -489.841, "y": 252.851, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "1bG/G7TGBOkZz2EPEqW/oS"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f561c0GLpO4qzjRVGrBsxj"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "__prefab": null, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 36, "_fontSize": 36, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": "bdhLakO45IOZ21YwQIvKK3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "faGA0oRFxFjYN3WX30Smy1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_alignFlags": 9, "_target": {"__id__": 4}, "_left": 590, "_right": 0, "_top": 310, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "62DbXqVgdEpJcWf0Lc2wSn"}, {"__type__": "cc.Node", "_name": "UICamera_Canvas", "_objFlags": 0, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 82}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "abRqurbwFED7D8OtyxehN1"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 520.9205020920502, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 0}, "_depth": 1, "_stencil": 0, "_clearFlags": 0, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 42467328, "_targetTexture": null, "_id": "d8CTEtAAVIyIp8WAuV7UzD"}, {"__type__": "cc.UITransform", "_name": "Canvas<UITransform>", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1660, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "612GBCz0BBJbBUIIcp2JC+"}, {"__type__": "cc.<PERSON>", "_name": "<PERSON><PERSON><Canvas>", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 82}, "_alignCanvasWithScreen": true, "_id": "e26NYcYBlLWqXkVxoUl3Cp"}, {"__type__": "daea1EBoy1Ms6PBdAupQWnn", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": null, "loadingUI": {"__id__": 45}, "lbVersion": {"__id__": 54}, "tip": {"__id__": 56}, "bottle": {"__id__": 28}, "CameraNode": {"__id__": 82}, "StrengthTimeLab": {"__id__": 78}, "CurStrengthLab": {"__id__": 74}, "_id": "80KqwksVdF1oJkB+vLZh1i"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 4}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "15X6rIUw5OH4TgF5X8prqW"}, {"__type__": "cc.PrefabInfo", "fileId": "e96d6ae0-6179-4ac3-8969-a9b662fb0e9c"}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 89}, "shadows": {"__id__": 90}, "_skybox": {"__id__": 91}, "fog": {"__id__": 92}, "octree": {"__id__": 93}}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.8392156862745098, "y": 0.9647058823529412, "z": 1, "w": 2.3437490625}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.8392156862745098, "y": 0.9647058823529412, "z": 1, "w": 2.3437490625}, "_skyIllumHDR": 90000, "_skyIllum": 90000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 1, "y": 1, "z": 1, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 1, "y": 1, "z": 1, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.8392156862745098, "y": 0.9647058823529412, "z": 1, "w": 0.520833125}, "_skyIllumLDR": 3.515625, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 1, "y": 1, "z": 1, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_type": 0, "_enabled": true, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 76}, "_firstSetCSM": false, "_fixedArea": false, "_pcf": 0, "_bias": 0.1, "_normalBias": 0, "_near": 1, "_far": 30, "_shadowDistance": 3.4641016151377544, "_invisibleOcclusionRange": 200, "_orthoSize": 5, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 512, "y": 512}, "_saturation": 0.2980392156862745}, {"__type__": "cc.SkyboxInfo", "_applyDiffuseMap": false, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useIBL": false, "_useHDR": true}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 225, "g": 225, "b": 225, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}]