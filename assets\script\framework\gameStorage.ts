
import { _decorator, Component, Node , sys} from 'cc';
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = gameStorage
 * DateTime = Wed Apr 27 2022 17:55:50 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = gameStorage.ts
 * FileBasenameNoExtension = gameStorage
 * URL = db://assets/script/framework/gameStorage.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('gameStorage')
export class gameStorage extends Component {

    static GameKey = 'MTA';

    public static Key(key:string){
        return this.GameKey + '_' + key;
    }

    static getInt(key:string,def:number = 0){
        let n = localStorage.getItem(this.Key(key));
        if (n !== null && n !== "") {
            return Number(n);
        }
        return def;
    }

    static setInt(key:string,num:number){
        key = this.Key(key);
        sys.localStorage.setItem(key,num+"");
    }

    static setIntMemory(key:string,num:number){
        key = this.Key(key);
        sys.localStorage.setItem(key,num+"");
    }

    static getBoolean(key:string,def:boolean = false){
        let n = sys.localStorage.getItem(this.Key(key));
        if (n !== null&&n !== "") {
            return Number(n) == 1;
        }
        return def;
    }

    static setBoolean(key:string,is:boolean){
        key = this.Key(key);
        let v = is?1:0;
        sys.localStorage.setItem(key,v+"");
    }
    static setBooleanMemory(key:string,is:boolean){
        key = this.Key(key);
        let v = is?1:0;
        sys.localStorage.setItem(key,v+"");
    }

    static getString(key:string,def:string = ''){
        let n = sys.localStorage.getItem(this.Key(key));
        if (n !== null&& n !== "") {
            return n;
        }
        return def;
    }

    static setString(key:string,num:string){
        key = this.Key(key);
        sys.localStorage.setItem(key,num);
        if (key == this.GameKey+'login_account') {
            return;
        }
    }

    static getObject(key:string,def:any = null){
        let n = sys.localStorage.getItem(this.Key(key));
        if (n !== null&& n !== "") {
            try {
                let json = JSON.parse(n);
                return json || def;
            } catch (error) {
                return def;
            }

        }
        return def;
    }

    static setObject(key:string,obj:any){
        key = this.Key(key);
        let v = JSON.stringify(obj);
        localStorage.setItem(key,v);
    }

    static removeItem(key:string){
        key = this.Key(key);
        sys.localStorage.removeItem(key);
    }

    static async clear(){
        // console.log('清除数据:',sys.localStorage.length)

        let list:string[] = [];

        for (let i = 0; i <  sys.localStorage.length; i++) {
            let key:string =  sys.localStorage.key(i); //获取本地存储的Key
            if (key.indexOf(this.GameKey) >=0 && key.indexOf('login') == -1 && key.indexOf('zzdl') == -1) {
                // log("清理前检查key", key, key.indexOf(this.GameKey) )
                list.push(key);
            }
        }

        let keys = sys.localStorage.keys();
        for (let i = 0; i <  keys.length; i++) {
            let key:string =  keys[i]; //获取本地存储的Key
            if (key.indexOf('login') == -1 && key.indexOf('zzdl') == -1) {
                // console.log("清除key:",key)
                list.push(key);
            }
        }
        for (let i = 0; i < list.length; i++) {
            const key = list[i];
            await sys.localStorage.removeItem(key);
        }
        //清空本地无用的缓存

        // sys.localStorage.clear();
    }

    static clearByKey(gameKey:string){
        let list:string[] = [];
        for (let i = 0; i <  sys.localStorage.length; i++) {
            let key:string =  sys.localStorage.key(i); //获取本地存储的Key
            if (key.indexOf(gameKey) != -1) {
                list.push(key);
            }
        }
        for (let i = 0; i < list.length; i++) {
            const key = list[i];
            sys.localStorage.removeItem(key);
        }
    }

    static getStringArrayByKey(str:string){
        // console.log("获取列表:",str)
        let list:any[] = [];
        let list2:any[] = [];
        let keys = sys.localStorage.keys();
        for (let i = 0; i <  keys.length; i++) {
            const key:string =  keys[i]; //获取本地存储的Key
            //console.log("key:",key);
            if (key.indexOf(str) != -1) {
                let n = sys.localStorage.getItem(key);
                // console.log("=====n:",n)
                list.push(n);
                list2.push(key)
            }
        }
        return {keys: list2, values: list};
    }

    static getObjectArrayByKey(str:string){
        // console.log("获取列表:",str)
        let list:any[] = [];
        let keys = sys.localStorage.keys();
        for (let i = 0; i <  keys.length; i++) {
            const key:string =  keys[i]; //获取本地存储的Key
            //console.log("key:",key);
            if (key.indexOf(str) != -1) {
                let n = sys.localStorage.getItem(key);
                //console.log("n:",n)
                let value = null;
                if (n !== null&& n !== "") {
                    try {
                        let json = JSON.parse(n);
                        value = json || null;
                    } catch (error) {
                        value = null;
                    }
                }
                if (value) {
                    list.push(value);
                }

            }

        }
        return list;
    }

}

