import {_decorator, Collider2D, Contact2DType, Node, tween, UITransform, Vec3} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Constants} from "../Constants";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level4')
export class Level4 extends LevelDialogBase {

    @property(Node)
    BoxLeft:Node = null
    @property(Node)
    BoxRight:Node = null
    @property(Node)
    WoodMan:Node = null

    leftCnt:number=0
    rightCnt:number=0
    selfCnt:number=0

    start () {
        super.start()
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.WoodMan.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onWoodManContact, this);
        //左边接触物体的逻辑
        this.BoxLeft.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBoxLeftContact, this);
        //右边边接触物体的逻辑
        this.BoxRight.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBoxRightContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
    }

    onBoxLeftContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            this.leftCnt+=1
            audioManager.instance.playSound(Constants.Sounds.out)
            if(this.leftCnt>=2){
                //飞过去
                Public.Bezier2DShow(this.BoxLeft, this.WoodMan,0.5, 150, 150,1,(flyObj)=>{
                    this.BoxLeft.active = false;
                    //TODO 火柴人动画
                })
            }else{
                //变化
                let y = this.BoxLeft.position.y
                tween(this.BoxLeft)
                    .to(0.05,{position:new Vec3(this.BoxLeft.position.x,y+10,this.BoxLeft.position.z)})
                    .to(0.1,{position:new Vec3(this.BoxLeft.position.x,y,this.BoxLeft.position.z)})
                    .call(()=>{
                        this.BoxLeft.getChildByName("AniNode").active = true
                        this.BoxLeft.getChildByName("QuestionMark").active = false
                    })
                    .start()
            }

        })
    }

    onBoxRightContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)
            this.rightCnt+=1
            if(this.rightCnt>=2){
                //飞过去
                Public.Bezier2DShow(this.BoxRight, this.WoodMan,0.5, -150, 150,1,(flyObj)=>{
                    this.BoxRight.active = false;
                    //TODO 火柴人动画
                })
            }else {
                //变化
                let y = this.BoxRight.position.y
                tween(this.BoxRight)
                    .to(0.05, {position: new Vec3(this.BoxRight.position.x, y + 10, this.BoxRight.position.z)})
                    .to(0.1, {position: new Vec3(this.BoxRight.position.x, y, this.BoxRight.position.z)})
                    .call(() => {
                        this.BoxRight.getChildByName("AniNode").active = true
                        this.BoxRight.getChildByName("QuestionMark").active = false
                    })
                    .start()
            }
        })
    }

    onWoodManContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
            if(otherCollider.node.name=="RoleNode" && otherCollider.node.scale.x >=2){
                //TODO 火柴人消失动画
                tween(this.WoodMan).to(0.5, {scale: new Vec3(0.8,0,0)}).call(()=>{
                    this.WoodMan.active = false
                    this.showKey()
                }).start()
            }
        })
    }

    touchStart(event){
        super.touchStart(event)

        let pos = event.getUILocation()
        const roleNode = this.roleNode.getComponent(UITransform)!;
        let wordPos = roleNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let dis = Public.GetDistance2D(wordPos, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(dis<60){
            this.selfCnt+=1
            if(this.selfCnt%10==0){
                //必须在火柴人的上方,否则计数清零
                if(this.roleNode.position.y > (this.WoodMan.position.y+ this.WoodMan.getComponent(UITransform).height/2 )){
                    //把自己变大
                    tween(this.roleNode).to(0.2,{scale:new Vec3(3,3,1)}).start()
                    audioManager.instance.playSound(Constants.Sounds.jump)
                }else{
                    this.selfCnt=0
                }
            }else if(this.selfCnt%10==5){
                //把自己变回去
                audioManager.instance.playSound(Constants.Sounds.jump)
                tween(this.roleNode).to(0.2,{scale:new Vec3(1,1,1)}).start()
            }
            console.log("self, touch self-------", dis)

        }
    }


}
