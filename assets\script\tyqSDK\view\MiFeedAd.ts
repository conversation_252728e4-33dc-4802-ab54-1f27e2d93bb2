// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { Component, Label, Sprite, view, _decorator, Node, UITransform, assetManager, SpriteFrame, Texture2D, ImageAsset } from "cc";
import MiAdMgr from "../Third/MiAdMgr";
import { tyqSDK } from "../tyq-sdk";



const { ccclass, property } = _decorator;

@ccclass
export default class MiFeedAd extends Component {
    @property(Sprite) spt_img: Sprite = null;
    @property(Label) label_title: Label = null;
    @property(Label) label_des: Label = null;
    @property(Label) label_tag: Label = null;
    @property(Label) label_btnText: Label = null;
    @property(Node) clickArea: Node = null;
    @property(Node) maskNode: Node = null;
    public onLoad() {
        // this.init();
    }
    public init(opt: any = {}) {
        console.log(opt);
        if (opt["title"]) {
            this.label_title.string = opt["title"];
        }

        if (opt["des"]) {
            this.label_des.string = opt["des"];
        }
        if (opt["imgUrl"]) {
            this.loadRemoteImg(opt["imgUrl"], this.spt_img);
        }

        if (opt["tag"]) {
            this.label_tag.string = opt["tag"];
        }

        if (opt["btnText"]) {
            this.label_btnText.string = opt["btnText"];
        }

        if (opt["icon"]) {
            console.log(opt["icon"]);
        }
        if (opt["logo"]) {
            console.log(opt["logo"]);
        }
        let size = this.transPhoneSize(this.clickArea);
        //
        if (tyqSDK.getSwitchValue("tyq_isFeedLargeScale")) {
            size = this.transPhoneSize(this.maskNode);
        }

        MiAdMgr.getInstance().setFeedClickArea(size.left, size.top, size.width, size.height);
        MiAdMgr.getInstance().setFeedClickCb(() => {
            this.onClickClose();
        })
    }
    //将cocos size转换为手机size
    public transPhoneSize(node: Node): { left: number, top: number, width: number, height: number } {
        let winSize = view.getVisibleSize();
        let frameSize = view.getVisibleSize();
        let sw = frameSize.width / winSize.width;
        let sh = frameSize.height / winSize.height;

        let w = node.getComponent(UITransform).width;
        let h = node.getComponent(UITransform).height;

        let worldPos = node.getWorldPosition();


        let l = worldPos.x - w / 2;

        let t = winSize.height - worldPos.y - h / 2;
        return { left: l * sw, top: t * sh, width: w * sw, height: h * sh };
    }
    protected onClickClose() {
        MiAdMgr.ins.onCloseFeed();
        this.node.destroy();
    }

    private loadRemoteImg(url: string, spt: Sprite) {
        assetManager.loadRemote(url, (err, texture: ImageAsset) => {
            if (err) {
                console.error(err);
                return;
            }
            let text = new Texture2D();
            text.image = texture;
            let sptFrame = new SpriteFrame();
            sptFrame.texture = text;
            spt.spriteFrame = sptFrame;
        })
    }

}
