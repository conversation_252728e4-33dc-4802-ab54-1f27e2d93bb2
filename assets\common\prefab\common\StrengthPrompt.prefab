[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "StrengthPrompt", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}], "_prefab": {"__id__": 162}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}], "_active": true, "_components": [], "_prefab": {"__id__": 153}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 2000, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "290tQdL8JB8J5FbBmrFgF0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "c0c6810b-faa9-44c7-8bca-7a584d9626f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "adR2z+abNAw5lMzjQyeES7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 40, "_target": {"__id__": 1}, "_left": -950, "_right": -950, "_top": -310, "_bottom": -310, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33KPZYdsxIXLY+hYOj3GAX"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_opacity": 150, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4wKCKOQ9GzpHoJBxLcs4y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__id__": 0}, "fileId": "2fEZm6sFNLtb3zojqv7E0r"}, {"__type__": "cc.Node", "_name": "PromptResBg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 14}, {"__id__": 20}, {"__id__": 26}, {"__id__": 66}, {"__id__": 106}], "_active": true, "_components": [{"__id__": 148}, {"__id__": 150}], "_prefab": {"__id__": 152}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 82.121, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TextLab", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": -18.226, "y": 127.34, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 376.2, "height": 69.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3Vqs0Vl5L65pfwpcOYxZW"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "欣赏小视频获得当天体力", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69kBHAO61LOYTViQealplb"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": {"__id__": 0}, "fileId": "5cZs6ZRmpO2ascO2lfpWtm"}, {"__type__": "cc.Node", "_name": "title1", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 23}], "_prefab": {"__id__": 25}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 219.197, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 22}, "_contentSize": {"__type__": "cc.Size", "width": 354, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "64SzcFGwhC+5q1FTc7eNhJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 24}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@3d535", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7sOjH+xFOP7gZWoed3WR2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31YYn9MOtKJ6u9Ig5Yr2mr"}, {"__type__": "cc.Node", "_name": "other", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 27}, {"__id__": 45}], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 65}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "h1", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 28}, {"__id__": 34}], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 44}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "heart", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 31}], "_prefab": {"__id__": 33}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 77.125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.6, "y": 0.6, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 30}, "_contentSize": {"__type__": "cc.Size", "width": 57, "height": 49}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeuKI8zWlI2Y4gPcT4Yr4Y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "__prefab": {"__id__": 32}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@34afd", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19SNUyj3hFH5s9W36Jtitq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3af033JXBCw73rp9MnSi01"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 27}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}, {"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": 33.029, "y": 82.222, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 32.86, "height": 64.47999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fYiOeT+9BCKBdmor5MPk4"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 38}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x5", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 48, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b4T++5pNEE6jFqjXTgwtd"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "__prefab": {"__id__": 40}, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fbxTqI99MBbNvY2gBJD8k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98QrnBE9tIiq7yBdOEUZ6I"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeqGRqXydGWI0M9TmE366/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "49bVLqHLBN06Mxmyb3zDle"}, {"__type__": "cc.Node", "_name": "h2", "_objFlags": 0, "_parent": {"__id__": 26}, "_children": [{"__id__": 46}, {"__id__": 52}], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": 179.211, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "heart", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [{"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 77.125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 46, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeHrXBS01LmKT+qk469Lik"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 50}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@94a29", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21NOin+iBHu7d8A34MaM2H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "02YiIA0MFJibA2vL0dDEBJ"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": 46.892, "y": 81.582, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 54}, "_contentSize": {"__type__": "cc.Size", "width": 60.28, "height": 64.47999999999999}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "69GH2aM4hBK47g2Osrk6ZD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 56}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "x999", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 30, "_fontSize": 30, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 48, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4TV+51hVK5b9MB30qCFLZ"}, {"__type__": "cc.LabelOutline", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 58}, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_width": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0BGvahA9MK4IXxjmkipx6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eZG7CZzFJP73Eo2jdu/H9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "__prefab": {"__id__": 61}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f02VieOo9BGKNzpmsW6j1X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ffgw0hLIRMfZaFcuOXD2XG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e7Q0GWqPxFEr1QMI7KI/+c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ce2TG/GnJA8pljzS0bwoUg"}, {"__type__": "cc.Node", "_name": "progress", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 67}, {"__id__": 83}, {"__id__": 89}, {"__id__": 95}], "_active": true, "_components": [{"__id__": 101}, {"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 29.117, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [{"__id__": 68}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 80}], "_prefab": {"__id__": 82}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 426, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58zvEjprxG+bS4/Omzf4U+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 72}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@4e698", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dg+ZKHf9MHb4jfRmFYqh3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ebpRDGEURB4awdoLTcbQq0"}, {"__type__": "cc.Node", "_name": "bg-001", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 75}, {"__id__": 77}], "_prefab": {"__id__": 79}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 76}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 47}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b31lI46vNGdrttVUxghdot"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 74}, "_enabled": true, "__prefab": {"__id__": 78}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@54355", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09cN9zPRhIoLpJAXmY1yHq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abDvJAJyNAl7drMhh4UxoG"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 81}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39Oyref9RFn7hpNB7+3xyM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3dRBgf72VE7adUWQOhWmSs"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 84}, {"__id__": 86}], "_prefab": {"__id__": 88}, "_lpos": {"__type__": "cc.Vec3", "x": -213.999, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 85}, "_contentSize": {"__type__": "cc.Size", "width": 426, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34pY9OL8RDjLU1a8U6SKUs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 87}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@58cb7", "__expectedType__": "cc.SpriteFrame"}, "_type": 3, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0.5, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dd5Akr9FhCYKC9x44Cv2kA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9afVq8W2VMmY7dX1oI9N/P"}, {"__type__": "cc.Node", "_name": "slide", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 90}, {"__id__": 92}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": 1.468, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 91}, "_contentSize": {"__type__": "cc.Size", "width": 430, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bxXBlb5xM+ZsGzbgInRvi"}, {"__type__": "cc.Slider", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "__prefab": {"__id__": 93}, "slideEvents": [], "_handle": {"__id__": 94}, "_direction": 0, "_progress": 0.5, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "can5bzCmVBXoVskh4bMyws"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 99}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d1d563db-54d1-436b-b8c3-913be18a27da@dc20f", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.Node", "_name": "btnIcon", "_objFlags": 0, "_parent": {"__id__": 66}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 94}], "_prefab": {"__id__": 98}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 97}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 54}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0BN4K1FRMXLVi/FyxPTQz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3wMwcGzZG1JVr5TfboA73"}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dyC9WhEZHv6Djoob5HMNT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bd1BzuDNVELbxwYdD+CFWv"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 102}, "_contentSize": {"__type__": "cc.Size", "width": 460, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bOVKIjaZK+YFz3nnjNscd"}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "__prefab": {"__id__": 104}, "_barSprite": {"__id__": 86}, "_mode": 2, "_totalLength": 1, "_progress": 0.5, "_reverse": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8acyn/Pr9O252MBMi60+Nz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "66lA1xK4JCgYKpk0FX1Jm+"}, {"__type__": "cc.Node", "_name": "btns", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 107}, {"__id__": 128}], "_active": true, "_components": [{"__id__": 143}, {"__id__": 145}], "_prefab": {"__id__": 147}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -82.474, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnBg2", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 108}, {"__id__": 114}], "_active": true, "_components": [{"__id__": 120}, {"__id__": 122}, {"__id__": 124}], "_prefab": {"__id__": 127}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnText", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_components": [{"__id__": 109}, {"__id__": 111}], "_prefab": {"__id__": 113}, "_lpos": {"__type__": "cc.Vec3", "x": 0.157, "y": 6.035, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 110}, "_contentSize": {"__type__": "cc.Size", "width": 136.8, "height": 69.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49Dc0Si89GxZuMaXx93SjK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 112}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "现在欣赏", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0p1G1TaJHfItz3vf1cSvM"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": {"__id__": 0}, "fileId": "eahSCjQeZAAbDt8sh+g48k"}, {"__type__": "cc.Node", "_name": "videoIcon", "_objFlags": 0, "_parent": {"__id__": 107}, "_children": [], "_active": true, "_components": [{"__id__": 115}, {"__id__": 117}], "_prefab": {"__id__": 119}, "_lpos": {"__type__": "cc.Vec3", "x": 88.22, "y": 41.496, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 116}, "_contentSize": {"__type__": "cc.Size", "width": 49, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aathP/+0JIoIN/EgEk8lWz"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 114}, "_enabled": true, "__prefab": {"__id__": 118}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@7b90c", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37OH0bAKtC/oHcacTchjz1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 114}, "asset": {"__id__": 0}, "fileId": "8aC2WkPgVA17sMO1QRxM+x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 121}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aCFAz5AVH75btd8m+gcUu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 123}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e9Vw8pWxP8L9spc0mLI0D"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 125}, "clickEvents": [{"__id__": 126}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaspY5dIFI+79vOx7R9Q1u"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9b70fRPQR5I46Z5aTNfrbjT", "handler": "OnClickGetStrength", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 107}, "asset": {"__id__": 0}, "fileId": "7bdtXGrq1OTYpWScTOrJCf"}, {"__type__": "cc.Node", "_name": "CloseBtn", "_objFlags": 0, "_parent": {"__id__": 106}, "_children": [{"__id__": 129}], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}, {"__id__": 140}], "_prefab": {"__id__": 142}, "_lpos": {"__type__": "cc.Vec3", "x": 123, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnText", "_objFlags": 0, "_parent": {"__id__": 128}, "_children": [], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}], "_prefab": {"__id__": 134}, "_lpos": {"__type__": "cc.Vec3", "x": 4.48, "y": 5.982, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 106.4, "height": 69.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e28En3nFdJwYqXqpsTIv55"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "__prefab": {"__id__": 133}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "不必了", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 38, "_fontSize": 38, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22yrP/UoFMOqDkIXu86ho0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abuVFmg/9K7LwcpPrd5c4l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45IDkBW6tDf7tmJG33iVzp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 138}, "clickEvents": [{"__id__": 139}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.1, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aaleElj+5M2YfJ3/f1+8fU"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "9b70fRPQR5I46Z5aTNfrbjT", "handler": "close", "customEventData": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 141}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5e9Vw8pWxP8L9spc0mLI0D"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "75kCSpq6VAG7GB6iLm2u9x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 144}, "_contentSize": {"__type__": "cc.Size", "width": 454, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8FEx5DjxJwZtP2q2PU2HU"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 146}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 38, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bUtj81bZItY5+x8aivRVw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "cbYL195qlHToKx6EaCJ/dc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 149}, "_contentSize": {"__type__": "cc.Size", "width": 667, "height": 411}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19NcqqRcpJnbZdhUZ4AohW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 151}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@55807", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cafHPgKllA/6bTRM+VIvC1"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__id__": 0}, "fileId": "06fnGxGVJMJ5Qos7HAuRVg"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": {"__id__": 0}, "fileId": "a7NWKQXEJHcJpRylKdEPYa"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 155}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "951xR5TsZK/Iung6SkWPYG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 157}, "_alignFlags": 0, "_target": null, "_left": 590, "_right": 590, "_top": 310, "_bottom": 310, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8crXApa9ZAaKx1eEre7vS9"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 159}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33CnK3nrxHMpLXEMlnmgoa"}, {"__type__": "9b70fRPQR5I46Z5aTNfrbjT", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 161}, "zIndex": 200, "AniNode": {"__id__": 13}, "Slider": {"__id__": 92}, "Progress": {"__id__": 103}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11NkExN35CMIZM4gPzIaSY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c46/YsCPVOJYA4mWEpNYRx"}]