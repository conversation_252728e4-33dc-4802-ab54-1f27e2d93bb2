[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "Prompt", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 58}, {"__id__": 60}, {"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 13}], "_active": true, "_components": [{"__id__": 55}], "_prefab": {"__id__": 57}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}, {"__id__": 10}], "_prefab": {"__id__": 12}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 3180, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68V3DGfIpD9Ii30IRXh652"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "c0c6810b-faa9-44c7-8bca-7a584d9626f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92eth8Q/hEPIaf87RM5Uha"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 40, "_target": {"__id__": 1}, "_left": -590, "_right": -590, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92xgWWknxO2L2mADrJeWYo"}, {"__type__": "cc.UIOpacity", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_opacity": 150, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dde2ApHu9KBrfofdVvJP70"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfuQ9IGc5AJKCZYB0niJmE"}, {"__type__": "cc.Node", "_name": "PromptResBg", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 14}, {"__id__": 20}, {"__id__": 41}], "_active": true, "_components": [{"__id__": 50}, {"__id__": 52}], "_prefab": {"__id__": 54}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 82.121, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TextLab", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 82.463, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 16}, "_contentSize": {"__type__": "cc.Size", "width": 314.95, "height": 69.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0dfZDWwihIgLze4qaBiIdH"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 18}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "是否获取提示？", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2az65x9tCnKHcxKZReL4k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7esOF2t+ZJRrBTj9srvEib"}, {"__type__": "cc.Node", "_name": "BtnBg2", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 21}, {"__id__": 27}], "_active": true, "_components": [{"__id__": 33}, {"__id__": 35}, {"__id__": 37}], "_prefab": {"__id__": 40}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -90, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BtnText", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 22}, {"__id__": 24}], "_prefab": {"__id__": 26}, "_lpos": {"__type__": "cc.Vec3", "x": 31.781, "y": 9.165, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 23}, "_contentSize": {"__type__": "cc.Size", "width": 89.95, "height": 69.3}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97U5wEcKBOgJFKpkczbMlx"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 21}, "_enabled": true, "__prefab": {"__id__": 25}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "获取", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 50, "_fontSize": 50, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 55, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "76jPLbBXdImJIGj2FvV44c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "44pGccKc1GUZLWwMOYnopS"}, {"__type__": "cc.Node", "_name": "videoIcon", "_objFlags": 0, "_parent": {"__id__": 20}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": -49.243, "y": 4.039, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_contentSize": {"__type__": "cc.Size", "width": 61, "height": 43}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8fFBOlDYFB+4Ch752r0YJm"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@7b90c", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "45MgRRCvFJoqli2N+T69h0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6TrqSzS5E+LjmgP+aHo45"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 34}, "_contentSize": {"__type__": "cc.Size", "width": 208, "height": 91}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54T8D48ChKJIE1q8I5wKGF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 36}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60/8WuanxGJ521lk84QYTG"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 20}, "_enabled": true, "__prefab": {"__id__": 38}, "clickEvents": [{"__id__": 39}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@24ee3", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33ZRCT1RZDU7DFPvgMPnIL"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "318f7efwFhMdY8NLdSr5BJJ", "handler": "OnClickGetResByAD", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "80/CBZPbROIKpIfJuP8iMU"}, {"__type__": "cc.Node", "_name": "CloseBtn", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}, {"__id__": 46}], "_prefab": {"__id__": 49}, "_lpos": {"__type__": "cc.Vec3", "x": 285.163, "y": 178.124, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 66, "height": 66}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59/kUNP8hIvokLyq2blf68"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 45}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@90118", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d638hcgehFyZguMJ8FlouM"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "__prefab": {"__id__": 47}, "clickEvents": [{"__id__": 48}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@90118", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4789hIkddA3J8BqNnp7Fjx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "318f7efwFhMdY8NLdSr5BJJ", "handler": "OnClickClose", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20+wpZSPxL56N0lGEI6CKA"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 51}, "_contentSize": {"__type__": "cc.Size", "width": 667, "height": 411}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bN+0ChMRAZoW3YuJ128V5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 53}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@55807", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "67MhbsohRCbbaipQdA8p00"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27MwE/SE9HH4s1pXctTjb9"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bMFgMaA1LFbsPibuj0LSE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aaZ+OEtfhEh6n24Uhu8n6x"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 59}, "_contentSize": {"__type__": "cc.Size", "width": 2000, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fBe5tRa9FLbK2K2Sytf3p"}, {"__type__": "318f7efwFhMdY8NLdSr5BJJ", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 61}, "zIndex": 100, "AniNode": {"__id__": 13}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b183UGy0BPPK7TeS7PUmGN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 63}, "_alignFlags": 0, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1280, "_originalHeight": 720, "_alignMode": 0, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41FeBT6pJP7al4mZLCOI/D"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 65}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01JyAZyn9I1ZU07vShDKch"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ecihZagE9OK4QNy+365QoA"}]