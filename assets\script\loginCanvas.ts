// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import {_decorator, assetManager, director, Label, Node, tween, Vec3} from "cc";
import {localConfig} from "./framework/localConfig";
import {playerData} from "./framework/playerData";
import {configuration} from "./framework/configuration";
import {loading} from "./ui/common/loading";
import {i18n} from "./i18nMaster/runtime-scripts/LanguageData";
import {audioManager} from "./framework/audioManager";
import {comm} from "./framework/comm";
import {Public} from "./game/Public";
import {tyqSDK} from "./tyqSDK/tyq-sdk";
import TyqEventMgr from "./tyqSDK/tyq-event-mgr";
import {AdCtl} from "./channel/AdCtl";
import {Constants} from "./game/Constants";
import {gameStorage} from "./framework/gameStorage";

const { ccclass, property } = _decorator;

i18n.init('zh');

@ccclass("loginCanvas")
export class loginCanvas extends comm {
    /* class member could be defined like this */
    // dummy = '';

    @property(Label)
    lbVersion: Label = null!;

    @property(loading)
    loadingUI: loading = null!;

    @property(Node)
    bottle: Node = null!;

    retryTimes: number = 0;
    uid: string = '';
    curProgress: number = 0;
    isLoginFinished: boolean = false;
    isSubPackageFinished: boolean = false;
    isConfigLoaded: boolean = false;

    start () {
        // profiler.hideStats();

        this.loadingUI.show(0);

        //后续将由服务器提供时间
        playerData.instance.syncServerTime(Date.now());

        // Your initialization goes here.
        // this.curProgress = 0; //起始10%
        // this.loadingUI.updateProgress(this.curProgress, i18n.t("main.dataLoading"));
        localConfig.instance.loadConfig(()=>{
            // cc.gameSpace.isConfigLoadFinished = true;

            // this.lbVersion.string = `Version. ${localConfig.instance.getVersion()}`;
            this.lbVersion.string = `版本 ${Constants.Version}`;
            this.isConfigLoaded = true;
            this.loadMainScene();
        });

        // this.curProgress += 5;
        // if (this.loadingUI) {
        //     this.loadingUI.updateProgress(this.curProgress, i18n.t("main.dataLoadOver"));
        // }
        //
        // this.curProgress += 5;
        // // this.loadingUI.updateProgress(this.curProgress, '登录中...');
        // //其他环境下，直接开始加载资源
        // this.curProgress += 15;
        // this.loadingUI.updateProgress(this.curProgress, i18n.t("main.gameResourceLoading"));

        //普通用户登录
        playerData.instance.loadGlobalCache();
        if (!playerData.instance.userId) {
            //需要创建个账号
            this.uid = configuration.generateGuestAccount();
        } else {
            this.uid = playerData.instance.userId;
        }

        this.startLogin();

        this.initMusic()

        this.loadGameSubPackage(() => {
            console.log('subpackage download finished!');
            this.isSubPackageFinished = true;
            TyqEventMgr.ins.sendEvent("进入加载页");
            this.loadMainScene();
        }).then(()=>{
            console.log("---------子包加载完毕----------")
        })

    }

    share(){
        Public.Share()
    }

    initMusic(){
        let stat = gameStorage.getString('music', 'true');
        console.log("----------initMusic----", stat)
        if(stat=='true'){
            audioManager.instance.openMusic()
        }else{
            audioManager.instance.closeMusic()
        }

        let stat2 = gameStorage.getString('sound', 'true');
        if(stat2=='true'){
            audioManager.instance.openSound()
        }else{
            audioManager.instance.closeSound()
        }
    }

    startLogin () {
        configuration.instance.setUserId(this.uid);

        playerData.instance.syncServerTime(Date.now());
        this.userLogin();
    }

    userLogin () {
        playerData.instance.loadFromCache();

        if (playerData.instance.playerInfo.createDate === undefined) {
            //表示没有创建过
            playerData.instance.createPlayerInfo();
        }

        console.log('login finished!');
        this.isLoginFinished = true;
        this.loadMainScene();

    }

    // downloadGameRes (cb?: Function) {
    //     //不用加载子包，直接+30
    //     this.curProgress += 15;
    //     this.loadingUI.updateProgress(this.curProgress);
    //
    //     cb && cb();
    // }

    showSubPackageError () {

    }

    async loadGameSubPackage (cb?: Function) {
        // this.loadingUI.updateProgress(this.curProgress, i18n.t("main.default"));
        // let pos = this.bottle.position
        // tween(this.bottle).to(0.2,{position:new Vec3(pos.x+100, pos.y, pos.z)}).start()
        // await this.loadBundle('resources')
        // tween(this.bottle).to(0.2,{position:new Vec3(pos.x+100, pos.y, pos.z)}).start()
        // await this.loadBundle('font')
        // tween(this.bottle).to(0.2,{position:new Vec3(pos.x+100, pos.y, pos.z)}).start()
        // await this.loadBundle('prefab')
        // tween(this.bottle).to(0.3,{position:new Vec3(0, pos.y, pos.z)}).start()
        // await this.loadBundle('textures')

        //初始化SDK
        tyqSDK.init(()=>{
            console.log("初始化成功");
            tyqSDK.login();
        });

        cb && cb();
    }

    async loadBundle(name){
        return new Promise(result=>{
            assetManager.loadBundle(name, (err,bundle)=>{
                result(bundle);
                this.curProgress += 5;
                this.loadingUI.updateProgress(this.curProgress, i18n.t("main."+name));
                if (err) {
                    this.showSubPackageError();
                    return console.error(err);
                }
                // if (err) {
                //     console.log('加载bundle错误:',name,err)
                //     return;
                // }
                // console.log("加载完成")
            })
        })
    }

    loadMainScene() {
        if (!this.isConfigLoaded || !this.isLoginFinished || !this.isSubPackageFinished) {
            //配置，子包，登录，三项没有加载成功的话要等下一项
            return;
        }

        director.preloadScene('main', (err) => {
            this.curProgress += 5;
            this.loadingUI.updateProgress(this.curProgress, i18n.t("main.entering"));
            if (!err) {
                director.loadScene('main', function () {

                });
            }
        });
    }
}
