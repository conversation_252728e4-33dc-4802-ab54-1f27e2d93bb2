
import { _decorator, Component, Vec3, Label } from 'cc';
import {dialogBase} from "../../framework/dialogBase";
import {localConfig} from "../../framework/localConfig";
import {Constants} from "../../game/Constants";
import {Public} from "../../game/Public";
import {AdCtl} from "../../channel/AdCtl";
import {clientEvent} from "../../framework/clientEvent";
import {configuration} from "../../framework/configuration";
import {uiManager} from "../../framework/uiManager";
import TyqEventMgr from "../../tyqSDK/tyq-event-mgr";
import {gameStorage} from "../../framework/gameStorage";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = PromptRes
 * DateTime = Fri Feb 25 2022 16:28:34 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = PromptRes.ts
 * FileBasenameNoExtension = PromptRes
 * URL = db://assets/script/ui/common/PromptRes.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('PromptRes')
export class PromptRes extends dialogBase {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;

    @property(Label)
    LabelContent:Label =null

    levelNum:number=0

    start () {
        // [3]
    }

    show(){
        super.show(null)
        let levelTableObj = localConfig.instance.getTable(Constants.CSVTables.level)
        let order = localConfig.instance.getTable(Constants.CSVTables.order)
        let level = order[Public.CurLevelNum].level
        this.LabelContent.string = levelTableObj[level].prompt
        this.levelNum = Public.CurLevelNum
        AdCtl.instance.ShowBannerAd()
    }

    OnClickClose(){
        clientEvent.dispatchEvent(Constants.Events.resetLevelInsertTimeout)
        this.close()
        AdCtl.instance.HideBannerAD()
        AdCtl.instance.ShowCustomLevelUI()
        AdCtl.instance.ShowMainCanvas()
    }

    OnClickSkipLevel(){
        AdCtl.instance.HideBannerAD()
        AdCtl.instance.ShowRewardVideoAd(()=>{
            AdCtl.instance.ShowBannerAd()
            this.OnClickClose()
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoSuccess, cb:()=>{
                    uiManager.instance.destroyDialog(Constants.Dialogs.ops)
                    uiManager.instance.destroyDialog(Public.GetRightLevelPath())

                    TyqEventMgr.ins.sendEvent("观看激励视频-跳过关卡"+Public.CurLevelNum);
                    this.levelNum += 1
                    Public.CurLevelNum = this.levelNum
                    //隐藏当前关卡，进入下一个关卡，解锁下一个关卡
                    let isUnlock = gameStorage.getInt(Constants.LevelHead + this.levelNum, Constants.LevelState.locked)
                    //保存到本地数据
                    if(isUnlock==undefined || isUnlock <= Constants.LevelState.open){
                        gameStorage.setInt(Constants.LevelHead+this.levelNum, Constants.LevelState.open)
                    }
                    Public.LoadLevel(uiManager.instance)
                }})
        }, ()=>{
            AdCtl.instance.ShowBannerAd()
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoFail, cb:()=>{

                }})
            // uiManager.instance.showTips(`视频观看失败`)
        });
    }

}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
