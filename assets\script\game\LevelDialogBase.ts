
import { _decorator, EventTouch, Node, Vec3, RigidBody2D,tween, v2, PhysicsSystem2D,Label, Contact2DType, Collider2D, sp, ERigidBody2DType , EPhysics2DDrawFlags,UITransform} from 'cc';
import {dialogBase} from "../framework/dialogBase";
import {clientEvent} from "../framework/clientEvent";
import {Constants} from "./Constants";
import {Public} from "./Public";
import {uiManager} from "../framework/uiManager";
import {audioManager} from "../framework/audioManager";
import {localConfig} from "../framework/localConfig";
import {AdCtl} from "../channel/AdCtl";
import {tyqSDK} from "../tyqSDK/tyq-sdk";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = LevelDialogBase
 * DateTime = Mon Feb 28 2022 17:01:01 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = LevelDialogBase.ts
 * FileBasenameNoExtension = LevelDialogBase
 * URL = db://assets/script/game/LevelDialogBase.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */

@ccclass('LevelDialogBase')
export class LevelDialogBase extends dialogBase {
    @property(Node)
    roleNode:Node = null
    @property(Node)
    doorNode:Node = null
    @property(Label)
    levelLabel:Label = null
    @property(Node)
    Key:Node = null

    moveLeft:boolean = false;
    moveRight:boolean = false;
    broaderX:number[] = [-1000, 1000]
    broaderY:number[] = [-1000, 1000]
    isJumping:boolean = false;
    isLocked:boolean = false;
    isEnd:boolean = false;
    keyTouchOnce:boolean = false; //钥匙只碰一次
    checkKeyTime:number = 0;
    isInWater:boolean = false;
    roleAniStat:string = "" //角色动画
    isBorderOutFail:boolean = true  //是否超出边界就算失败
    isStop:boolean = false //玩家是否被暂停住

    roleSpine:sp.Skeleton = null //角色的spine
    isShowInsert:boolean = false
    isShowDieAni:boolean = true

    show(param){
        //展示banner广告
        AdCtl.instance.HideCustomLevelUI()
        AdCtl.instance.HideMainCanvas()
        AdCtl.instance.ShowCustomLevelUI()
        AdCtl.instance.ShowMainCanvas()
    }
    hide(){
        AdCtl.instance.HideCustomLevelUI()
        AdCtl.instance.HideMainCanvas()
        this.clearLevelInsertTimeout()
    }

    superShow(){
        // let order = localConfig.instance.getTable("order")
        this.levelLabel.string = `第${Public.CurLevelNum}关`
        uiManager.instance.showDialog(Constants.Dialogs.ops)
    }

    flyKey(){
        //飞向大门
        Public.Bezier2DShow(this.Key, this.doorNode,0.5, 0, 50,0.8,(flyObj)=>{
            console.log("--------xxx--------")
            audioManager.instance.playSound(Constants.Sounds.unlock)
            //解锁
            this.isLocked = false
            this.doorNode.getChildByName("Lock").active = false;
            this.Key.active = false;
        })
    }

    update (deltaTime: number) {
        Constants.GameRunTime += deltaTime
        // console.log("-----GameRunTime------",  Constants.GameRunTime)
        if(this.isEnd) return;
        // [4]
        let speed = Constants.MoveXSpeed;
        // console.log(deltaTime*speed)
        //控制左右的移动
        if(this.isStop==false){
            if(this.moveLeft){
                this.roleNode.setPosition(new Vec3(this.roleNode.position.x-deltaTime*speed, this.roleNode.position.y, this.roleNode.position.z))
            }else if(this.moveRight){
                this.roleNode.setPosition(new Vec3(this.roleNode.position.x+deltaTime*speed, this.roleNode.position.y, this.roleNode.position.z))
            }
        }

        //判断是否超出边界，超出则重新开始
        if(this.isBorderOutFail==true){
            if(this.roleNode.position.x < this.broaderX[0] || this.roleNode.position.x > this.broaderX[1]){
                this.Lose()
            }else if(this.roleNode.position.y < this.broaderY[0] || this.roleNode.position.y > this.broaderY[1]){
                this.Lose()
            }
        }

        //判断是否胜利
        if(this.isLocked==false){
            const roleNodeUIT = this.roleNode.getComponent(UITransform)!;
            let roleNodeUITWP = roleNodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
            const doorNodeUIT = this.doorNode.getComponent(UITransform)!;
            let doorNodeUITWP = doorNodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
            let doorDis = Public.GetDistance2D(roleNodeUITWP, doorNodeUITWP)
            if(doorDis<80){
                this.Win()
            }
        }

        //判断钥匙是否显示，如果显示，每300毫秒检查一次
        if(this.Key && this.Key.active==true){
            let worldPosKey = this.Key.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let worldPosRole = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(worldPosKey, worldPosRole)
            if(dis<50){
                if(this.keyTouchOnce == false){
                    this.keyTouchOnce = true
                    this.flyKey()
                }
            }
        }
    }

    Lose(delay:number=2.5){
        this.isEnd = true;
        audioManager.instance.playSound(Constants.Sounds.gameOver)
        if(this.isShowDieAni){
            this.roleSpine.setAnimation(1, "die", false)
        }
        this.scheduleOnce(()=>{
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.levelFail, cb:()=>{
                    clientEvent.dispatchEvent(Constants.Events.restart)
                }})
        },delay)

        tyqSDK.endGame(false)
    }

    Win(){
        this.isEnd = true;
        audioManager.instance.playSound(Constants.Sounds.openDoor)
        let door = this.doorNode.getChildByName("Door")
        tween(door).to(0.62, {scale:new Vec3(0.2,1,1)}).call(()=>{
            clientEvent.dispatchEvent(Constants.Events.clearLevelInsertTimeout)
            this.scheduleOnce(()=>{
                uiManager.instance.showDialog(Constants.Dialogs.success, null, ()=>{
                    uiManager.instance.getDialog(Constants.Dialogs.success).setPosition(new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0))
                })
            },0.38)
        }).start()
        tyqSDK.endGame(true)
    }

    onEnable(){
        clientEvent.on(Constants.Events.opsLeftStart, this.opsLeftStart, this);
        clientEvent.on(Constants.Events.opsLeftEnd, this.opsLeftEnd, this);
        clientEvent.on(Constants.Events.opsRightStart, this.opsRightStart, this);
        clientEvent.on(Constants.Events.opsRightEnd, this.opsRightEnd, this);
        clientEvent.on(Constants.Events.opsUp, this.opsUp, this);
        clientEvent.on(Constants.Events.opsDown, this.opsDown, this);
        clientEvent.on(Constants.Events.touchStart, this.touchStart, this);
        clientEvent.on(Constants.Events.touchMove, this.touchMove, this);
        clientEvent.on(Constants.Events.touchEnd, this.touchEnd, this);
        clientEvent.on(Constants.Events.touchCancel, this.touchCancel, this);
        clientEvent.on(Constants.Events.clearLevelInsertTimeout, this.clearLevelInsertTimeout, this);
        clientEvent.on(Constants.Events.resetLevelInsertTimeout, this.resetLevelInsertTimeout, this);

        Constants.GameRunTime=0
        this.resetLevelInsertTimeout()
        tyqSDK.startGame(Public.CurLevelNum);
    }

    onDisable(){
        clientEvent.off(Constants.Events.opsLeftStart, this.opsLeftStart, this);
        clientEvent.off(Constants.Events.opsLeftEnd, this.opsLeftEnd, this);
        clientEvent.off(Constants.Events.opsRightStart, this.opsRightStart, this);
        clientEvent.off(Constants.Events.opsRightEnd, this.opsRightEnd, this);
        clientEvent.off(Constants.Events.opsUp, this.opsUp, this);
        clientEvent.off(Constants.Events.opsDown, this.opsDown, this);
        clientEvent.off(Constants.Events.touchStart, this.touchStart, this);
        clientEvent.off(Constants.Events.touchMove, this.touchMove, this);
        clientEvent.off(Constants.Events.touchEnd, this.touchEnd, this);
        clientEvent.off(Constants.Events.touchCancel, this.touchCancel, this);
        clientEvent.off(Constants.Events.clearLevelInsertTimeout, this.clearLevelInsertTimeout, this);
        clientEvent.off(Constants.Events.resetLevelInsertTimeout, this.resetLevelInsertTimeout, this);
        this.clearLevelInsertTimeout()

    }

    clearLevelInsertTimeout(){
        // this.unschedule(this.showInsertAd)
    }
    resetLevelInsertTimeout(){
        // if(this.isShowInsert==false){
        //     this.scheduleOnce(this.showInsertAd, 8)
        // }
    }
    showInsertAd(){
        this.isShowInsert = true
        //老手才给插屏广告
        // if(AdCtl.instance.IsPlayAd()==false) return
        // AdCtl.instance.HideCustomLevelUI()
        // AdCtl.instance.ShowInterstitialAd(()=>{
        //     AdCtl.instance.ShowCustomLevelUI()
        // })
    }

    touchStart(event:EventTouch){
        // console.log("parent, touchStart-------", event.getUILocation())
    }
    touchMove(event:EventTouch){
        // console.log("parent, touchMove-------", event.getUILocation())
    }
    touchEnd(event:EventTouch){
        // console.log("parent, touchEnd-------", event.getUILocation())
    }
    touchCancel(event:EventTouch){
        // console.log("parent, touchCancel-------", event.getUILocation())
    }

    roleRun(){
        console.log("roleRun-------")
        if(this.isInWater){
            if(this.roleAniStat!="run"){
                this.roleAniStat = "run";
                this.roleSpine.setAnimation(1,"jump", true)
            }
        }else{
            if(this.roleAniStat!="run"){
                this.roleAniStat = "run";
                this.roleSpine.setAnimation(1,"run", true)
            }
        }

    }
    roleJump(){
        // console.log("roleJump-------")
        if(this.isInWater){
            if(this.roleAniStat!="jump"){
                this.roleAniStat = "jump";
                this.roleSpine.setAnimation(1,"jump", true)
            }
        }else{
            this.roleSpine.setAnimation(1,"jump", false)
        }
    }
    roleStandby(){
        // console.log("roleStandby-------", this.isInWater, this.roleAniStat)
        if(this.isInWater){
            if(this.roleAniStat!="standBy"){
                this.roleAniStat = "standBy";
                this.roleSpine.setAnimation(1,"jump", true)
            }
        }else{
            if(this.roleAniStat!="standBy"){
                this.roleAniStat = "standBy";
                this.roleSpine.setAnimation(1,"standBy", true)
            }
        }
    }


    opsLeftStart(){
        if(this.isEnd) return
        this.moveLeft = true;
		let s = this.roleNode.scale
		this.roleNode.setScale(new Vec3(-Math.abs(s.x), s.y, s.z))
        if(this.isJumping==false){
			// this.roleSpine.clearTracks();
            this.roleRun()
            // this.roleSpine.setAnimation(1,"run", true)
        }
    }

    opsLeftEnd(){
        if(this.isEnd) return
        this.moveLeft = false;
        if(this.isJumping==false){
            this.roleStandby()
            // this.roleSpine.setAnimation(1,"standBy", true)
        }
    }

    opsRightStart(){

        if(this.isEnd) return
        this.moveRight = true;
		let s = this.roleNode.scale
		this.roleNode.setScale(new Vec3(Math.abs(s.x), s.y, s.z))
        if(this.isJumping==false){
            this.roleRun()
            // this.roleSpine.setAnimation(1,"run", true)
        }
    }

    opsRightEnd(){
        if(this.isEnd) return
        this.moveRight = false;
        if(this.isJumping==false){
            console.log("right end ----", this.isJumping)
            this.roleStandby()
            // this.roleSpine.setAnimation(1,"standBy", true)
        }
    }

    opsUp(){
        if(this.isEnd) return
        // console.log("----events----", clientEvent.handlers)
        if(this.isStop) return
        if(this.isJumping==false){
            this.isJumping = true;
            //施加一个向上的脉冲力,只有底部触碰后才可以再次跳跃。
            console.warn("@@@@ jumpUp----", this.isJumping)
            this.roleNode.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(0,Constants.ImpulseUp), false);
            this.roleJump()
            // this.roleSpine.setAnimation(1,"jump", false)
        }
    }

    opsDown(){

    }

    start(){
        if(Public.MainCamera!=null){
            this.node.position = new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0)
        }
        PhysicsSystem2D.instance.enable = true;
        // PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb |
        //     EPhysics2DDrawFlags.Pair |
        //     EPhysics2DDrawFlags.CenterOfMass |
        //     EPhysics2DDrawFlags.Joint |
        //     EPhysics2DDrawFlags.Shape;
        let collider = this.roleNode.getComponent(Collider2D);
        collider.on(Contact2DType.BEGIN_CONTACT, this.onRoleBeginContact, this);
        // [3]

        this.roleSpine = this.roleNode.getChildByName("RoleSpine").getComponent('sp.Skeleton') as sp.Skeleton;

    }

    onRoleBeginContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        // let otherBody = otherCollider.body;
        // let platformBody = selfCollider.body;
        //
        // let worldManifold = contact.getWorldManifold();
        // let points = worldManifold.points;

        // console.log("111111111---", points)
        //如果自己在被接触的物体的上方，那么可以再跳

        // Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
        //
        // })

        let h = selfCollider.node.getComponent(UITransform).contentSize.height
        // console.log("-----", selfCollider.node.position.y - h/2, "-----", otherCollider.node.position.y)
        if(selfCollider.node.position.y - h/2 > otherCollider.node.position.y){
			if(this.isJumping==true){
                console.log("onRoleBeginContact roleStandby---")
                this.roleStandby()
			}
            // this.roleSpine.setAnimation(1,"run", true)
            //
            if(this.moveLeft==true || this.moveRight==true){
                this.roleRun()
            }
            this.isJumping = false;
			console.warn("@@@ 碰到东西变jumping-----", this.isJumping)
        }
        //check if contact points are moving into platform
        // for (let i = 0; i < points.length; i++) {
        //     platformBody.getLinearVelocityFromWorldPoint(points[i], pointVelPlatform);
        //     otherBody.getLinearVelocityFromWorldPoint(points[i], pointVelOther);
        //     platformBody.getLocalVector(pointVelOther.subtract(pointVelPlatform), relativeVel);
        //
        //     if (relativeVel.y < -1 * PHYSICS_2D_PTM_RATIO) //if moving down faster than PHYSICS_2D_PTM_RATIO pixel/s (1m/s), handle as before
        //         return;  //point is moving into platform, leave contact solid and exit
        //     else if (relativeVel.y < 1 * PHYSICS_2D_PTM_RATIO) { //if moving slower than PHYSICS_2D_PTM_RATIO pixel/s (1m/s)
        //         //borderline case, moving only slightly out of platform
        //         platformBody.getLocalPoint(points[i], relativePoint);
        //         let platformFaceY = selfCollider.worldAABB.height / 2;  //front of platform, should only used on a box collider
        //         if (relativePoint.y > platformFaceY - 0.1 * PHYSICS_2D_PTM_RATIO)
        //             return;  //contact point is less than 3.2pixel (10cm) inside front face of platfrom
        //     }
        //     else {
        //         //moving up faster than 1 m/s
        //     }
        // }

        // console.log("2222222222---")

        // store disabled state to contact
        // contact.disabled = true;
    }


    onKeyContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        if(this.keyTouchOnce == true) return
        //如果自己是动态的，改为静态
        // if(selfCollider.node.getComponent(RigidBody2D).type == ERigidBody2DType.Dynamic){
        //     this.Key.getComponent(RigidBody2D).type = ERigidBody2DType.Static
        // }
        // console.log("-----name-------", otherCollider.node.name)
        if(otherCollider.node.name=="RoleNode"){
            this.keyTouchOnce = true
        }else{
            return
        }

        //飞向大门
        Public.RunCbContactDistance(selfCollider, otherCollider, 50, ()=>{
            Public.Bezier2DShow(this.Key, this.doorNode,0.5, 0, 50,0.8,(flyObj)=>{
                console.log("--------xxx--------")
                audioManager.instance.playSound(Constants.Sounds.unlock)
                //解锁
                this.isLocked = false
                this.doorNode.getChildByName("Lock").active = false;
                this.Key.active = false;
            })

        })
    }

    showKey(){
        if(this.Key && this.Key.active==false){
            audioManager.instance.playSound(Constants.Sounds.prompt)
            this.Key.active = true
            this.Key.getComponent(RigidBody2D).type = ERigidBody2DType.Dynamic
        }
    }

}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
