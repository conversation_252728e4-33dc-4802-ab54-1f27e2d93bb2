{"ver": "1.0.22", "importer": "image", "imported": true, "uuid": "8cdf6e68-f464-4d62-a219-c0a575ee18a2", "files": [".png", ".json"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "8cdf6e68-f464-4d62-a219-c0a575ee18a2@6c48a", "displayName": "fishnet", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "8cdf6e68-f464-4d62-a219-c0a575ee18a2"}, "ver": "1.0.21", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "8cdf6e68-f464-4d62-a219-c0a575ee18a2@f9941", "displayName": "fishnet", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 284, "height": 218, "rawWidth": 284, "rawHeight": 218, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "isUuid": true, "imageUuidOrDatabaseUri": "8cdf6e68-f464-4d62-a219-c0a575ee18a2@6c48a", "atlasUuid": ""}, "ver": "1.0.9", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"hasAlpha": true, "type": "sprite-frame", "redirect": "8cdf6e68-f464-4d62-a219-c0a575ee18a2@f9941"}}