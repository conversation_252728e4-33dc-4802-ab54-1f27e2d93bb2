
import { _decorator, Component, Node } from 'cc';
import {Public} from "../Public";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Wave1
 * DateTime = Mon Mar 14 2022 09:49:48 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Wave1.ts
 * FileBasenameNoExtension = Wave1
 * URL = db://assets/script/game/common/Wave1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Wave2')
export class Wave2 extends Component {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;

    start () {
        // [3]
        Public.MoveWater(this.node, this.node)
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
