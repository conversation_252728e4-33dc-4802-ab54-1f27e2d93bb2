[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "mainUI", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 226}, {"__id__": 228}, {"__id__": 230}], "_prefab": {"__id__": 232}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 39}, {"__id__": 198}, {"__id__": 212}], "_active": true, "_components": [{"__id__": 223}], "_prefab": {"__id__": 225}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BgUnit1", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 1600, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eSyujn+BLc6Qje7lIGHQS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 152, "g": 152, "b": 152, "a": 255}, "_spriteFrame": {"__uuid__": "c0c6810b-faa9-44c7-8bca-7a584d9626f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6454O7uthAGbio5TheBomy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 45, "_target": {"__id__": 1}, "_left": -160, "_right": -160, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1600, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeep5zBBtCLYGoWKkwgiME"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "887v/5HOtAJrhX9CL3Cn1n"}, {"__type__": "cc.Node", "_name": "Clouds", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}, {"__id__": 18}, {"__id__": 24}, {"__id__": 30}], "_active": true, "_components": [{"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "MainListCloud", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 636.441, "y": 271, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 14}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68mqtKhJBHD7q6oJRM72zJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 16}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@d7091", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44MlqqB8VM+ILSDWtrrvv6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bcOGYVahBHJ78v4tSJj0Wq"}, {"__type__": "cc.Node", "_name": "MainListCloud-001", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 19}, {"__id__": 21}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": -588.674, "y": 187.297, "z": 1}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 20}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efjlLU3X9KPYB0STNmtT++"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "__prefab": {"__id__": 22}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@d7091", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e1BWkwcXFAvK/DQ/EN8N5O"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d5cGgZFc1LaaiqrmoP0Rz0"}, {"__type__": "cc.Node", "_name": "MainListCloud-002", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}], "_prefab": {"__id__": 29}, "_lpos": {"__type__": "cc.Vec3", "x": 639.587, "y": 121.604, "z": 2}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8VjX2OThIQp2c0002tbxV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 28}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@75e6c", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2aNw4Rn59HKrwRZS6mpF8Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16B489ei9KQ69WDWp8eKhT"}, {"__type__": "cc.Node", "_name": "MainListCloud-003", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 33}], "_prefab": {"__id__": 35}, "_lpos": {"__type__": "cc.Vec3", "x": -433.603, "y": -15.935, "z": 3}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 90}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fi9DMKlZCjYMc082F71DN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 34}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@d7091", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63wG9Z9qhKHogJs4lu/yeW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7M5RQfyRD7akGl3w6nqVC"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 37}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2sn9pJB5PAr13H/CznIlG"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b2AfmBH0lOHJXyYa6OMXbH"}, {"__type__": "cc.Node", "_name": "center", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 40}, {"__id__": 46}, {"__id__": 52}, {"__id__": 61}, {"__id__": 170}, {"__id__": 179}], "_active": true, "_components": [{"__id__": 195}], "_prefab": {"__id__": 197}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 47.179, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "MainListBg", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 41}, {"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": -35.67399999999998, "y": 47.571000000000026, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 1112, "height": 622}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81UuIxx4lOobwLBwro67Ko"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 44}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@62194", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 2, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8Bvq+Lw9CALUyVP3MyL0+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20dhdYry1PE7sGpLPLc+BN"}, {"__type__": "cc.Node", "_name": "RedPack", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": false, "_components": [{"__id__": 47}, {"__id__": 49}], "_prefab": {"__id__": 51}, "_lpos": {"__type__": "cc.Vec3", "x": 483.3109999999999, "y": 228.288, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 48}, "_contentSize": {"__type__": "cc.Size", "width": 113, "height": 119}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eenieeo8FLmIc+BfDvGty0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "__prefab": {"__id__": 50}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@075ae", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9/0yrKp1H0LV3L1LCcKnz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92oJykKYdPj43399IFMJMw"}, {"__type__": "cc.Node", "_name": "MainListArrowLeft", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 53}, {"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 60}, "_lpos": {"__type__": "cc.Vec3", "x": 569.392, "y": 6.333000000000027, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 54}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fHAGSUCFN/4A1kOM6mS5x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 56}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@32927", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53RFH+c5RERYrVWjcv9RQP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 52}, "_enabled": true, "__prefab": {"__id__": 58}, "clickEvents": [{"__id__": 59}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@32927", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9DeYlOyNLALU+hkE2NFZh"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "6e662xQs9xPfZ4Gn7vNo4u5", "handler": "OnClickNext", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d1ShN390ZIi4jfxFkQYtCq"}, {"__type__": "cc.Node", "_name": "MainListSV", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [{"__id__": 62}], "_active": true, "_components": [{"__id__": 165}, {"__id__": 167}], "_prefab": {"__id__": 169}, "_lpos": {"__type__": "cc.Vec3", "x": -26.82899999999995, "y": 35.00599999999997, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "ViewMask", "_objFlags": 0, "_parent": {"__id__": 61}, "_children": [{"__id__": 63}], "_active": true, "_components": [{"__id__": 160}, {"__id__": 162}], "_prefab": {"__id__": 164}, "_lpos": {"__type__": "cc.Vec3", "x": 30.074, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Layout", "_objFlags": 0, "_parent": {"__id__": 62}, "_children": [{"__id__": 64}, {"__id__": 77}, {"__id__": 90}, {"__id__": 103}, {"__id__": 116}, {"__id__": 129}, {"__id__": 142}], "_active": true, "_components": [{"__id__": 155}, {"__id__": 157}], "_prefab": {"__id__": 159}, "_lpos": {"__type__": "cc.Vec3", "x": 20.886, "y": 168.271, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 65}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 66}}, {"__type__": "cc.PrefabInstance", "fileId": "ad566VX6RFubgYRlGQL2U4", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 67}, {"__id__": 69}, {"__id__": 71}, {"__id__": 73}, {"__id__": 75}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_name"], "value": "MainListItem"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -362.5, "y": -75, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 78}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 79}}, {"__type__": "cc.PrefabInstance", "fileId": "c1nkZYlLlHtKpR2jJT6Pz0", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 80}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_name"], "value": "MainListItem-001"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -187.5, "y": -75, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 89}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 91}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 92}}, {"__type__": "cc.PrefabInstance", "fileId": "356j249rZB+Ze6e9m2slUL", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 93}, {"__id__": 95}, {"__id__": 97}, {"__id__": 99}, {"__id__": 101}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 94}, "propertyPath": ["_name"], "value": "MainListItem-002"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 96}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -12.5, "y": -75, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 98}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 100}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 102}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 104}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 103}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 105}}, {"__type__": "cc.PrefabInstance", "fileId": "d60T7iiJ9PA5yebEUBdsDp", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 106}, {"__id__": 108}, {"__id__": 110}, {"__id__": 112}, {"__id__": 114}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 107}, "propertyPath": ["_name"], "value": "MainListItem-003"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 109}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 162.5, "y": -75, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 111}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 113}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 115}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 117}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 116}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 118}}, {"__type__": "cc.PrefabInstance", "fileId": "bdzxCvN+5NuLKjGjMjVh7U", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 119}, {"__id__": 121}, {"__id__": 123}, {"__id__": 125}, {"__id__": 127}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 120}, "propertyPath": ["_name"], "value": "MainListItem-004"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 122}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 337.5, "y": -75, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 124}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 126}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 130}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 129}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 131}}, {"__type__": "cc.PrefabInstance", "fileId": "77izS9/ZRBVoTKat+FhQI+", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 132}, {"__id__": 134}, {"__id__": 136}, {"__id__": 138}, {"__id__": 140}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_name"], "value": "MainListItem-005"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -362.5, "y": -250, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 137}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 139}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 141}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 143}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 142}, "asset": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "fileId": "d8bLAzK6FAwrAejc4Rr8ac", "instance": {"__id__": 144}}, {"__type__": "cc.PrefabInstance", "fileId": "5c+2hPiQ1H8pu64iPnDiEU", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 145}, {"__id__": 147}, {"__id__": 149}, {"__id__": 151}, {"__id__": 153}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 146}, "propertyPath": ["_name"], "value": "MainListItem-006"}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 148}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -187.5, "y": -250, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["d8bLAzK6FAwrAejc4Rr8ac"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 42.5, "height": 75.6}}, {"__type__": "cc.TargetInfo", "localID": ["85XNOG+eJMa6iXZ4/+5Iy9"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 156}, "_contentSize": {"__type__": "cc.Size", "width": 875, "height": 325}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35/G2ShxpDxabLLlfYIdg+"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 158}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 25, "_spacingY": 25, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53/z+qnk1F45w/9jBATgNq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24izJBWa1HAZnVqAwW0cF5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 161}, "_contentSize": {"__type__": "cc.Size", "width": 980, "height": 506}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0tbKh8p1AeZdnpPPZbmQg"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 163}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_type": 0, "_inverted": false, "_segments": 64, "_spriteFrame": null, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3jQxOdFVN6YDvEmIrDr+A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9fSqK+7oRKjZDXH7rqGXRc"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 166}, "_contentSize": {"__type__": "cc.Size", "width": 875, "height": 506}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "219bQhRBdAj7bkY7iJxORM"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 168}, "bounceDuration": 1, "brake": 0.5, "elastic": true, "inertia": true, "horizontal": true, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": null, "_horizontalScrollBar": null, "_verticalScrollBar": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cgGN6CmFBXK3pOhyaAjGX"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5cpgK4wINDXYgFa9gaM47R"}, {"__type__": "cc.Node", "_name": "MainListArrowRight", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 173}, {"__id__": 175}], "_prefab": {"__id__": 178}, "_lpos": {"__type__": "cc.Vec3", "x": -569.217, "y": 6.333000000000027, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": -1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 172}, "_contentSize": {"__type__": "cc.Size", "width": 65, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23KblYbkVDvLKAs+DRiFBk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 174}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@32927", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73NE7g7J9BVaXBu0F65PWP"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 170}, "_enabled": true, "__prefab": {"__id__": 176}, "clickEvents": [{"__id__": 177}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@32927", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "501stOaiJLupQEJbsIats/"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "6e662xQs9xPfZ4Gn7vNo4u5", "handler": "OnClickLast", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fe3JD2JB5HUaazeiH9kZ5c"}, {"__type__": "cc.Node", "_name": "Title", "_objFlags": 0, "_parent": {"__id__": 39}, "_children": [{"__id__": 180}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 192}], "_prefab": {"__id__": 194}, "_lpos": {"__type__": "cc.Vec3", "x": -33.09699999999998, "y": -205.099, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "TitleLabel", "_objFlags": 0, "_parent": {"__id__": 179}, "_children": [], "_active": true, "_components": [{"__id__": 181}, {"__id__": 183}], "_prefab": {"__id__": 185}, "_lpos": {"__type__": "cc.Vec3", "x": 48.175, "y": 20.892, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 182}, "_contentSize": {"__type__": "cc.Size", "width": 102.19, "height": 75.6}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80tMoA/85MvpU+pyAtN+Ix"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 180}, "_enabled": true, "__prefab": {"__id__": 184}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_string": "1/90", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 55, "_fontSize": 55, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 60, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78n1MhEnZEs7wrm3hYuprB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dH6DRakxM5pMhGzmZv9t3"}, {"__type__": "cc.Node", "_name": "Key", "_objFlags": 0, "_parent": {"__id__": 179}, "_children": [], "_active": true, "_components": [{"__id__": 187}, {"__id__": 189}], "_prefab": {"__id__": 191}, "_lpos": {"__type__": "cc.Vec3", "x": -64.411, "y": 17.975, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 188}, "_contentSize": {"__type__": "cc.Size", "width": 83, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8e6NXZvDZFbLy0VBeN+R45"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 190}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3499b1a6-96ef-4853-810c-2a933cdb4617@8914a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71EDW9USpL773h/aMIDVYp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b5bC/XNN1DtLHPQvLC56rP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 179}, "_enabled": true, "__prefab": {"__id__": 193}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5A1kyRcNO0IhlVTe/tjf0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c3Xx3CtjdFqaoHeld0mnSo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 196}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bBQBSGGRPhqYpkz0YZmB7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "19pSZuMrZCxatdWVNtq/pi"}, {"__type__": "cc.Node", "_name": "Ground", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 199}], "_active": true, "_components": [{"__id__": 209}], "_prefab": {"__id__": 211}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 198}, "_prefab": {"__id__": 200}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 199}, "asset": {"__uuid__": "83dc858b-a6be-4e4b-b1ab-aa1edf76cb21", "__expectedType__": "cc.Prefab"}, "fileId": "98scW63RNJ6YVws8T+YH+b", "instance": {"__id__": 201}}, {"__type__": "cc.PrefabInstance", "fileId": "d43YXKNJtGMJqbWkaZvmg6", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 202}, {"__id__": 204}, {"__id__": 205}, {"__id__": 206}, {"__id__": 207}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_name"], "value": "Ground"}, {"__type__": "cc.TargetInfo", "localID": ["98scW63RNJ6YVws8T+YH+b"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -333.853, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 203}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 208}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -196.081, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["e4aqH8dDNPIoiYa8C/NgSK"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 198}, "_enabled": true, "__prefab": {"__id__": 210}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "61Tp5aEgVBkaw9aPirWKlx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "92j/rfcmBJ4LMWG0vpra/8"}, {"__type__": "cc.Node", "_name": "back", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 213}, {"__id__": 215}, {"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 222}, "_lpos": {"__type__": "cc.Vec3", "x": -575.616, "y": 299.459, "z": 1}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 214}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bam1AWiZtPapoVZSdlVmC/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 216}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1c20c37e-e994-43d4-9f0f-927c850f7904@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27dy3Qb2ZDA6rd9/A6oQ1r"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 218}, "_alignFlags": 9, "_target": {"__id__": 1}, "_left": 16.884000000000015, "_right": 0, "_top": 19.040999999999997, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caHJpqWVFCr7/powGhFVR8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 212}, "_enabled": true, "__prefab": {"__id__": 220}, "clickEvents": [{"__id__": 221}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "1c20c37e-e994-43d4-9f0f-927c850f7904@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fHqx494lAmJV6KVsmZf0H"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "6e662xQs9xPfZ4Gn7vNo4u5", "handler": "clickBackMainCanvas", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16JKV4EdBJpIH3SghuhPXT"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 224}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9SQxkdJxIdr7++IJRVrZo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91PUw41SJJkKXGhB3pA0Iu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 227}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8236R4W1FFfbTF0UPfAF3Z"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 229}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5cw1/UfdD0bqw1Zf0ZYDx"}, {"__type__": "6e662xQs9xPfZ4Gn7vNo4u5", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 231}, "zIndex": 100, "MainListSV": {"__id__": 167}, "MainListLayout": {"__id__": 157}, "MainListItemPrefab": {"__uuid__": "5bf7a36f-7cd1-410c-aa3b-f64f3761b560", "__expectedType__": "cc.Prefab"}, "TitleLabel": {"__id__": 183}, "CloudNodes": [{"__id__": 12}, {"__id__": 18}, {"__id__": 24}, {"__id__": 30}], "Clouds": [{"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@d7091", "__expectedType__": "cc.SpriteFrame"}, {"__uuid__": "8b0fd669-fa92-4534-9145-d8388ffcb510@75e6c", "__expectedType__": "cc.SpriteFrame"}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07QGuY1CNPR4OU8FNjBs5t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74ap5x21tIuLr2J4ymU6en", "nestedPrefabInstanceRoots": [{"__id__": 64}, {"__id__": 77}, {"__id__": 90}, {"__id__": 103}, {"__id__": 116}, {"__id__": 129}, {"__id__": 142}, {"__id__": 199}]}]