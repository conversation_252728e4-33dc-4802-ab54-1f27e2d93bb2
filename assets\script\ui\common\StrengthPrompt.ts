import {_decorator, Slider,ProgressBar } from 'cc';
import {dialogBase} from "../../framework/dialogBase";
import {Constants} from "../../game/Constants";
import {uiManager} from "../../framework/uiManager";
import {Public} from "../../game/Public";
import {AdCtl} from "../../channel/AdCtl";
import {comm} from "../../framework/comm";
import {gameStorage} from "../../framework/gameStorage";
import {clientEvent} from "../../framework/clientEvent";
import {DataCtl} from "../../game/DataCtl";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Success
 * DateTime = Mon Feb 28 2022 17:54:00 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Success.ts
 * FileBasenameNoExtension = Success
 * URL = db://assets/script/ui/common/Success.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('StrengthPrompt')
export class StrengthPrompt extends dialogBase {
    @property(Slider)
    Slider:Slider = null!
    @property(ProgressBar)
    Progress:ProgressBar = null!

    show(args){
        this.Slider.enabled=false
        let adTime = gameStorage.getInt(Constants.CacheDataKey.strengthAdTimes, 0)
        this.setProgress(adTime)
    }

    hide(){
        AdCtl.instance.HideBannerAD()
    }

    setProgress(adTime){
        if(adTime<=0){
            this.Slider.progress = 0
            this.Progress.progress = 0
        }else if(adTime==1){
            this.Slider.progress = 0.5
            this.Progress.progress = 0.5
        }else{
            this.Slider.progress = 1
            this.Progress.progress = 1
        }
    }

    OnClickGetStrength(){
        AdCtl.instance.ShowRewardVideoAd(()=>{
            let adTime = gameStorage.getInt(Constants.CacheDataKey.strengthAdTimes, 0)
            adTime+=1
            this.setProgress(adTime)
            gameStorage.setInt(Constants.CacheDataKey.strengthAdTimes, adTime)
            // this.close()

            //如果为1，那么加5，其他加999
            let strength = gameStorage.getInt(Constants.CacheDataKey.strength,0)
            if(adTime==1){
                DataCtl.instance.AddStrength(5)
            }else if(adTime>=2){
                strength=999
                gameStorage.setInt(Constants.CacheDataKey.strength, strength)
            }
            clientEvent.dispatchEvent(Constants.Events.updateStrength)
        }, ()=>{

        })
    }


}

