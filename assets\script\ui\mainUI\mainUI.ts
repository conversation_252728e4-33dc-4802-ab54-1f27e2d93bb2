// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, Label, Animation, find, Sprite, Node, Vec3, SpriteFrame, tween, sys, ScrollView, Layout, Prefab} from "cc";
import { clientEvent } from "../../framework/clientEvent";
import { uiManager } from "../../framework/uiManager";
import {poolManager} from "../../framework/poolManager";
import {configuration} from "../../framework/configuration";
import {Constants} from "../../game/Constants";
import {Public} from "../../game/Public";
import {MainListItem} from "../../items/MainListItem";
import {dialogBase} from "../../framework/dialogBase";
import {loading} from "../common/loading";
import {Wechat} from "../../channel/wechat/wechat";
import {AdCtl} from "../../channel/AdCtl";
const { ccclass, property } = _decorator;

@ccclass("mainUI")
export class mainUI extends dialogBase {
    /* class member could be defined like this */
    // dummy = '';
    // @property(Sprite)
    // spIcon: Sprite = null!;
    //
    // @property(Label)
    // lbGold: Label = null!;
    //
    // @property(Node)
    // nodeBtnService: Node = null!;
    //
    // //签到红点提示
    // @property(Node)
    // nodeSignInRedDot: Node = null!;
    //
    // @property(Node)
    // nodeGoldIcon: Node = null!;
    //
    // @property(Node)
    // nodeShopRedDot: Node = null!;

    @property(ScrollView)
    MainListSV:ScrollView = null;
    @property(Layout)
    MainListLayout:Layout = null;
    @property(Prefab)
    MainListItemPrefab:Prefab = null;
    @property(Label)
    TitleLabel:Label = null;
    @property({type:Node, tooltip:"云的节点"})
    CloudNodes: Node[] = [null,null,null,null]

    @property({type:SpriteFrame,tooltip:"随机2朵云"})
    Clouds: SpriteFrame[] = []

    curPage:number=1
    pageSize:number =10
    cacheItems:Node[] = []


    onEnable () {
        clientEvent.on(Constants.Events.showList, this.showListEvent, this);
    }

    onDisable () {
        clientEvent.off(Constants.Events.showList, this.showListEvent, this);
    }

    show(){
        this.showList(this.curPage)
        // AdCtl.instance.ShowCustomAdMainUI()
        // AdCtl.instance.ShowMainCanvas()
        // AdCtl.instance.HideAllCustomAD()
        // AdCtl.instance.HideCustomAdMainUI()
        // AdCtl.instance.HideMainCanvas()
        AdCtl.instance.ShowBannerAd()
        // AdCtl.instance.ShowInterstitialAd(()=>{
        //     AdCtl.instance.ShowCustomAdMainUI()
        //     AdCtl.instance.ShowMainCanvas()
        // })
    }
    hide(){
        // AdCtl.instance.HideCustomAdMainUI()
        // AdCtl.instance.HideMainCanvas()
        AdCtl.instance.HideBannerAD()
    }
    showAD2(){

    }
    start () {
        // Your initialization goes here.

        //界面启动后表示登录完了
        // gameLogic.afterLogin();

        // this.updateSignIn();

        this.randomInitCloudsPos()

        // this.MainListLayout.node.destroyAllChildren()
        // this.showList(this.curPage)

        if(Public.GetTotalBreakLevel()==0){
            Public.enterLevel(1)
        }
    }

    clickBackMainCanvas(){
        uiManager.instance.hideDialog(Constants.Dialogs.mainUI)
        find("Canvas").getChildByName("loading").active = true
        find("Canvas").getChildByName("loading").getComponent(loading).showBtn()
        AdCtl.instance.HideCustomAdMainUI()
        AdCtl.instance.HideMainCanvas()
        AdCtl.instance.ShowMainCanvas()
        Public.isShowMainCanvas = true
    }

    randomInitCloudsPos(){ //随机的设置云的起始位置
        for(let i=0; i<this.CloudNodes.length; i++){
            let x = Public.RanInt(-800, 800)
            let cNode = this.CloudNodes[i]
            // console.log("=======1", cNode.position)
            cNode.setPosition(new Vec3(x, cNode.position.y, cNode.position.z));
            let which = Public.RanInt(0,1)
            cNode.getComponent(Sprite).spriteFrame = this.Clouds[which]
            // cNode.setPosition(new Vec3(0,0,0));
            // console.log("=======2", cNode.position)
            this.runCircle(cNode)
        }
        //让云无限往左边移动，到顶后从左边出来，速度各异。
    }

    runCircle(cNode:Node){
        let time = Public.RanInt(30,40)
        time = Math.abs(Math.abs(cNode.position.x)/900) * time
        tween(cNode)
            .to(time, {position:new Vec3(-900, cNode.position.y, cNode.position.z)})
            .call(()=>{
                cNode.setPosition(new Vec3(900, cNode.position.y, cNode.position.z));
                this.runCircle(cNode)
        }).start()
    }

    showListEvent(){
        this.showList(this.curPage)
    }

    showList(page:number){
        //放入缓存池
        if(this.cacheItems.length>0){
            for(let i=0; i<this.cacheItems.length; i++){
                poolManager.instance.putNode(this.cacheItems[i])
            }
            this.cacheItems = []
        }

        //清空节点
        this.MainListLayout.node.destroyAllChildren()


        //设置分页数据
        let start = this.pageSize*(page-1)+1
        let end = this.pageSize*page+1
        for(let i=start; i<end; i++){
            if(i<=0||i>Constants.TotalLevelNum) continue
            // console.log("start---",start, end, i)
            let item = poolManager.instance.getNode(this.MainListItemPrefab, this.MainListLayout.node)
            item.getComponent(MainListItem).Init({levelNum:i})
            this.cacheItems.push(item)
        }
        // console.log(this.cacheItems.length+"------------")

        //设置页头
        let breakCnt = Public.GetTotalBreakLevel()
        this.TitleLabel.string = `${breakCnt}/${Constants.TotalLevelNum}`
    }

    OnClickNext(){
        if(this.curPage*this.pageSize<Constants.TotalLevelNum){
            this.curPage += 1
            this.showList(this.curPage)
        }
    }

    OnClickLast(){
        if(this.curPage>1){
            this.curPage -= 1
            this.showList(this.curPage)
        }
    }

    resetPlayData(){
        sys.localStorage.removeItem('CarConfig');
        uiManager.instance.showTips('数据重置成功！');
    }
}
