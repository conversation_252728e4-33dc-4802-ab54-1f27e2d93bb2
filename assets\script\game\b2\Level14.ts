
import { _decorator, UITransform, Node , tween, Vec3, Collider2D, Contact2DType, v2, PHYSICS_2D_PTM_RATIO, RigidBody2D} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level14')
export class Level14 extends LevelDialogBase {

    @property(Node)
    PricksNode:Node = null

    @property(Node)
    TopHide1:Node = null
    @property(Node)
    TopHide2:Node = null
    @property(Node)
    TopHide3:Node = null

    isTouchWord1:boolean = false
    isTouchWord2:boolean = false
    isTouchWord3:boolean = false

    start(){
        super.start()
        this.PricksNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onPricksNodeContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);

    }

    show(params){
        super.show(params)
        super.superShow()

        this.isLocked = true
        this.Key.active = false
        this.TopHide1.getChildByName("Top1").active = false
        this.TopHide2.getChildByName("Top2").active = false
        this.TopHide3.getChildByName("Top3").active = false
        this.showKey()
    }

    //碰到尖刺就死
    onPricksNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        if(otherCollider.node.name=="RoleNode"){
            this.scheduleOnce(()=>{
                this.Lose()
            },0.1)
        }
    }

    touchStart(event){
        super.touchStart(event)
        let pos = event.getUILocation()
        if(Public.IsPointInNodeArea2D(this.TopHide1, pos)){
            this.TopHide1.getChildByName("Top1").active=true
            this.isTouchWord1 = true
        }
        if(Public.IsPointInNodeArea2D(this.TopHide2, pos)){
            this.TopHide2.getChildByName("Top2").active=true
            this.isTouchWord2 = true
        }
        if(Public.IsPointInNodeArea2D(this.TopHide3, pos)){
            this.TopHide3.getChildByName("Top3").active=true
            this.isTouchWord3 = true
        }
    }

    touchMove(event) {
        super.touchMove(event)
        if(this.isTouchWord1==true){
            let pos = this.TopHide1.position
            let ePos = event.getUIDelta()
            this.TopHide1.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }
        if(this.isTouchWord2==true){
            let pos = this.TopHide2.position
            let ePos = event.getUIDelta()
            this.TopHide2.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }
        if(this.isTouchWord3==true){
            let pos = this.TopHide3.position
            let ePos = event.getUIDelta()
            this.TopHide3.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }

    }

    touchEnd(event) {
        super.touchEnd(event)
        if(this.isTouchWord1==true){
            this.isTouchWord1 = false
        }
        if(this.isTouchWord2==true){
            this.isTouchWord2 = false
        }
        if(this.isTouchWord3==true){
            this.isTouchWord3 = false
        }
    }



}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
