
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, EventTouch, UITransform, UIOpacity, RigidBody2D, Sprite, SpriteFrame} from 'cc';
import {Constants} from "../Constants";
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {uiManager} from "../../framework/uiManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level7')
export class Level7 extends LevelDialogBase {

    //画框节点
    @property(Node)
    PicNode:Node = null

    //蛇加上4只脚
    @property(Node)
    SnakeNode:Node = null

    //龙的动画
    @property(Node)
    DragonNode:Node = null

    //灯笼节点
    @property(Node)
    LightNodes:Node[] = []

    //是否触碰到灯笼的标志
    isLightTouches:boolean[] = [false, false, false, false]
    lightNodeIdx:number=-1
    //脚的数量
    legsCnt:number = 0
    isTouchPic:boolean = false
    touchPicPath = []

    start(){
        super.start()
        //画框需要判断
        this.PicNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onPicContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        // super.superShow()
        // let order = localConfig.instance.getTable("order")
        this.levelLabel.string = `第${Public.CurLevelNum}关`
        uiManager.instance.showDialog(Constants.Dialogs.ops, {offsetVec:new Vec3(470,10,0)})


        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        this.SnakeNode.active = false
        this.DragonNode.active = false
        // this.waterPosition = this.WaterLabel.position  //设置水的初始位置
    }

    checkWin(){
        if(this.legsCnt==4){
            //消除画框，然后显示龙，最后掉出钥匙
            audioManager.instance.playSound(Constants.Sounds.disappear)
            tween(this.PicNode.getComponent(UIOpacity)).to(1, {opacity:0}).call(()=>{
                this.PicNode.active = false
                this.DragonNode.active = true
                this.DragonNode.setScale(new Vec3(0.5,0.5,1))
                audioManager.instance.playSound(Constants.Sounds.appear)
                tween(this.DragonNode).to(1,{scale:new Vec3(1,1,1)}).start()
                tween(this.DragonNode.getChildByName("Light"))
                    .to(2,{angle:360}, {easing:"cubicOut"})
                    .call(()=>{
                        this.DragonNode.getChildByName("Light").active = false
                        this.DragonNode.getChildByName("Mask").active = false
                        audioManager.instance.playSound(Constants.Sounds.dragon)
                        this.scheduleOnce(()=>{
                            this.showKey()
                        },2)
                    }).start()
            }).start()

        }
    }

    onPicContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.PicNode.position.y
            tween(this.PicNode)
                .to(0.05,{position:new Vec3(this.PicNode.position.x,y+10,this.PicNode.position.z)})
                .to(0.1,{position:new Vec3(this.PicNode.position.x,y,this.PicNode.position.z)})
                .call(()=>{
                    this.checkWin()
                })
                .start()
        })
    }


    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        //判断画框手指的滑动行为
        if(this.SnakeNode.active==false && Public.IsPointInNodeArea2D(this.PicNode, pos)==true){
            this.isTouchPic = true
        }else{
            this.lightNodeIdx = -1
            //判断灯笼的位置
            for(let i=0; i<this.LightNodes.length; i++){
                if(this.LightNodes[i].active == true){
                    // const nodeUIT = this.LightNodes[i].getComponent(UITransform)!
                    // let wordPos = nodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
                    if(Public.IsPointInNodeArea2D(this.LightNodes[i], pos)){
                        this.isLightTouches[i] = true
                        this.lightNodeIdx = i
                        break
                    }
                }
            }
            // if(this.SnakeNode.)
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.SnakeNode.active==false){
            if(this.isTouchPic==true){
                let pos = event.getLocation()
                // console.log("-----------isTouchPic move---", pos)
                if(this.touchPicPath.length<50){
                    this.touchPicPath.push(pos)
                }
            }
        }else{
            if(this.lightNodeIdx!=-1){
                let n = this.LightNodes[this.lightNodeIdx]
                let pos = n.position
                let ePos = event.getUIDelta()
                n.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
            }
        }

    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.SnakeNode.active==false){
            if(this.isTouchPic==true){
                this.isTouchPic=false
                //便利所有点，如果距离和第一个点大于，100并且在格子内，那么就ok
                for(let i=0; i<this.touchPicPath.length; i++){
                    let dis = Public.GetDistance2D(this.touchPicPath[0], this.touchPicPath[i])
                    // console.log("-----------isTouchPic dis---", dis, i, this.touchPicPath.length)
                    if(dis>100){
                        audioManager.instance.playSound(Constants.Sounds.prompt)
                        this.SnakeNode.active= true
                        for(let j=0; j<4; j++){
                            this.SnakeNode.getChildByName("Leg"+(j+1)).active = false
                        }
                        // console.log("-----------isTouchPic bingo---", dis, i, this.touchPicPath.length)
                        break
                    }
                }
                // console.log("-----------isTouchPic end---", this.touchPicPath)
                this.touchPicPath=[]
            }
        }else{
            //如果拖到方框内，那么就隐藏触发
            if(this.lightNodeIdx!=-1){
                let lightNode = this.LightNodes[this.lightNodeIdx]
                if(lightNode.active==true){
                    const nodeUIT = lightNode.getComponent(UITransform)!
                    let wordPos = nodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
                    if(Public.IsPointInNodeArea2D(this.PicNode, wordPos)==true){
                        lightNode.active = false
                        audioManager.instance.playSound(Constants.Sounds.prompt)
                        this.legsCnt ++
                        this.SnakeNode.getChildByName("Leg"+this.legsCnt).active = true
                        console.log("-----------isTouchLight end---", wordPos)
                    }
                }
            }

        }

    }


}
