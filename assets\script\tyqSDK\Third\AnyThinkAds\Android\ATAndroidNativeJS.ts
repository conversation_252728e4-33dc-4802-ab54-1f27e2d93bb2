var classJavaName = "com/anythink/cocosjs/ATNativeJSBridge";
var ATAndroidBannerJS = ATAndroidBannerJS || {

    loadNative: function (placementId, settings) {
        console.log("Android-loadNative");
        jsb.reflection.callStaticMethod(classJavaName, "load", "(Ljava/lang/String;Ljava/lang/String;)V", placementId, settings);
    },

    setAdListener: function (listener) {
        console.log("Android-setAdListener");
        jsb.reflection.callStaticMethod(classJavaName, "setAdListener", "(Ljava/lang/String;)V", listener);
    },

    hasAdReady: function (placementId) {
        console.log("Android-hasAdReady");
        return jsb.reflection.callStaticMethod(classJavaName, "isAdReady", "(Ljava/lang/String;)Z", placementId);;
    },

    checkAdStatus: function (placementId) {
        console.log("Android-checkAdStatus:" + placementId);
        return jsb.reflection.callStaticMethod(classJavaName, "checkAdStatus", "(Ljava/lang/String;)Ljava/lang/String;", placementId);
    },

    showAd: function (placementId, adViewProperty) {
        console.log("Android-showAd");
        jsb.reflection.callStaticMethod(classJavaName, "show", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, adViewProperty, "");
    },

    showAdInScenario: function (placementId, adViewProperty, scenario) {
        console.log("Android-showAdInScenario");
        jsb.reflection.callStaticMethod(classJavaName, "show", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V", placementId, adViewProperty, scenario);
    },

    removeAd: function (placementId) {
        console.log("Android-removeAd");
        jsb.reflection.callStaticMethod(classJavaName, "remove", "(Ljava/lang/String;)V", placementId);
    }

};

export default ATAndroidBannerJS;