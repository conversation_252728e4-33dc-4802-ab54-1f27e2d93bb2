import { _decorator, Component , director} from 'cc';
import {Constants} from "../../game/Constants";

export interface IPos {
    left?: number;
    right?: number;
    top?: number;
    bottom?: number;
    centerX?: number;
    centerY?: number;
}

export enum EType {
    // * @param type 1矩阵；2横向；3竖向；4单格子
    rect = 1,
    horizontal,
    vertical,
    grid,
}

//默认垂直水平居中
export interface IPosMode{
    posCenterMode?:PosCenterMode,//居中模式，水平居中，垂直居中，center为水平垂直居中
    posCenterSideMode?:PosCenterSideMode,//水平居中靠上下，垂直居中靠左右，center不需要配置sideMode
    offsetX?:number, //x偏移正负
    offsetY?:number //y偏移正负
    winWidth: number, //窗口的宽
    winHeight: number, //窗口的高
}
//居中模式，水平居中，垂直居中，center为水平垂直居中
export enum PosCenterMode {
    horizontalCenter=1,
    verticalCenter,
    center
}
//单个居中靠边模式
export enum PosCenterSideMode {
    up=1,
    down,
    left,
    right,
}


//必须选择一遍靠，然后加偏移值
export interface IPosSideMode{
    sideMode:number,//靠哪边
    offsetX?:number, //x偏移正负
    offsetY?:number //y偏移正负
}
//靠边模式
export enum PosSideMode {
    leftTop=100,//左上开始
    leftBottom, //左下开始
    rightTop, //右上开始
    rightBottom, //右下开始
}

export class WXCustomAd {

    /**原生模板广告矩阵样式id	 */
    // private static _customc_rect_adunit = 'adunit-fbc5aa028cce8b79';
    //
    // /**原生模板广告横向样式id	 */
    // private static _customc_h_adunit = 'adunit-99a24d8297656974';
    //
    // /**原生模板广告竖向样式id	 */
    // private static _customc_v_adunit = 'adunit-661958fb25df4b1b';
    //
    // /**原生模板广告单个样式id	 */
    // private static _customc_one_adunit = 'adunit-2f718464770ec90b';

    /**广告缓存 */
    private static _ads = {};

    /** */
    private static _waits = [];



    // static setadunit(rectid, hid, vid, oneid) {
    //     this._customc_rect_adunit = rectid;
    //     this._customc_h_adunit = hid;
    //     this._customc_v_adunit = vid;
    //     this._customc_one_adunit = oneid;
    // }


    /**
     * 创建一个原生广告对象
     * @param flag 创建来源标识（用标识来控制显示和隐藏）
     * @param adInfo 配置信息
     * @param posMode 位置模式
     * @param adInterval 广告间隔
     * @param show 广告间隔
     * @returns
     */
    static createCustomAd(flag, adInfo, adInterval:number=30, show:boolean=true) {
        // if (!window.wx) return;
        return new Promise<void>((resolve,reject)=>{
            let sysInfo = wx.getSystemInfoSync();
            let version = sysInfo.SDKVersion;
            let ad = null;
            let self = this;

            //缓存有
            if (this._ads[flag]) {
                ad = this._ads[flag];
                console.log("缓存有",flag);
                if(show==true){
                    if(ad==null){
                        resolve()
                        return
                    }
                    if (ad.isShow()==false){
                        ad.show().then(()=>{
                            resolve()
                        }).catch(res=>{
                            resolve()
                            console.log("!!!show err----------", res)
                        })
                    }else{
                        resolve()
                    }
                }else{
                    resolve()
                }

            }else if (this.compareVersion(version, '2.11.1') >= 0) {
                self._ads[flag] = null;
                // console.log("显示原生模板广告", flag, this._ads);
                let posMode = {
                    posCenterMode: adInfo.mode,
                    posCenterSideMode: adInfo.sideMode,
                    offsetX:adInfo.x, //x偏移正负
                    offsetY:adInfo.y, //y偏移正负
                    winWidth: adInfo.w, //窗口的宽
                    winHeight: adInfo.h, //窗口的高
                } as IPosMode
                let pos = this.getRealPos(posMode);
                console.warn("@@@创建原生模板广告" + flag);
                // let p = self.getPos(pos);
                ad = wx.createCustomAd({
                    adUnitId: adInfo.id,
                    adIntervals: adInterval,
                    style: {
                        top: pos.top,
                        left: pos.left,
                        width: posMode.winWidth, // 用于设置组件宽度，只有部分模板才支持，如矩阵格子模板
                        fixed: false // fixed 只适用于小程序环境
                    }
                })

                ad.onLoad(() => {
                    self._ads[flag] = ad;
                    // let a1 = self._waits.filter(x => x == flag);
                    // console.log("原生模板广告加载完成：" + flag, self._waits, a1);
                    // if (a1.length == 0)
                    if(show){
                        ad.show().then(()=>{
                            resolve()
                        }).catch(()=>{
                            resolve()
                        });
                    }else{
                        resolve()
                    }
                    // else {
                    //     self._waits = self._waits.filter(x => x != flag);
                    //     ad.hide();
                    // }
                });

                ad.onError((err)=>{
                    console.error("-------原始广告加载失败--",flag, err)
                    delete this._ads[flag]
                    resolve()
                })

                //关闭后就不再出现了。！！
                ad.onClose(()=>{
                    delete this._ads[flag]
                    console.log("---------关闭原生广告--", flag )
                })
                // ad.onError(self.onError);
                // ad.onClose(self.onClose);
            } else {
                // 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
                // wx.showModal({
                //     title: '提示',
                //     content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
                // })
            }

        })

    }

    static getRealPos(posMode: IPosMode) {
        let sysInfo = wx.getSystemInfoSync();
        //居中模式
        let pos = {left:0, top:0}
        // if (posMode.posCenterMode == PosCenterMode.center) {
        if(posMode.posCenterMode==PosCenterMode.center){ //水平居中 垂直居中
            pos.left = sysInfo.screenWidth / 2 - posMode.winWidth / 2
            pos.top = sysInfo.screenHeight / 2 - posMode.winHeight / 2

        }else if (posMode.posCenterMode == PosCenterMode.horizontalCenter) { //水平居中
            pos.left = sysInfo.screenWidth / 2 - posMode.winWidth / 2
            if(posMode.posCenterSideMode == PosCenterSideMode.up){
                pos.top = 0
            }else if(posMode.posCenterSideMode ==PosCenterSideMode.down){
                pos.top = sysInfo.screenHeight - posMode.winHeight
            }

        } else if (posMode.posCenterMode == PosCenterMode.verticalCenter) { //垂直居中
            pos.top = sysInfo.screenHeight / 2 - posMode.winHeight / 2
            if(posMode.posCenterSideMode==PosCenterSideMode.left){
                pos.left = 0
            }else if(posMode.posCenterSideMode==PosCenterSideMode.right){
                pos.left = sysInfo.screenWidth - posMode.winWidth
            }
            console.log("@@@@-----mode------verticalCenter", pos)
        }
        pos.left += posMode.offsetX
        pos.top += posMode.offsetY

        // }
        // else if (posMode.whichMode == IPosModeVal.sideMode) {
        //     let mode = posMode.posSideMode
        //     if (mode.sideMode == PosSideMode.leftTop) {
        //
        //     } else if (mode.sideMode == PosSideMode.leftBottom) {
        //         pos.top = sysInfo.screenHeight - posMode.winHeight
        //     } else if (mode.sideMode == PosSideMode.rightTop) {
        //         pos.left = sysInfo.screenWidth - posMode.winWidth
        //     } else if (mode.sideMode == PosSideMode.rightBottom) {
        //         pos.top = sysInfo.screenHeight - posMode.winHeight
        //         pos.left = sysInfo.screenWidth - posMode.winWidth
        //     }
        //     pos.left += mode.offsetX
        //     pos.top += mode.offsetY
        // }
        return pos
    }

    /**
     * 隐藏原生模板广告
     * @param flag
     */
    public static hideCustomAd(flag) {
        let ad = null;
        console.log("隐藏原生模板广告", flag, this._ads, this._waits);
        if (this._ads[flag]) {
            ad = this._ads[flag];
            if (ad && ad.isShow()){
                ad.hide().catch(()=>{
                    console.error("---隐藏不掉原生广告--------")
                    // ad.hide()
                    setTimeout(()=>{
                        this.hideCustomAd(flag)
                    },100)
                });
            }

        } else {
            // this._waits.push(flag);
        }
    }

    /**
     * 隐藏所有的flag，然后返回所有的flag，下次一口气show出来
     */
    private static _tmpFlags = []
    public static hideCustomAdAll():void {
        let res = []
        for(let flag in this._ads){
            let ad = this._ads[flag]
            console.log("----统一处理隐藏--",flag)
            if(ad && ad.isShow()==true){
                ad.hide().catch(()=>{
                    console.error("---隐藏不掉原生广告--------")
                    ad.hide()
                });
                res.push(flag)
            }
        }
        this._tmpFlags = res
    }

    /**
     * 显示所有刚刚关闭的原生广告
     */
    public static showCustomAdAll():void {
        let flags = this._tmpFlags
        for(let i=0; i<flags.length; i++){
            let flag = flags[i]
            let ad = this._ads[flag]
            console.log("----统一处理显示--",flag)
            if(ad && ad.isShow()==false){
                ad.show()
            }
        }
        this._tmpFlags = []
    }


    public static destroyCustomAd(flag) {
        if (this._ads[flag]) {
            this._ads[flag].destroy();
            this._ads[flag] = null;
        }
    }
    private static onError(err) {
        console.log("广告加载失败！！！！！！",err);
    }

    private static onLoad() {
        console.log('多格子原生广告加载成功')
    }

    private static onClose() {
        console.log('原生模板广告关闭')
    }

    private static compareVersion(v1, v2) {
        v1 = v1.split('.')
        v2 = v2.split('.')
        const len = Math.max(v1.length, v2.length)

        while (v1.length < len) {
            v1.push('0')
        }
        while (v2.length < len) {
            v2.push('0')
        }

        for (let i = 0; i < len; i++) {
            const num1 = parseInt(v1[i])
            const num2 = parseInt(v2[i])

            if (num1 > num2) {
                return 1
            } else if (num1 < num2) {
                return -1
            }
        }

        return 0
    }

    /**
     * 获取实际的位置
     * @param type 模板类型
     * @param pos 游戏中的相对位置
     * @returns
     */
    private static getPos(type, pos: IPos) {
        let sysInfo = wx.getSystemInfoSync();
        let w = 0, h = 0;

        if (type == 1) { w = 330; h = 360; }
        if (type == 2) { w = 360; h = 106; }
        if (type == 3) { w = 72; h = 250; }
        if (type == 4) { w = 68; h = 106; }


        // if (type == 1) { w = 330; h = 360; };
        // if (type == 2) { w = 72; h = 250; };
        // if (type == 3) { w = 72; h = 250; };
        // if (type == 4) { w = 68; h = 106; };


        let sc_w = sysInfo.windowWidth/sysInfo.windowWidth  /// director.getWinSize().width.width;
        let sc_h = sysInfo.windowHeight/sysInfo.windowHeight /// director.getWinSize().width.height;

        let l = (sysInfo.windowWidth - w) / 2;
        let t = (sysInfo.windowHeight - h) / 2;
        if (pos && pos.left != undefined) l = pos.left * sc_w;
        if (pos && pos.top != undefined) t = pos.top * sc_h;
        if (pos && pos.right != undefined) l = sysInfo.windowWidth - w - pos.right * sc_w;
        if (pos && pos.bottom != undefined) t = sysInfo.windowHeight - h - pos.bottom * sc_h;
        if (pos && pos.centerX != undefined) l = (sysInfo.windowWidth - w) / 2 + pos.centerX * sc_w;
        if (pos && pos.centerY != undefined) t = (sysInfo.windowHeight - h) / 2 + pos.centerY * sc_h;

        return {
            left: l,
            top: t
        }
    }
    public static hideAllAd() {
        for (const key in this._ads) {
            this.hideCustomAd(key);
        }
    }
}