
import { _decorator, UITransform, Node , tween, Vec3, Collider2D, Contact2DType, v2, EventTouch, sp} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level15')
export class Level15 extends LevelDialogBase {

    @property(Node)
    BubblesNodes:Node[] = [null, null, null, null, null, null, null]
    @property(Node)
    Place1Node:Node = null
    @property(Node)
    Place2Node:Node = null

    @property(Node)
    MoveNode:Node[] = [null,null]

    @property(Node)
    Water:Node = null
    @property(Node)
    StartNode:Node = null
    @property(sp.Skeleton)
    DragonNode:sp.Skeleton = null

    isTouchDragon:boolean = false
    intervalTime:number = 0.8
    calTime:number = 0
    calCheckBubble:number = 0
    bubbleCnt:number = 0

    //抓住玩家的气泡编号
    catchBubbleNum:number = -1

    isTouchWater:boolean = false
    isUp:boolean = false
    touchPicPath=[]

    isTouchMouth:boolean = false
    isShoutOut:boolean = false
    isTouchMouthTime:number = 0


    start(){
        super.start()
        this.isShowDieAni = false
        //水需要判断
        this.Water.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onWaterContact, this);
        this.Place1Node.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onPlaceContact, this);
        this.Place2Node.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onPlaceContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        this.isLocked = true
        this.Key.active = false
        this.showKey()

        Public.MoveWater(this.Water, this.Water.getChildByName("Wave"))
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        this.calTime+= deltaTime
        this.calCheckBubble += deltaTime

        //轮流释放气泡
        if(this.isTouchMouth==false && this.calTime>this.intervalTime && this.isInWater==true && this.isTouchDragon==false){
            this.calTime = 0
            let bNode = this.BubblesNodes[this.bubbleCnt%7]
            bNode.active = true
            bNode.position = new Vec3(this.StartNode.position.x, this.StartNode.position.y, this.StartNode.position.z)

            // tween(bNode).delay(0.1).call(()=>{
            // }).start()
            let bPos = bNode.position
            bNode.setScale(new Vec3(0.3,0.3,1))
            tween(bNode).delay(0.2).to(0.3, {position:new Vec3(bPos.x-30, bPos.y-30, bPos.z)}).call(()=>{
                let dx = Public.RanInt(-10,180)
                let dy = Public.RanInt(-10,180)
                let endX = Public.RanInt(-190,190)
                Public.Bezier2DShowPoint(bNode, new Vec3(endX,960,0), 0.5, dx, dy, 3, ()=>{
                    // bNode.active = false
                })
            }).to(1,{scale:new Vec3(1,1,1)}).start()
            this.bubbleCnt+=1
        }
        //判断泡泡是否靠近玩家
        if(this.isInWater && this.calCheckBubble/0.2>=1 && this.isStop==false){
            this.calCheckBubble = 0
            // console.log("-------bubble0----", this.BubblesNodes)
            for(let i=0; i<this.BubblesNodes.length; i++){
                let bubble = this.BubblesNodes[i]
                // console.log("-------bubble1----", i, bubble)
                if(bubble!=undefined && bubble.active == true){
                    // console.log("-------bubble2----", bubble)
                    let roleWPos = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                    let bubbleWPos = bubble.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                    if(Public.GetDistance2D(roleWPos, bubbleWPos)<138){
                        // console.log("-------bubble3----", bubble)
                        this.catchBubbleNum = i
                        this.isStop = true
                        this.scheduleOnce(()=>{
                            this.Lose()
                        },0.1)
                        this.scheduleOnce(()=>{
                            this.roleNode.active = false
                        },3)
                    }
                }
            }
        }

        if(this.catchBubbleNum!=-1){
            //TODO 估计是引擎bug，移动节点的位置，子节点如果是spine会出现位置便宜。
            let pos = this.BubblesNodes[this.catchBubbleNum].getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            // console.log("-------bubble i----", this.catchBubbleNum)
            // let nodePos = this.roleNode.parent.getComponent(UITransform).convertToNodeSpaceAR(pos)
            // this.roleNode.setPosition(new Vec3(nodePos.x, nodePos.y, nodePos.z))
            let nodePos2 = this.roleNode.getComponent(UITransform).convertToNodeSpaceAR(pos)
            let spine = this.roleNode.getChildByName("RoleSpine")
            spine.setPosition(new Vec3(nodePos2.x, nodePos2.y-50, nodePos2.z))
            // console.log("-------bubble----", pos, "------roleNode w----", this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0)))
            // console.log("-------bubble----", pos, "------roleNode w----", spine.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0)))
        }

    }

    onRoleBeginContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        super.onRoleBeginContact (selfCollider, otherCollider, contact)

        if(this.isInWater==true){
            this.roleRun()
        }
    }

    onWaterContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
            if(this.isInWater == false){
                this.isInWater = true
                if(this.isShoutOut==false) {
                    this.DragonNode.setAnimation(1, "kuai", true)
                }
                this.roleStandby()
            }
        })
    }

    onPlaceContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        console.log(otherCollider.node.name+"-------xxxxxxxx1--",this.isInWater)
        Public.RunCbContactUp(selfCollider, otherCollider, ()=>{
            if(this.isInWater == true){
                this.isInWater = false
                if(this.isShoutOut==false){
                    this.DragonNode.setAnimation(1,"animation",true)
                }
                this.roleStandby()
            }
            console.log(otherCollider.node.name+"-------xxxxxxxx2--",this.isInWater)
        })
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        //判断画框手指的滑动行为
        if(this.isUp==false && Public.IsPointInNodeArea2D(this.Water, pos)==true){
            this.isTouchWater = true
        }
        //判断
        if(this.isTouchMouth==false){
            let startPos = this.StartNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            console.log("-------pos------", startPos, pos)
            if(Public.GetDistance2D(startPos, pos)<100){
                this.isTouchMouthTime = new Date().getTime()
                this.isTouchMouth=true

            }
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.isTouchWater==true){
            let pos = event.getLocation()
            // console.log("-----------isTouchPic move---", pos)
            if(this.touchPicPath.length<50){
                this.touchPicPath.push(pos)
            }
        }

        if(this.isTouchMouth==true && this.isShoutOut == false) {
            let t = new Date().getTime() - this.isTouchMouthTime
            if (t>=3000) {
                this.isShoutOut = true
                this.DragonNode.setAnimation(0, "bizui", true)
            }
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.isTouchWater==true){
            this.isTouchWater=false
            //便利所有点，如果距离和第一个点大于，100并且在格子内，那么就ok
            for(let i=0; i<this.touchPicPath.length; i++){
                let dis = Public.GetDistance2D(this.touchPicPath[0], this.touchPicPath[i])
                // console.log("-----------isTouchPic dis---", dis, i, this.touchPicPath.length)
                if(dis>100){
                    audioManager.instance.playSound(Constants.Sounds.prompt)
                    console.log("-----111----")
                    //相机向下移动
                    for(let i=0; i<this.MoveNode.length; i++){
                        let n = this.MoveNode[i]
                        n.setPosition(new Vec3(n.position.x, n.position.y+200, n.position.z))
                    }
                    this.isUp = true
                    break
                }
            }
            // console.log("-----------isTouchPic end---", this.touchPicPath)
            this.touchPicPath=[]
        }

        if(this.isTouchMouth==true && this.isShoutOut == false){
            let t = new Date().getTime() - this.isTouchMouthTime
            if(t<3000){
                this.isTouchMouth=false
                this.DragonNode.setAnimation(0, "animation", true)
            }else{
                this.isShoutOut = true
                this.DragonNode.setAnimation(0, "bizui", true)
            }
        }

    }




}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
