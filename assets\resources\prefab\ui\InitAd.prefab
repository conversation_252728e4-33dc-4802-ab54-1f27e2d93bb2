[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "InitAd", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 35}, {"__id__": 37}, {"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": -1}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Root", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 11}, {"__id__": 24}], "_active": true, "_components": [{"__id__": 32}], "_prefab": {"__id__": 34}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "BgUnit1", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}, {"__id__": 6}, {"__id__": 8}], "_prefab": {"__id__": 10}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 5}, "_contentSize": {"__type__": "cc.Size", "width": 1600, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6eSyujn+BLc6Qje7lIGHQS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 7}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 113, "g": 166, "b": 207, "a": 255}, "_spriteFrame": {"__uuid__": "c0c6810b-faa9-44c7-8bca-7a584d9626f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6454O7uthAGbio5TheBomy"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 9}, "_alignFlags": 45, "_target": {"__id__": 1}, "_left": -160, "_right": -160, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1600, "_originalHeight": 720, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeep5zBBtCLYGoWKkwgiME"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "887v/5HOtAJrhX9CL3Cn1n"}, {"__type__": "cc.Node", "_name": "back", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [{"__id__": 12}], "_active": true, "_components": [{"__id__": 18}, {"__id__": 20}], "_prefab": {"__id__": 23}, "_lpos": {"__type__": "cc.Vec3", "x": -8.66700000000003, "y": -269.50800000000004, "z": 1}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 17}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 14}, "_contentSize": {"__type__": "cc.Size", "width": 198, "height": 83.16}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82+v12vi9BL5MXWwKgTOzn"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 16}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "继续游戏", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 55, "_fontSize": 55, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 66, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "18828b0f-9232-4be9-adf1-fd5177f80512", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1VRQIXmRLQJ1HFimeh/fg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "20M9XviF9Bp64cjeoEXk6W"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 19}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 83}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bam1AWiZtPapoVZSdlVmC/"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 21}, "clickEvents": [{"__id__": 22}], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "1c20c37e-e994-43d4-9f0f-927c850f7904@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 0.9, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2fHqx494lAmJV6KVsmZf0H"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "43896hQ6VFJfrfRdeEoqDvx", "handler": "OnClickClose", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "16JKV4EdBJpIH3SghuhPXT"}, {"__type__": "cc.Node", "_name": "Mask", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "_prefab": {"__id__": 31}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 26}, "_contentSize": {"__type__": "cc.Size", "width": 3100, "height": 1388}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7fDF70jVDbJ67PHVMdwSK"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 28}, "_visFlags": 0, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_spriteFrame": {"__uuid__": "c0c6810b-faa9-44c7-8bca-7a584d9626f0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eBg4GK9tFJJ/1ZrI7MIPs"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 24}, "_enabled": true, "__prefab": {"__id__": 30}, "_alignFlags": 45, "_target": {"__id__": 1}, "_left": -910, "_right": -910, "_top": -334, "_bottom": -334, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5cw1/UfdD0bqw1Zf0ZYDx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f1Ec6eP99O76fsUiWm0tO3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 33}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9SQxkdJxIdr7++IJRVrZo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "91PUw41SJJkKXGhB3pA0Iu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 36}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8236R4W1FFfbTF0UPfAF3Z"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 38}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5cw1/UfdD0bqw1Zf0ZYDx"}, {"__type__": "43896hQ6VFJfrfRdeEoqDvx", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 40}, "zIndex": 100, "Mask": {"__id__": 24}, "goLabel": {"__id__": 15}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75L7rcgwlBe6w8/ZoWMO9X"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "74ap5x21tIuLr2J4ymU6en"}]