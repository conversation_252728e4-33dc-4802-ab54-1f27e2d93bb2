{"__version__": "1.0.5", "modules": {"cache": {"base": {"_value": true}, "graphcis": {"_value": true}, "gfx-webgl": {"_value": true}, "gfx-webgl2": {"_value": true}, "3d": {"_value": false}, "2d": {"_value": true}, "ui": {"_value": true}, "particle": {"_value": false}, "physics": {"_value": false, "_option": "physics-cannon"}, "physics-ammo": {"_value": false}, "physics-cannon": {"_value": false}, "physics-physx": {"_value": false}, "physics-builtin": {"_value": false}, "physics-2d": {"_value": true, "_option": "physics-2d-box2d"}, "physics-2d-box2d": {"_value": false}, "physics-2d-builtin": {"_value": false}, "intersection-2d": {"_value": true}, "primitive": {"_value": false}, "profiler": {"_value": true}, "particle-2d": {"_value": true}, "audio": {"_value": true}, "video": {"_value": false}, "webview": {"_value": false}, "tween": {"_value": true}, "terrain": {"_value": false}, "tiled-map": {"_value": false}, "spine": {"_value": true}, "dragon-bones": {"_value": false}, "marionette": {"_value": true}}, "includeModules": ["base", "gfx-webgl", "gfx-webgl2", "2d", "ui", "physics-2d-box2d", "intersection-2d", "profiler", "particle-2d", "audio", "tween", "spine", "marionette"], "noDeprecatedFeatures": {"value": false, "version": ""}, "flags": {}}}