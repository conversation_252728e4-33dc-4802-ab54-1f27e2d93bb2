
import { find, instantiate, Prefab, resources, Node, Vec2, v2 } from "cc";
import { EDartStatus, EDartsType, tyqSDK } from "./tyq-sdk";
import CrazyBox, { crazyType } from "./view/CrazyBox";
import DartAd from "./view/DartAd";
import LandPage from "./view/LandPage";
import Privacypolicy, { IUrlConfig } from "./view/Privacypolicy";
import VerifyRealName from "./view/VerifyRealName";

export default class TyqViewMgr {
    private static prefabPath: string = "tyqRes/prefab/";
    /**
     * 显示狂点盒子
     * @param type 
     * @param cb 
     */
    public static showCrazyBox(type: crazyType, cb: (isSuccess) => void) {
        resources.load(this.prefabPath + "CrazyBox", Prefab, (err: Error, prefab: Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = instantiate(prefab);
            find("Canvas").addChild(node);
            node.setSiblingIndex(99999);
            node.getComponent(CrazyBox).init(cb, type);
        });
    }
    /**
     * 显示实名认证界面
     * @param parent 
     * @param next 
     */
    public static showVerifyRealName(parent: Node, next: () => void) {
        resources.load(this.prefabPath + "VerifyRealName", Prefab, (err: Error, prefab: Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = instantiate(prefab);
            parent.addChild(node);
            node.getComponent(VerifyRealName).init(next);
        });
    }
    /**
     * 显示隐私政策界面
     * @param parent 
     * @param next 
     */
    public static showVerifyPrivacypolicy(parent: Node, config: IUrlConfig, next: () => void) {
        resources.load(this.prefabPath + "Privacypolicy", Prefab, (err: Error, prefab: Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = instantiate(prefab);
            parent.addChild(node);
            node.getComponent(Privacypolicy).init(next, config);
        });
    }

    //显示落地页
    public static showLandPage(data, config, cb?) {
        resources.load(this.prefabPath + "LandPage", Prefab, (err: Error, prefab: Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = instantiate(prefab);
            node.getComponent(LandPage).initData(data, config, cb);
            find("Canvas").addChild(node);
            node.setSiblingIndex(99999);
        });
    }
    //显示飞镖广告详情页
    public static showDartAd(type: EDartsType, parent: Node, config, pos: Vec2 = v2()) {
        let dartData = tyqSDK.getDartAdByType(type);
        if (dartData) {
            resources.load(this.prefabPath + "DartAd", Prefab, (err: Error, prefab: Prefab) => {
                if (err) {
                    console.error(err);
                    return;
                }

                let node = instantiate(prefab);
                node.getComponent(DartAd).initData(dartData, type, config);
                if (pos) {
                    node.setPosition(pos.x, pos.y);
                }
                parent.addChild(node);
                tyqSDK.sendDartAdStatus(dartData.ads_id, EDartStatus.exposure);
            });
        } else {
            console.error("没有dartAd数据");
        }

    }
}