
import { _decorator, Component, Node, assetManager,SpriteAtlas, Prefab, Font} from 'cc';
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = BundleMgr
 * DateTime = Tue Mar 01 2022 14:12:29 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = BundleMgr.ts
 * FileBasenameNoExtension = BundleMgr
 * URL = db://assets/script/framework/BundleMgr.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('BundleMgr')
export class BundleMgr extends Component {

    private static _instance: BundleMgr;

    public static getInstance(): BundleMgr {
        if (!this._instance) {
            this._instance = new BundleMgr();
        }
        return this._instance;
    }

    loadBundle(name){
        return new Promise(result=>{
            assetManager.loadBundle(name,null,(err,bundle)=>{
                result(bundle);
                if (err) {
                    console.log('加载bundle错误:',name,err)
                    return;
                }
            })
        })
    }

    /**
     * 分包的方式加载类型是type传入
     * @param bundleName bundle的名称，默认包是resources
     * @param resPathAndName 资源路径和名称
     * @param cb 回调函数
     * @param resType SpriteAtlas Prefab Font 资源类型
     */
    loadRes(bundleName, resPathAndName, cb, resType){
        return new Promise(result=>{
            assetManager.getBundle(bundleName).load(resPathAndName,resType,(err, resType)=>{
                console.log("通用加载资源-----", err,resType)
                if (cb) {
                    cb(resType);
                }
                result(resType);
            })
        })
    }

    loadSpriteAtlas(name,cb){
        return new Promise(result=>{
            assetManager.getBundle('textures').load(name,SpriteAtlas,(err,atlas:SpriteAtlas)=>{
                console.log(err,atlas)
                if (cb) {
                    cb(atlas);
                }
                result(atlas);
            })
        })
    }

    loadPrefab(name,cb){
        return new Promise(result=>{
            assetManager.getBundle('resources').load(name,Prefab,(err,prefab:Prefab)=>{
                // console.log(err,prefab)
                if (cb) {
                    cb(prefab);
                }
                result(prefab);
            })
        })
    }

    loadFont(name,cb){
        return new Promise(result=>{
            assetManager.getBundle('font').load(name,Font,(err,font:Font)=>{
                console.log(err,font)
                if (cb) {
                    cb(font);
                }
                result(font);
            })
        })
    }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
