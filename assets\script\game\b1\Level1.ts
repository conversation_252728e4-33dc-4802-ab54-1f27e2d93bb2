
import { _decorator, Component, Node , tween, Vec3, PhysicsSystem2D, EPhysics2DDrawFlags, v2, PHYSICS_2D_PTM_RATIO, RigidBody2D} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {uiManager} from "../../framework/uiManager";
import {Constants} from "../Constants";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level1')
export class Level1 extends LevelDialogBase {
    // [1]
    // dummy = '';

    // [2]
    // @property
    // serializableDummy = 0;


    start(){
       super.start()
    }

    show(params){
        super.show(params)
        // let order = localConfig.instance.getTable("order")
        this.levelLabel.string = `第${Public.CurLevelNum}关`
        uiManager.instance.showDialog(Constants.Dialogs.ops,{hiddenTitle:true})
    }

}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
