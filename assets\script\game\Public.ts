import {
    _decorator,
    assetManager,
    Collider2D,
    Component,
    Node,
    tween,
    UIOpacityComponent,
    UITransform,
    Camera,
    v3,
    Vec3
} from 'cc';
import {Constants} from "./Constants";
import {configuration} from "../framework/configuration";
import {localConfig} from "../framework/localConfig";
import {gameStorage} from "../framework/gameStorage";
import {uiManager} from "../framework/uiManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Public
 * DateTime = Wed Feb 23 2022 16:34:06 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Public.ts
 * FileBasenameNoExtension = Public
 * URL = db://assets/script/game/Public.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Public')
export class Public extends Component {
    private static _instance: Public;

    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new Public();
        return this._instance;
    }

    public static isRealLoadingEnd = false;
    public static isShowMainCanvas = false;
    public static CurLevelNum = 1; //当前关卡，全局变量
    public static CurLevelRestartCnt = 0; //当前关卡，重开次数
    public static MainCamera:Camera = null; //main场景摄像机


    start () {
        // [3]
    }

    /**
     * 分享
     * @constructor
     */
    public static Share(){
        console.log("------------待处理----------")
    }

    public static enterLevel(levelNum){
        //判断体力是否足够，如果不足够，那么弹窗提示看广告
        if(gameStorage.getInt(Constants.CacheDataKey.strength, -1)<=0){
            uiManager.instance.showDialog(Constants.Dialogs.strengthPrompt)
            return
        }
        uiManager.instance.hideDialog(Constants.Dialogs.mainUI)
        console.log("--------当前关@@@@@@@---", levelNum)
        Public.CurLevelNum = levelNum
        // uiManager.instance.showDialog(Constants.Dialogs.levelLoading)
        Public.LoadLevel(uiManager.instance)
    }


    /**
     * 日期格式化
     * @param date
     * @param fmt YYYY/mm/dd HH:MM:SS
     */
    public static DateFormat(date:Date, fmt){
        let ret;
        const opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            }
        }
        return fmt;
    }

    /**
     * 时间格式
     * @param _time
     */
    public static TimerFormat1(_time) {
        // console.log("timerFormat:",time);
        let time = Math.round(_time);
        let t = Number(time);
        let m = Math.floor(t / 60);
        let s = t % 60;
        let str_m = m < 10 ? '0' + m : m;
        let str_s = s < 10 ? '0' + s : s;
        let str = `${str_m}:${str_s}`;
        // console.log("timerFormat:",str);
        return str;
    }

    /**
     * 设置水流的动画
     * @param waterNode
     * @param waterPicNode
     * @constructor
     */
    public static MoveWater(waterNode, waterPicNode){
        let n = waterPicNode
        let pos = n.position
        tween(n).to(8,{position:new Vec3(pos.x+341,pos.y,pos.z,)}).call(()=>{
            if(waterNode.active ==true){
                n.position = new Vec3(pos.x-341,pos.y,pos.z)
                Public.MoveWater(waterNode, n)
            }
        }).start()
    }

    /**
     * 震动摄像机
     * @constructor
     * @param times 震动的次数
     * @param range 震动的幅度 建议是5
     */
    public static ShakeCamera(times:number=8, range:number=3){
        let oriPos = Public.MainCamera.node.position
       Public._ShakeCamera(new Vec3(oriPos.x, oriPos.y, oriPos.z), times, range)
    }

    private static _ShakeCamera(oriPos, times:number, range:number){
        let randOffsetX = Public.RanInt(-range, range)
        let randOffsetY = Public.RanInt(-range, range)
        let randOffsetZ = Public.RanInt(-range, range)
        tween(Public.MainCamera.node)
            .to(0.05, {position: new Vec3(oriPos.x+randOffsetX, oriPos.y+randOffsetY, oriPos.z+randOffsetZ)})
            .call(()=>{
                times--
                if(times>0){
                    Public._ShakeCamera(oriPos, times, range)
                }else{
                    Public.MainCamera.node.setPosition(oriPos)
                }
            }).start()
    }

    /**
     * 是否加载了包
     * @param bundleName
     * @constructor
     */
    public static IsLoadBundle(bundleName){
        let bundleObj = assetManager.getBundle(bundleName)
        console.log("bundleObj-------", bundleObj)
        return bundleObj!=undefined
    }

    /**
     * 加载关卡，如果已经加载就不弹出加载中的窗口
     * @constructor
     */
    public static LoadLevel(uiManagerInst){
        let levelName = Public.GetRightLevelPath()
        let bundleName = levelName.split("|")[0]
        if(Public.IsLoadBundle(bundleName)==true){
            uiManagerInst.showDialog(levelName)
        }else{
            uiManagerInst.showDialog(Constants.Dialogs.levelLoading)
        }
    }

    /**
     * 一个点是否在这个节点的区域内
     * @param node 节点
     * @param pos 触点坐标
     * @constructor
     */
    public static IsPointInNodeArea2D(node:Node, pos){
        const nodeUIT = node.getComponent(UITransform)!;
        let wordPos = nodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
        //判断点击的位置是否在矩形区域内
        if(pos.x < wordPos.x + nodeUIT.width/2 && pos.x > wordPos.x - nodeUIT.width/2){
            if(pos.y < wordPos.y + nodeUIT.height/2 && pos.y > wordPos.y - nodeUIT.height/2){
                return true
            }
        }
        return false
    }

    /**
     * 判断下方被碰到的情况
     * @param selfCollider
     * @param otherCollider
     * @param cb
     * @constructor
     */
    public static RunCbContactBelow(selfCollider: Collider2D, otherCollider: Collider2D, cb: Function){
        // let h = selfCollider.node.getComponent(UITransform).contentSize.height
        let w = selfCollider.node.getComponent(UITransform).contentSize.width
        let h = selfCollider.node.getComponent(UITransform).contentSize.height
        let h2 = otherCollider.node.getComponent(UITransform).contentSize.height
        let w2 = otherCollider.node.getComponent(UITransform).contentSize.width
        // if(otherCollider.node.position.x > selfCollider.node.position.x - w && otherCollider.node.position.x < selfCollider.node.position.x + w ){
        //     if(selfCollider.node.position.y> otherCollider.node.position.y + h2/2){
        //         cb()
        //     }
        // }
        if(selfCollider.node.position.y- h/2 >= otherCollider.node.position.y + h2/4){
            cb()
        }
    }

    /**
     * 判断上方被碰到的情况，前提2个物体的锚点都是0.5
     * @param selfCollider
     * @param otherCollider
     * @param cb
     * @constructor
     */
    public static RunCbContactUp(selfCollider: Collider2D, otherCollider: Collider2D, cb: Function){
        // let h = selfCollider.node.getComponent(UITransform).contentSize.height
        let w = selfCollider.node.getComponent(UITransform).contentSize.width
        let h = selfCollider.node.getComponent(UITransform).contentSize.height
        let h2 = otherCollider.node.getComponent(UITransform).contentSize.height
        let w2 = otherCollider.node.getComponent(UITransform).contentSize.width
        //中心点必须在上方那个物体的宽内
        // if(selfCollider.node.position.x > otherCollider.node.position.x - w/2 && selfCollider.node.position.x < otherCollider.node.position.x + w/2 ){
        //     //上方物体的底部y坐标必须大于自身物体顶部坐标
        //     if(otherCollider.node.position.y + h2/2 > selfCollider.node.position.y - h/2){
        //         cb()
        //     }
        // }
        // console.log("-------other---", otherCollider.node.position.y - h2/2, '----self----', selfCollider.node.position.y + h/2)
        if(otherCollider.node.position.y - h2/4 >= selfCollider.node.position.y + h/2){
            cb()
        }
    }

    /**
     * 左右碰到后移动
     * @param selfCollider
     * @param otherCollider
     * @param cbLeft 向左推动的回调函数
     * @param cbRight 向右推的回调函数
     * @constructor
     */
    public static RunCbContactLeftRight(selfCollider: Collider2D, otherCollider: Collider2D, cbLeft: Function, cbRight:Function){
        // let h = selfCollider.node.getComponent(UITransform).contentSize.height
        let w = selfCollider.node.getComponent(UITransform).contentSize.width
        if(otherCollider.node.position.x<selfCollider.node.position.x - w/2){
            cbRight()
        }else if(otherCollider.node.position.x>selfCollider.node.position.x + w/2){
            cbLeft()
        }
    }

    /**
     * 按距离判断触发
     * @param selfCollider
     * @param otherCollider
     * @param dis 距离参数
     * @param cb 回调函数
     * @constructor
     */
    public static RunCbContactDistance(selfCollider: Collider2D, otherCollider: Collider2D, dis:number, cb: Function){
        // let h = selfCollider.node.getComponent(UITransform).contentSize.height
        // let w = selfCollider.node.getComponent(UITransform).contentSize.width
        // let h2 = otherCollider.node.getComponent(UITransform).contentSize.height
        // if(otherCollider.node.position.x > selfCollider.node.position.x - w && otherCollider.node.position.x < selfCollider.node.position.x + w ){
        //     if(selfCollider.node.position.y> otherCollider.node.position.y + h2/2){
        //         cb()
        //     }
        // }
       if(dis<Public.GetDistance2D(selfCollider.node.position, otherCollider.node.position)){
           cb()
       }
    }

    public static GetRightLevelPath(){
        // let order = ("order")
        let order = localConfig.instance.getTable(Constants.CSVTables.order)
        let path1 = Constants.LevelPath.replace("{0}", order[Public.CurLevelNum].level+"")
        let path = path1.replace("{1}", order[Public.CurLevelNum].level+"")
        // console.log("order---------", order[Public.CurLevelNum].level, path)
        path = Constants.LevelBundlesConfig["level"+order[Public.CurLevelNum].level] + "|" + path
        return path
    }


    public static GetRightConfig(){
        let orderTab = localConfig.instance.getTable("order")
        let level = orderTab[Public.CurLevelNum].level
        let levelTab = localConfig.instance.getTable("level")
        // console.log("@@@@@@@@@@@@@@@@@-------levelTab---", levelTab, "level----",level, " curLevelNum----",Public.CurLevelNum)
        return levelTab[level]
    }

    public static GetTotalBreakLevel(){
        let cnt = 0;
        for(let i=0; i<Constants.TotalLevelNum; i++){
            let isUnlock = gameStorage.getInt(Constants.LevelHead+(i+1))
            if(isUnlock==2){
                cnt+=1
            }
        }
        return cnt;
    }

    /**
     * 计算2d的物体的距离
     * @param start
     * @param end
     * @constructor
     */
    public static GetDistance2D(start, end){
        let pos =new Vec3(start.x - end.x, start.y - end.y, 0);
        return Math.sqrt(pos.x*pos.x + pos.y*pos.y);
    }

    public static RanInt(min, max) {
        return Math.round(Math.random() * (max - min) + min);
    }

    /**
     *
     * @param pArr
     * @param startNode
     * @param i
     * @param cb
     * @param duration 总动画时长
     * @constructor
     */
    public static Bezier2DRun(pArr, startNode, i, duration:number, cb:Function){
        if(i>pArr.length){
            cb && cb()
            return;
        }
        if(startNode==undefined) return
        tween(startNode).to(duration/pArr.length, {position:pArr[i]}).call(()=>{
            Public.Bezier2DRun(pArr, startNode, i+1, duration, cb)
        }).start()
    }

    /**
     * 通用贝塞尔接口
     * @param startNode 起始节点
     * @param endNode 目标节点
     * @param radio 控制点偏移量 0.1-0.9 0.5是中点
     * @param deltaX 控制点的相对中点的偏移量
     * @param deltaY 控制点的相对中点的偏移量
     * @param duration 总动画时长
     * @param cb 回调函数
     */
    public static Bezier2DShow(startNode, endNode, radio, deltaX, deltaY, duration, cb:Function){
        let e = endNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
        let eNode = startNode.parent.getComponent(UITransform).convertToNodeSpaceAR(e)
        console.log("eNode--------", startNode.position.x, startNode.position.y, eNode.x, eNode.y)
        // tween(startNode).to(0.5, {position:eNode}).start()

        let pArr =[]
        let p1 = startNode.position
        let p2 = eNode
        let cp = Public.Bezier2DGetCtlPoint(p1, p2, radio, deltaX, deltaY)
        // console.log("----ppc----", p1, p2, cp)
        for(let i=0; i<30; i++){
            let p = Public.BezierTwo(i/30, p1, cp, p2)
            pArr.push(new Vec3(p[0], p[1], startNode.position.z))
            // console.log("------p------", p)
        }
        pArr.push(eNode)
        Public.Bezier2DRun(pArr, startNode, 0, duration, cb)
    }

    /**
     * 通用贝塞尔接口
     * @param startNode 起始节点
     * @param delta 坐标差
     * @param radio 控制点偏移量 0.1-0.9 0.5是中点
     * @param deltaX 控制点的相对中点的偏移量
     * @param deltaY 控制点的相对中点的偏移量
     * @param duration 总动画时长
     * @param cb 回调函数
     */
    public static Bezier2DShowPoint(startNode, delta:Vec3, radio, deltaX, deltaY, duration, cb:Function){
        // tween(startNode).to(0.5, {position:eNode}).start()

        let pArr =[]
        let p1 = startNode.position
        let p2 = new Vec3(startNode.position.x + delta.x, startNode.position.y + delta.y, startNode.position.z + delta.z)
        let cp = Public.Bezier2DGetCtlPoint(p1, p2, radio, deltaX, deltaY)
        // console.log("----ppc----", p1, p2, cp)
        for(let i=0; i<30; i++){
            let p = Public.BezierTwo(i/30, p1, cp, p2)
            pArr.push(new Vec3(p[0], p[1], 0))
            // console.log("------p------", p)
        }
        pArr.push(p2)
        Public.Bezier2DRun(pArr, startNode, 0, duration, cb)
    }

    /**
     * 获得贝塞尔曲线的控制点
     * @param p1 起始点
     * @param p2 结束点
     * @param radio 0.1-0.9  0.5就是中线
     * @param deltaX 中点为原点的偏移量
     * @param deltaY 中点为原点的偏移量
     * @constructor
     */
    public static Bezier2DGetCtlPoint(p1, p2, radio, deltaX, deltaY){
        return new Vec3((p1.x+p2.x)*radio + deltaX, (p1.y+p2.y)*radio + deltaY, 1)
    }
    /**
    * @desc 一阶贝塞尔
    * @param {number} t 当前百分比
    * @param {Array} p1 起点坐标
    * @param {Array} p2 终点坐标
    */
    public static BezierOne(t, p1, p2) {
        const x1 = p1.x
        const x2 = p2.x
        const y1 = p1.y
        const y2 = p2.y

        let x = x1 + (x2 - x1) * t;
        let y = y1 + (y2 - y1) * t;
        return [x, y];
    }

    /**
     * @desc 二阶贝塞尔
     * @param {number} t 当前百分比
     * @param {Array} p1 起点坐标
     * @param {Array} cp 控制点
     * @param {Array} p2 终点坐标
     */
    public static BezierTwo(t, p1, cp, p2) {
        const x1 = p1.x
        const x2 = p2.x
        const y1 = p1.y
        const y2 = p2.y
        const cx = cp.x
        const cy = cp.y
        let x = (1 - t) * (1 - t) * x1 + 2 * t * (1 - t) * cx + t * t * x2;
        let y = (1 - t) * (1 - t) * y1 + 2 * t * (1 - t) * cy + t * t * y2;
        return [Math.round(x), Math.round(y)];
    }
    //
    // /** TODO 改造为可用代码
    //  * @desc 三阶贝塞尔
    //  * @param {number} t 当前百分比
    //  * @param {Array} p1 起点坐标
    //  * @param {Array} p2 终点坐标
    //  * @param {Array} cp1 控制点1
    //  * @param {Array} cp2 控制点2
    //  */
    // threeBezier(t, p1, cp1, cp2, p2) {
    //     const [x1, y1] = p1;
    //     const [x2, y2] = p2;
    //     const [cx1, cy1] = cp1;
    //     const [cx2, cy2] = cp2;
    //     let x =
    //         x1 * (1 - t) * (1 - t) * (1 - t) +
    //         3 * cx1 * t * (1 - t) * (1 - t) +
    //         3 * cx2 * t * t * (1 - t) +
    //         x2 * t * t * t;
    //     let y =
    //         y1 * (1 - t) * (1 - t) * (1 - t) +
    //         3 * cy1 * t * (1 - t) * (1 - t) +
    //         3 * cy2 * t * t * (1 - t) +
    //         y2 * t * t * t;
    //     return [x, y];
    // }


    //CT快速放大并渐隐
    // public static CTScaleUpAndHide(formNode: Node, callFunc: Function) {
    //     let CT = formNode.getChildByName("CT") as Node;
    //     if (!CT) {
    //         return;
    //     }
    //
    //     if (CT["tween"]) {
    //         CT["tween"].stop();
    //     }
    //
    //     CT.getComponent(UIOpacityComponent).opacity = 255
    //     CT.scale = v3(1, 1, 1)
    //
    //     CT["tween"] = tween(CT)
    //         .to(3, {scale: v3(0.5, 0.5, 0.5)})
    //         .sequence(
    //             tween(CT.getComponent(UIOpacityComponent))
    //                 .to(0.15, {opacity: 0})
    //                 .start(),
    //             tween(CT)
    //                 .delay(0)
    //                 .start()
    //         )
    //         .call(callFunc)
    //         .start()
    // }

}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
