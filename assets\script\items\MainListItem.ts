import {_decorator, Component, Label, Sprite, Sprite<PERSON>rame, Node} from 'cc';
import {Constants} from "../game/Constants";
import {uiManager} from "../framework/uiManager";
import {Public} from "../game/Public";
import {AdCtl} from "../channel/AdCtl";
import TyqEventMgr from "../tyqSDK/tyq-event-mgr";
import {gameStorage} from "../framework/gameStorage";
import {clientEvent} from "../framework/clientEvent";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = MainListItem
 * DateTime = Wed Feb 23 2022 14:53:49 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = MainListItem.ts
 * FileBasenameNoExtension = MainListItem
 * URL = db://assets/script/items/MainListItem.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('MainListItem')
export class MainListItem extends Component {

    @property({type:Sprite,tooltip:"背景图标"})
    BgNode: Sprite = null
    @property({type:Sprite,tooltip:"没有通关时的锁图标"})
    LockIconSP: Sprite = null
    @property({type:Sprite,tooltip:"视频播放的提示图标"})
    VideoIconSP: Sprite = null
    @property({type:Label,tooltip:"第几关"})
    NumLab: Label = null
    @property({type:SpriteFrame,tooltip:"背景框"})
    ItemsBg: SpriteFrame[] = []
    @property({type:SpriteFrame,tooltip:"未解锁的背景框"})
    ItemsBgLock: SpriteFrame[] = []
    @property({type:Node,tooltip:"通关表示"})
    SuccessMark:Node = null

    isUnlock:boolean = false; //isUnlock是否解锁的标志位。
    levelNum:number=0; //levelNum关卡数

    Init(value){
        console.log("@@####初始化----", value)
        //接收参数
        this.levelNum = value.levelNum

        //从磁盘中判断是否解锁
        let isUnlock = gameStorage.getInt(Constants.LevelHead+this.levelNum, Constants.LevelState.locked)
        if(this.levelNum==1){
            let level1Conf = gameStorage.getInt(Constants.LevelHead+this.levelNum,Constants.LevelState.locked)
            // console.log("----------level1Conf--------", level1Conf)
            if(level1Conf!=Constants.LevelState.success){
                gameStorage.setInt(Constants.LevelHead+1, Constants.LevelState.open) //默认第一关解锁
                isUnlock = Constants.LevelState.open
            }
        }
        // console.log(isUnlock+"==========================")
        this.isUnlock = !(isUnlock==0)
        //如果是第一关，一定是开启的
        // console.log("----------this.levelNum--------", this.levelNum)
        this.SuccessMark.active= isUnlock==2
        this.LockIconSP.node.active = this.isUnlock==false
        this.VideoIconSP.node.active = this.isUnlock==false
        this.NumLab.node.active = this.isUnlock==true
        this.NumLab.string = this.levelNum + ""

        //设置不同的背景
        let n = this.levelNum%10
        if(this.isUnlock==true){
            this.BgNode.spriteFrame = this.ItemsBg[n]
        }else{
            this.BgNode.spriteFrame = this.ItemsBgLock[n]
        }

    }

    start () {
        // [3]
    }

    OnClickItem(){
        console.log("--------click1-------", this.levelNum, this.isUnlock)
        let isUnlock = gameStorage.getInt(Constants.LevelHead+this.levelNum, Constants.LevelState.locked)
        if(!(isUnlock==0)){
            console.log("--------click12-------", this.levelNum, this.isUnlock)
            Public.enterLevel(this.levelNum)
        }else{
            //看视频然后回调解锁
            //看广告激励广告
            AdCtl.instance.HideAllCustomADTmp()
            AdCtl.instance.ShowRewardVideoAd(()=>{
                console.warn("------执行video成功回调--")
                AdCtl.instance.ShowAllCustomADTmp()
                let levelNum = this.levelNum
                uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoSuccess, cb:()=>{
                        // 观看激励视频-解锁关卡n
                        TyqEventMgr.ins.sendEvent("观看激励视频-解锁关卡"+Public.CurLevelNum);
                        //保存到本地数据
                        gameStorage.setInt(Constants.LevelHead+this.levelNum, Constants.LevelState.open)
                        Public.CurLevelNum = levelNum
                        gameStorage.setInt(Constants.LevelHead+levelNum, Constants.LevelState.open)
                        clientEvent.dispatchEvent(Constants.Events.showList)
                        Public.enterLevel(levelNum)
                    }})

            }, ()=>{
                console.warn("------执行video失败回调--")
                AdCtl.instance.ShowAllCustomADTmp()
                uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoFail, cb:()=>{

                    }})
                // uiManager.instance.showTips(`视频观看失败`)
            });

        }
    }


    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
