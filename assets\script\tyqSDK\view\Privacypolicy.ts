import { BlockInputEvents, Component, game, sys, UITransform, view, WebView, _decorator, Node } from "cc";
import TyqEventMgr from "../tyq-event-mgr";
import { tyqSDK } from "../tyq-sdk";

const { ccclass, property } = _decorator;
export interface IUrlConfig {
    useAgreeUrl: string,
    privacypolicyUrl: string,
}
@ccclass
export default class Privacypolicy extends Component {
    private _useAgreeUrl = "http://www.xmtyq.com/pages/gamenotice_chn.html?gamename=%E6%9C%80%E5%BC%B7%E5%B0%8F%E8%8B%B1%E9%9B%84";
    private _privacypolicyUrl = "http://www.xmtyq.com/pages/gamenotice_chn.html?gamename=%E6%9C%80%E5%BC%B7%E5%B0%8F%E8%8B%B1%E9%9B%84";
    @property(Node) btn_agree: Node = null;
    @property(Node) btn_exit: Node = null;
    @property(Node) btn_close: Node = null;
    @property(WebView) webView: WebView = null;

    @property(Node) labelContent: Node = null;
    private _callBack: Function = null;
    private _privacy = "tyq_littleHero_privacypolicy";
    protected onLoad(): void {
        this.node.getComponent(UITransform).setContentSize(view.getVisibleSize());
        if (!this.node.getComponent(BlockInputEvents)) {
            this.node.addComponent(BlockInputEvents);
        }
        this.hideWebView();
    }
    public init(cb: Function = void 0, config: IUrlConfig) {
        this._callBack = cb;
        this._useAgreeUrl = config.useAgreeUrl;
        this._privacypolicyUrl = config.privacypolicyUrl;
    }
    protected start(): void {
        let isShowPrivacy = tyqSDK.getSwitchValue("isHidePrivacy");
        if (isShowPrivacy || sys.localStorage.getItem(this._privacy)) {
            this.node.active = false;
            this.node.destroy();
        }

    }
    public onClickAgree() {
        this.node.destroy();
    }
    public onClickClose() {
        this.hideWebView();
    }
    public onClickExit() {
        game.end();
    }

    public onClickUseAgree() {
        this.showWebView(this._useAgreeUrl);

    }
    protected onDestroy(): void {
        sys.localStorage.setItem(this._privacy, "true");
        if (this._callBack) {
            this._callBack();
            this._callBack = null;
        }
        TyqEventMgr.ins.onAgreeUseAgree();
    }
    public onClickPrivacypolicy() {
        this.showWebView(this._privacypolicyUrl);
    }
    public showWebView(url) {
        this.webView.url = url;
        this.webView.node.active = true;
        this.labelContent.active = false;
    }
    public hideWebView() {
        this.webView.node.active = false;
        this.labelContent.active = true;
    }
}