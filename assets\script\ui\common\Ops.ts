
import { _decorator,EventTouch, Node, macro , UITransform, Vec3, tween, Label} from 'cc';
import {Constants} from "../../game/Constants";
import {clientEvent} from "../../framework/clientEvent";
import {uiManager} from "../../framework/uiManager";
import {dialogBase} from "../../framework/dialogBase";
import {Public} from "../../game/Public";
import {audioManager} from "../../framework/audioManager";
import {AdCtl} from "../../channel/AdCtl";
import TyqEventMgr from "../../tyqSDK/tyq-event-mgr";
import {configuration} from "../../framework/configuration";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Ops
 * DateTime = Thu Feb 24 2022 16:22:17 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Ops.ts
 * FileBasenameNoExtension = Ops
 * URL = db://assets/script/game/Ops.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Ops')
export class Ops extends dialogBase {
    @property(Node)
    LeftNode:Node = null
    @property(Node)
    RightNode:Node = null
    @property(Node)
    UpNode:Node = null

    @property(Node)
    PromptNode:Node = null
    @property(Node)
    PauseNode:Node = null
    @property(Node)
    RestartNode:Node = null

    @property(Node)
    PromptDialog:Node = null

    @property(Label)
    TitleLabel:Label = null

    @property(Node)
    OpsLeft:Node = null
    @property(Node)
    OpsRight:Node = null
    @property(Node)
    OpsJump:Node = null

    opsLeftOrRight:string = "left"
    calShowTipTime:number=0
    calShakePromptTime:number=0
    isSeenVideo:boolean = false //是否看过视频
    isJumpPush:boolean = false //是否按下跳跃键

    onEnable(){
        clientEvent.on(Constants.Events.restart, this.restart, this);
    }

    onDisable(){
        clientEvent.off(Constants.Events.restart, this.restart, this);
        // AdCtl.instance.HideCustomLevelUI()
    }

    start(){
        macro.ENABLE_MULTI_TOUCH = true;
        // 绑定一个touch事件
        this.node.on(Node.EventType.TOUCH_START, this._touchStart, this)
        this.node.on(Node.EventType.TOUCH_MOVE, this._touchMove, this)
        this.node.on(Node.EventType.TOUCH_END, this._touchEnd, this)
        this.node.on(Node.EventType.TOUCH_CANCEL, this._touchCancel, this)
    }

    show(args){
        this.TitleLabel.node.active = true
        //显示标题
        let conf = Public.GetRightConfig()
        // console.log("conf---", conf)
        this.TitleLabel.string = conf.name

        this.PromptNode.setScale(new Vec3(0,0,0))
        this.PauseNode.setScale(new Vec3(0,0,0))
        this.RestartNode.setScale(new Vec3(0,0,0))
        // this.OpsLeft.setScale(new Vec3(0,0,0))
        // this.OpsRight.setScale(new Vec3(0,0,0))
        // this.OpsJump.setScale(new Vec3(0,0,0))
        this.OpsLeft.active = false
        this.OpsRight.active = false
        this.OpsJump.active = false
        // tween(this.PromptNode).to(1, {scale:new Vec3(1,1,1)}).start()
        tween(this.PromptNode).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()
        tween(this.PauseNode).delay(0.1).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()
        tween(this.RestartNode).delay(0.2).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()
        tween(this.OpsLeft).delay(0.3).call(()=>{
            this.OpsLeft.active = true
            this.OpsLeft.setScale(new Vec3(0,0,0))
        }).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()
        tween(this.OpsRight).delay(0.4).call(()=>{
            this.OpsRight.active = true
            this.OpsRight.setScale(new Vec3(0,0,0))
        }).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()
        tween(this.OpsJump).delay(0.5).call(()=>{
            this.OpsJump.active = true
            this.OpsJump.setScale(new Vec3(0,0,0))
        }).to(0.6, {scale:new Vec3(1,1,1)},  { easing: 'cubicInOut' }).start()

        //判断是否需要显示左上角提示框
        this.PromptDialog.active = false
        if(Public.CurLevelRestartCnt>=1){
            Public.CurLevelRestartCnt=0
            // this.PromptDialog.setScale(new Vec3(0.5,0.5,0))
            // this.PromptDialog.setWorldScale(new Vec3(0.5,0.5,0))
            tween(this.PromptDialog)
                .delay(0.5).call(()=>{
                    this.PromptDialog.active = true
                })
                // .to(1,{scale: new Vec3(1,1,1)})
                .delay(3)
                .call(()=>{
                    this.PromptDialog.active = false
            }).start()
        }

        // console.log("---------param--------", args)
        if(args!=undefined && args.hiddenTitle==true){
            this.TitleLabel.node.active = false
        }
        if(args!=undefined && args.offsetVec!=undefined){
            let pos = this.TitleLabel.node.position
            this.TitleLabel.node.setPosition(new Vec3(pos.x + args.offsetVec.x, pos.y + args.offsetVec.y, pos.z + args.offsetVec.z ))
        }

        AdCtl.instance.ShowBannerAd("game")

        //初始化参数
        this.calShowTipTime = Constants.PromptInterval-2
        this.calShakePromptTime = 0
        this.isSeenVideo = false
    }

    private _touchStart(event:EventTouch){
        clientEvent.dispatchEvent(Constants.Events.touchStart, event)
        //如果和先左的按钮距离在100px，就发送向左移动的信号
        //如果和先右的按钮距离在100px，就发送向右移动的信号
        // let pos = event.getLocation();
        let pos = event.getUILocation()
        let cPos = Public.MainCamera.node.position
        // let pos = event.getLocationInView()

        const leftNode1 = this.LeftNode.getComponent(UITransform)!;
        let wordPos = leftNode1.convertToWorldSpaceAR(new Vec3(0,0,0))
        let realPos = new Vec3(wordPos.x-cPos.x, wordPos.y - cPos.y, wordPos.z - cPos.z)
        // console.log("----------getLocationInView---------- touch",pos,"--worldTouch", wordPos)
        let leftDis = Public.GetDistance2D(realPos, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(leftDis<80){
            this.LeftNode.setScale(new Vec3(0.9, 0.9,1))
            this.opsLeftOrRight = "left"
            clientEvent.dispatchEvent(Constants.Events.opsLeftStart)
            // console.log("----------move left2----------",wordPos, pos, leftDis)
            return
        }

        const rightNode = this.RightNode.getComponent(UITransform)!;
        let wordPos2 = rightNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let realPos2 = new Vec3(wordPos2.x-cPos.x, wordPos2.y - cPos.y, wordPos2.z - cPos.z)
        let rightDis = Public.GetDistance2D(realPos2, pos)
        // console.log("----------move Right1----------",wordPos2, pos, rightDis)
        if(rightDis<80){
            this.RightNode.setScale(new Vec3(0.9, 0.9,1))
            this.opsLeftOrRight = "right"
            clientEvent.dispatchEvent(Constants.Events.opsRightStart)
            // console.log("----------move Right2----------",wordPos2, pos, rightDis)
            return
        }

        const jumpNode = this.UpNode.getComponent(UITransform)!;
        let wordPos3 = jumpNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let realPos3 = new Vec3(wordPos3.x-cPos.x, wordPos3.y - cPos.y, wordPos3.z - cPos.z)
        // console.log("----------getLocationInView---------- touch",pos,"--worldTouch", wordPos)
        let dis = Public.GetDistance2D(realPos3, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(dis<80){
            this.isJumpPush = true
            this.UpNode.setScale(new Vec3(0.9, 0.9,1))
            this.OnClickUp()
            // this.opsLeftOrRight = "left"
            // clientEvent.dispatchEvent(Constants.Events.opsLeftStart)
            // console.log("----------move left2----------",wordPos, pos, leftDis)
            return
        }
    }

    private _touchMove(event:EventTouch){
        clientEvent.dispatchEvent(Constants.Events.touchMove, event)
        // console.log("----------pos2----------",this.LeftNode.position)
    }

    private _touchEnd(event:EventTouch){//当手指在目标节点区域内离开屏幕时。
        clientEvent.dispatchEvent(Constants.Events.touchEnd, event)
        let pos = event.getUILocation()
        let cPos = Public.MainCamera.node.position
        // if(this.opsLeftOrRight=="left"){
        //     //如果是左键松开
        //     const leftNode1 = this.LeftNode.getComponent(UITransform)!;
        //     let wordPos = leftNode1.convertToWorldSpaceAR(new Vec3(0,0,0))
        //     let realPos = new Vec3(wordPos.x-cPos.x, wordPos.y - cPos.y, wordPos.z - cPos.z)
        //     let leftDis = Public.GetDistance2D(realPos, pos)
        //     if(leftDis<80){
        //         clientEvent.dispatchEvent(Constants.Events.opsLeftEnd)
        //         this.LeftNode.setScale(new Vec3(1,1,1))
        //         return
        //     }
        // }else{
        //     //如果右键松开
        //     const rightNode = this.RightNode.getComponent(UITransform)!;
        //     let wordPos2 = rightNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        //     let realPos2 = new Vec3(wordPos2.x-cPos.x, wordPos2.y - cPos.y, wordPos2.z - cPos.z)
        //     let rightDis = Public.GetDistance2D(realPos2, pos)
        //     if(rightDis<80){
        //         clientEvent.dispatchEvent(Constants.Events.opsRightEnd)
        //         this.RightNode.setScale(new Vec3(1,1,1))
        //         return
        //     }
        // }

        //如果是跳跃键松开
        const jumpNode = this.UpNode.getComponent(UITransform)!;
        let wordPos3 = jumpNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let realPos3 = new Vec3(wordPos3.x-cPos.x, wordPos3.y - cPos.y, wordPos3.z - cPos.z)
        let dis = Public.GetDistance2D(realPos3, pos)
        if(dis<80){
            // console.log("-------end Pos---", pos)
            if(this.isJumpPush==true){
                this.isJumpPush = false
                this.UpNode.setScale(new Vec3(1,1,1))
            }
        }else{
            if(this.opsLeftOrRight=="left"){
                //如果是左键松开
                clientEvent.dispatchEvent(Constants.Events.opsLeftEnd)
                this.LeftNode.setScale(new Vec3(1,1,1))
            }else{
                //如果右键松开
                clientEvent.dispatchEvent(Constants.Events.opsRightEnd)
                this.RightNode.setScale(new Vec3(1,1,1))
            }
        }

        // console.log("----------pos3----------",this.LeftNode.position)
    }

    private _touchCancel(event:EventTouch){ //当手指在目标节点区域外离开屏幕时。
        clientEvent.dispatchEvent(Constants.Events.touchCancel, event)
        // console.log("----------pos4----------",this.LeftNode.position)
    }

    public OnClickUp(){
        audioManager.instance.playSound(Constants.Sounds.jump)
        clientEvent.dispatchEvent(Constants.Events.opsUp)
    }

    public OnClickDown(){
        // clientEvent.dispatchEvent(Constants.Events.opsUp)
    }

    public OnClickPrompt(){
        // uiManager.instance.hideDialog(Constants.Dialogs.level1)
        // uiManager.instance.hideDialog(Constants.Dialogs.ops)
        clientEvent.dispatchEvent(Constants.Events.clearLevelInsertTimeout)
        // uiManager.instance.showDialog(Constants.Dialogs.prompt)

        //看广告激励广告
        if(this.isSeenVideo==false){
            AdCtl.instance.HideBannerAD()
            AdCtl.instance.ShowRewardVideoAd(()=>{
                this.isSeenVideo = true
                AdCtl.instance.ShowBannerAd("game")
                TyqEventMgr.ins.sendEvent("观看激励视频-获取提示关卡"+Public.CurLevelNum);
                // this.close()
                uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoSuccess, cb:()=>{
                        uiManager.instance.showDialog(Constants.Dialogs.promptRes)
                    }})
            }, ()=>{
                AdCtl.instance.ShowBannerAd("game")
                uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoFail, cb:()=>{

                    }})
            });
        }else{
            uiManager.instance.showDialog(Constants.Dialogs.promptRes)
        }


        AdCtl.instance.HideCustomLevelUI()
        AdCtl.instance.HideMainCanvas()
        AdCtl.instance.HideBannerAD()
    }

    public OnClickPause(){
        clientEvent.dispatchEvent(Constants.Events.clearLevelInsertTimeout)
        uiManager.instance.showDialog(Constants.Dialogs.menu)
        AdCtl.instance.HideCustomLevelUI()
        AdCtl.instance.HideMainCanvas()
        AdCtl.instance.HideBannerAD()
    }

    public restart(){
        Public.CurLevelRestartCnt += 1
        uiManager.instance.destroyDialog(Constants.Dialogs.ops)
        uiManager.instance.destroyDialog(Public.GetRightLevelPath())
        Public.LoadLevel(uiManager.instance)
    }

    public OnClickRestart(){
        if(AdCtl.instance.isNeedShowAdToContinue()==true){
            console.log("-----restartCnt121------",AdCtl.instance.restartCnt )
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.restart, cb:()=> {
                    AdCtl.instance.ShowRewardVideoAd(()=>{
                        console.warn("------1执行video成功回调--")
                        AdCtl.instance.restartCnt=0
                        clientEvent.dispatchEvent(Constants.Events.restart)
                        uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoSuccess, cb:()=>{
                            }})
                    }, ()=>{
                        console.warn("------2执行video失败回调--")
                        uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.videoFail, cb:()=>{
                            }})
                    });

                }
            })


        }else{
            console.log("-----restartCnt------",AdCtl.instance.restartCnt )
            AdCtl.instance.restartCnt+=1
            uiManager.instance.showDialog(Constants.Dialogs.adRes, {type: Constants.WinType.restart, cb:()=> {
                    clientEvent.dispatchEvent(Constants.Events.restart)
                }
            })
        }

    }

    update (deltaTime: number) {
        this.calShowTipTime+=deltaTime

        if(this.calShowTipTime>Constants.PromptInterval){
            this.calShowTipTime=-10000
            this.PromptDialog.active = true
            tween(this.PromptDialog).delay(0.5)
                .to(0.1, {scale:new Vec3(1.1, 1.1, 1.1)},  { easing: 'cubicInOut' })
                .to(0.1, {scale:new Vec3(1, 1, 1)},  { easing: 'cubicInOut' })
                .to(0.1, {scale:new Vec3(1.1, 1.1, 1.1)},  { easing: 'cubicInOut' })
                .to(0.1, {scale:new Vec3(1, 1, 1)},  { easing: 'cubicInOut' })
                .delay(2).call(()=>{
                    this.PromptDialog.active = false
                }).start()
        }

        this.calShakePromptTime += deltaTime
        if(this.calShakePromptTime>Constants.PromptShakeInterval){
            this.calShakePromptTime=0
            tween(this.PromptNode)
                .to(0.1, {angle: 10})
                .to(0.1, {angle: 0})
                .to(0.1, {angle: 10})
                .to(0.1, {angle: 0})
                .to(0.1, {angle: 10})
                .to(0.1, {angle: 0})
                .start()
        }

    }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
