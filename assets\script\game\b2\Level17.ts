
import { _decorator, UIOpacity, Node , tween, Vec3, Collider2D, Contact2DType, v2, RigidBody2D, UITransform, EventTouch, sp, ERigidBody2DType} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 * 牛郎织女
 */
 
@ccclass('Level17')
export class Level17 extends LevelDialogBase {

    @property(Node)
    ZhangFeiNode:Node = null

    @property(Node)
    BridgeNode:Node = null

    @property(Node)
    WaveNode:Node = null

    //大喝的时间累积
    shoutCalTime:number = 0
    shoutCalCnt:number = 0
    isFall:boolean=false
    isTouchMouth:boolean=false

    touchTimePass:number = 0
    isShutUp:boolean =false

    start(){
        super.start()
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        this.isLocked = true
        this.Key.active = false
        // this.showKey()
        this.WaveNode.active = false
        this.showKey()
    }

    flyKey(){
        //飞向大门
        Public.Bezier2DShow(this.Key, this.doorNode,0.5, 0, 300,0.8,(flyObj)=>{
            console.log("--------xxx--------")
            audioManager.instance.playSound(Constants.Sounds.unlock)
            //解锁
            this.isLocked = false
            this.doorNode.getChildByName("Lock").active = false;
            this.Key.active = false;
        })
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        //判断角色是否靠近张飞
        this.shoutCalTime += deltaTime
        // console.log("this.shoutCalTime-------", deltaTime, this.shoutCalTime)
        let posRole = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
        let posZF = this.ZhangFeiNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
        if(Public.GetDistance2D(posRole, posZF)<300){
            if(this.shoutCalTime>=1){
                this.shoutCalTime=0
                if(this.shoutCalCnt<=3 && this.isShutUp ==false){
                    this.shoutCalCnt+=1

                    // console.log("this.shoutCalTime-------", deltaTime, this.shoutCalTime)
                    audioManager.instance.playSound(Constants.Sounds.shout)
                    let spine = this.ZhangFeiNode.getChildByName("Spine").getComponent(sp.Skeleton)
                    spine.setAnimation(0, "angry", false)
                    spine.setCompleteListener((trackEntry :sp.spine.TrackEntry)=>{
                        // console.log("trackEnd------------", trackEntry)
                        spine.setAnimation(0, "standBy2", true)
                    })


                    Public.ShakeCamera()
                    this.WaveNode.active = true
                    this.WaveNode.setScale(new Vec3(0,0,0))
                    this.WaveNode.getComponent(UIOpacity).opacity = 100
                    tween(this.WaveNode).to(0.2, {scale: new Vec3(3,3,3)}).call(()=>{
                        this.WaveNode.active = false
                        // console.log("back-------", Constants.ImpulseUp/2)
                        let plus = -1
                        if(this.ZhangFeiNode.position.x < this.roleNode.position.x){
                            plus =1
                        }
                        this.roleNode.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(plus*Constants.ImpulseUp*0.8,0), false);
                    }).start()
                    tween(this.WaveNode.getComponent(UIOpacity)).to(0.2, {opacity:0}).start()

                }

                if(this.shoutCalCnt>=3 && this.isFall==false){
                    this.isFall=true

                    //桥先裂开，然后掉下去
                    audioManager.instance.playSound(Constants.Sounds.crush)
                    this.BridgeNode.getChildByName("Spine").active= true
                    this.BridgeNode.getChildByName("Sprite").active= false
                    tween(this.BridgeNode).delay(0.5).call(()=>{
                        let bPos = this.BridgeNode.position
                        tween(this.BridgeNode).to(3,{position:new Vec3(bPos.x, bPos.y-1600, bPos.z)}).start()
                        tween(this.ZhangFeiNode).to(3,{position:new Vec3(bPos.x, bPos.y-1600, bPos.z)}).start()
                        audioManager.instance.playSound(Constants.Sounds.drop)
                    }).start()

                }
            }
        }
        if(this.isTouchMouth==true){
            let diffTime = new Date().getTime() - this.touchTimePass
            // console.log("-0-----------diffTime--", diffTime)
            if(diffTime>3000){
                this.isShutUp = true
                this.ZhangFeiNode.getChildByName("Spine").getComponent(sp.Skeleton).setAnimation(0, "bizui", true)
            }
        }
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        //判断
        if(this.isTouchMouth==false && this.isShutUp == false){
            let startPos = this.ZhangFeiNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            console.log("-------pos------", startPos, pos)
            if(Public.GetDistance2D(startPos, pos)<300 ){
                this.isTouchMouth=true
                this.touchTimePass = new Date().getTime()
            }
        }
    }

    touchMove(event) {
        super.touchMove(event);
        if(this.isTouchMouth==true){
            let pos = event.getUILocation()
            let startPos = this.ZhangFeiNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            if(Public.GetDistance2D(startPos, pos)>150){
                this.isTouchMouth=false
            }
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        this.isTouchMouth=false
        // if(this.isTouchMouth==true){
        //     this.isTouchMouth=false
        //     let diffTime = new Date().getTime() - this.touchTimePass
        //     console.log("-0-----------diffTime--", diffTime)
        //     if(diffTime>3000){
        //         this.isShutUp = true
        //         this.ZhangFeiNode.getChildByName("Spine").getComponent(sp.Skeleton).setAnimation(0, "bizui", true)
        //     }
        // }
    }


}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
