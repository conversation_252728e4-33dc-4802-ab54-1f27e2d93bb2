import { _decorator, AudioClip, sys, AudioSource, assert, clamp01, warn } from "cc";
import { configuration } from "./configuration";
import { resourceUtil } from "./resourceUtil";
import { lodash } from "./lodash";
import {Constants} from "../game/Constants";
import {gameStorage} from "./gameStorage";

export class audioManager {
    private static _instance: audioManager;
    private static _audioSource?: AudioSource;

    static get instance () {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new audioManager();
        return this._instance;
    }

    soundVolume: number = 1;

    // init AudioManager in GameRoot.
    init (audioSource: AudioSource) {
        this.soundVolume = this.getConfiguration(false) ? 1 : 0;

        audioManager._audioSource = audioSource;

        //如果刚刚进入，那么配置为ok
        let isMusicOpen = gameStorage.getString(Constants.CacheDataKey.music)
        if(isMusicOpen==undefined){
            gameStorage.getString(Constants.CacheDataKey.music,'true')
        }
        let isSoundOpen = gameStorage.getString(Constants.CacheDataKey.sound)
        if(isSoundOpen==undefined){
            gameStorage.getString(Constants.CacheDataKey.sound,'true')
        }
        // console.log("---------检查默认配置---", isSoundOpen)
    }

    getConfiguration (isMusic: boolean) {
        let state;
        if (isMusic) {
            state = gameStorage.getString(Constants.CacheDataKey.music);
        } else {
            state = gameStorage.getString(Constants.CacheDataKey.sound);
        }

        console.log('Config for [' + (isMusic ? 'Music' : 'Sound') + '] is ' + state);

        return state === undefined || state === 'true';
    }

    /**
     * 播放音乐
     * @param {String} name 音乐名称可通过constants.AUDIO_MUSIC 获取
     * @param {Boolean} loop 是否循环播放
     */
    playMusic (loop: boolean) {
        const audioSource = audioManager._audioSource!;
        assert(audioSource, 'AudioManager not inited!');

        audioSource.loop = loop;
        if (!audioSource.playing) {
            audioSource.play();
        }
    }

    /**
     * 播放音效
     * @param {String} name 音效名称可通过constants.AUDIO_SOUND 获取
     */
    playSound (name:string) {
        const audioSource = audioManager._audioSource!;
        assert(audioSource, 'AudioManager not inited!');

        //音效一般是多个的，不会只有一个
        let path = 'gamePackage/audio/sound/';
        // if (name !== 'click') {
        //     path = 'gamePackage/' + path; //微信特殊处理，除一开场的音乐，其余的放在子包里头
        // }

        resourceUtil.loadRes(path + name, AudioClip, (err, clip)=> {
            if (err) {
                warn('load audioClip failed: ', err);
                return;
            }

            // NOTE: the second parameter is volume scale.
            if(audioSource.volume==0){
                audioSource.volume=1
                let scale = audioSource.volume ? this.soundVolume / audioSource.volume : 0
                console.log("#######volume scale----------", scale, audioSource.volume, this.soundVolume)
                audioSource.playOneShot(clip, scale);
                audioSource.volume=0
            }else{
                let scale = audioSource.volume ? this.soundVolume / audioSource.volume : 0
                audioSource.playOneShot(clip, scale);
            }

        });

    }

    setMusicVolume (flag: number) {
        const audioSource = audioManager._audioSource!;
        assert(audioSource, 'AudioManager not inited!');

        flag = clamp01(flag);
        audioSource.volume = flag;
    }

    setSoundVolume (flag: number) {
        this.soundVolume = flag;
    }

    openMusic () {
        this.setMusicVolume(0.8);
        gameStorage.setString(Constants.CacheDataKey.music, 'true');
    }

    closeMusic () {
        this.setMusicVolume(0);
        gameStorage.setString(Constants.CacheDataKey.music, 'false');
    }

    openSound () {
        this.setSoundVolume(1);
        gameStorage.setString(Constants.CacheDataKey.sound, 'true');
    }

    closeSound () {
        this.setSoundVolume(0);
        gameStorage.setString(Constants.CacheDataKey.sound, 'false');
    }
}
