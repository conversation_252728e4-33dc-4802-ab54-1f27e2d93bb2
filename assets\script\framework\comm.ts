
import { _decorator, Component, Node, <PERSON>Integer, tween } from 'cc';
import {uiManager} from "./uiManager";
import {Constants} from "../game/Constants";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = dialogBase
 * DateTime = Thu Feb 24 2022 15:10:51 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = dialogBase.ts
 * FileBasenameNoExtension = dialogBase
 * URL = db://assets/script/framework/dialogBase.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('comm')
export class comm extends Component {
    // [1]
    // dummy = '';

    async delayTime(time:number){
        return new Promise<void>(resolve=>{
            tween(this.node).delay(time).call(()=>{
                resolve();
            }).start();
            // this.scheduleOnce(()=>{
            // 	resolve();
            // },time)
        })
        // return new Promise(resolve=>{
        // 	this.scheduleOnce(()=>{
        // 		resolve();
        // 	},time)
        // })
    }

    // update (deltaTime: number) {
    //     // [4]
    // }
}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
