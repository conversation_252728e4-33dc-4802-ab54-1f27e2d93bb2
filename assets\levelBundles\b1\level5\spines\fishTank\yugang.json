{"skeleton": {"hash": "Pk/2yY+5HDo", "spine": "3.8-from-4.0.09", "x": -83.82, "y": -1.37, "width": 171, "height": 123, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/如鱼得水"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 1.11, "y": 39.46}, {"name": "bone2", "parent": "bone", "length": 14.34, "rotation": 79.79, "x": 36.54, "y": -17.4}, {"name": "bone3", "parent": "bone2", "length": 12.79, "rotation": 27.57, "x": 15.36, "y": 0.06}, {"name": "bone4", "parent": "bone3", "length": 13.13, "rotation": -26.27, "x": 13.15, "y": -0.11}, {"name": "bone5", "parent": "bone", "length": 15.48, "rotation": 65.75, "x": 50.27, "y": -14.85}, {"name": "bone6", "parent": "bone5", "length": 11.92, "rotation": 13.18, "x": 16.9, "y": -0.06}, {"name": "bone7", "parent": "bone6", "length": 10.05, "rotation": -7.36, "x": 13.29, "y": 0.27}, {"name": "bone8", "parent": "bone7", "length": 7.91, "rotation": -26.57, "x": 11.06, "y": -0.6}, {"name": "bone9", "parent": "bone", "x": -48.04, "y": -4.19}, {"name": "bone10", "parent": "bone", "x": -65.89, "y": 11.04}, {"name": "bone11", "parent": "bone", "x": -54.23, "y": 25.32}], "slots": [{"name": "s", "bone": "root", "attachment": "s"}, {"name": "haicao1", "bone": "bone2", "attachment": "haicao1"}, {"name": "haicao2", "bone": "bone5", "attachment": "haicao2"}, {"name": "yg", "bone": "root", "attachment": "yg"}, {"name": "w", "bone": "root", "attachment": "w"}, {"name": "p3", "bone": "bone11", "attachment": "p3"}, {"name": "p2", "bone": "bone10", "attachment": "p2"}, {"name": "p1", "bone": "bone9", "attachment": "p1"}], "skins": [{"name": "default", "attachments": {"haicao1": {"haicao1": {"type": "mesh", "uvs": [0.33831, 0.9714, 0.19671, 0.88562, 0.07084, 0.76098, 0.05197, 0.66315, 0.14951, 0.53583, 0.15266, 0.44871, 0.03309, 0.34284, 0, 0.23696, 0.15895, 0.07479, 0.41068, 0, 0.56486, 0, 0.72534, 0.08552, 0.78197, 0.21284, 0.74422, 0.34284, 0.75051, 0.43531, 0.86693, 0.52913, 0.92986, 0.62696, 0.85749, 0.75562, 0.81344, 0.84542, 0.82288, 0.95934, 0.74736, 0.9915, 0.36662, 0.99954], "triangles": [11, 6, 7, 9, 10, 11, 8, 9, 11, 11, 12, 6, 6, 12, 5, 8, 11, 7, 13, 5, 12, 5, 13, 14, 15, 5, 14, 15, 4, 5, 4, 15, 16, 17, 2, 3, 17, 4, 16, 4, 17, 3, 17, 1, 2, 18, 1, 17, 0, 1, 18, 18, 20, 0, 19, 20, 18, 21, 0, 20], "vertices": [1, 2, -6.09, 0.62, 1, 1, 2, -2.11, 4.65, 1, 2, 2, 4, 8.69, 0.99072, 3, -6.08, 12.91, 0.00928, 2, 2, 9.12, 10.05, 0.88549, 3, -0.91, 11.75, 0.11451, 2, 2, 16.29, 9.06, 0.335, 3, 4.99, 7.56, 0.665, 3, 2, 20.93, 9.83, 0.0432, 3, 9.45, 6.09, 0.92372, 4, -6.06, 3.92, 0.03308, 2, 3, 15.73, 7.01, 0.32324, 4, -0.84, 7.53, 0.67676, 2, 3, 21.42, 6.03, 0.0144, 4, 4.69, 9.16, 0.9856, 1, 4, 13.91, 6.91, 1, 1, 4, 18.8, 1.82, 1, 1, 4, 19.35, -1.69, 1, 2, 3, 24.25, -12.34, 1e-05, 4, 15.36, -6.05, 0.99999, 2, 3, 17.29, -11.53, 0.05645, 4, 8.77, -8.4, 0.94355, 2, 3, 10.85, -8.61, 0.4928, 4, 1.7, -8.63, 0.5072, 2, 3, 6.04, -7.25, 0.90256, 4, -3.21, -9.55, 0.09744, 3, 2, 19.57, -7.11, 0.10702, 3, 0.41, -8.3, 0.89188, 4, -7.8, -12.98, 0.0011, 2, 2, 14.62, -9.47, 0.52588, 3, -5.07, -8.1, 0.47412, 2, 2, 7.49, -9.07, 0.94654, 3, -11.2, -4.44, 0.05346, 2, 2, 2.54, -8.93, 0.99936, 3, -15.53, -2.03, 0.00064, 1, 2, -3.48, -10.23, 1, 1, 2, -5.49, -8.83, 1, 1, 2, -7.47, -0.29, 1], "hull": 22, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 0, 42], "width": 23, "height": 54}}, "haicao2": {"haicao2": {"type": "mesh", "uvs": [0.06638, 0.94621, 0.07572, 0.78011, 0.06638, 0.62469, 0.16677, 0.51436, 0.31384, 0.44792, 0.31384, 0.34372, 0.34886, 0.22033, 0.44925, 0.08152, 0.64068, 0.00559, 0.82978, 0, 0.96052, 0.07203, 1, 0.14203, 0.9862, 0.25474, 0.85546, 0.35677, 0.70605, 0.43626, 0.69905, 0.50152, 0.70138, 0.59168, 0.69671, 0.68778, 0.61967, 0.77083, 0.53563, 0.82066, 0.49127, 0.85744, 0.47726, 0.95947, 0.44925, 0.98676, 0.11541, 0.98557], "triangles": [11, 6, 7, 12, 13, 11, 8, 11, 7, 8, 9, 10, 10, 11, 8, 5, 6, 13, 13, 6, 11, 16, 4, 15, 4, 16, 3, 15, 4, 14, 4, 5, 14, 14, 5, 13, 22, 23, 21, 23, 0, 21, 21, 0, 20, 0, 1, 20, 20, 1, 19, 18, 19, 2, 19, 1, 2, 18, 2, 17, 17, 2, 16, 16, 2, 3], "vertices": [1, 5, -9.13, 1.53, 1, 1, 5, 0.23, 5.43, 1, 2, 5, 8.76, 9.59, 0.97938, 6, -5.73, 11.25, 0.02062, 2, 5, 16.17, 9.51, 0.67283, 6, 1.47, 9.48, 0.32717, 3, 5, 21.74, 7.02, 0.14055, 6, 6.32, 5.79, 0.82072, 7, -7.62, 4.58, 0.03874, 3, 5, 27.53, 9.63, 0.00057, 6, 12.56, 7.01, 0.36614, 7, -1.59, 6.59, 0.63329, 3, 6, 20.16, 7.39, 0.00058, 7, 5.9, 7.94, 0.99197, 8, -8.44, 5.33, 0.00745, 2, 7, 14.91, 7.67, 0.41692, 8, -0.25, 9.12, 0.58308, 2, 7, 21.18, 3.5, 0.02514, 8, 7.22, 8.2, 0.97486, 1, 8, 11.61, 4.3, 1, 2, 6, 32.68, -9.48, 1e-05, 8, 11.37, -1.68, 0.99999, 3, 6, 28.72, -11.5, 0.00506, 7, 16.81, -9.7, 0.00153, 8, 9.21, -5.56, 0.99341, 3, 6, 21.89, -12.4, 0.06777, 7, 10.15, -11.47, 0.1305, 8, 4.05, -10.12, 0.80173, 3, 6, 15.01, -9.62, 0.35697, 7, 2.97, -9.59, 0.34293, 8, -3.22, -11.66, 0.30009, 3, 6, 9.36, -6.01, 0.93148, 7, -3.1, -6.73, 0.04242, 8, -9.92, -11.81, 0.0261, 3, 5, 23.66, -5.21, 0.01173, 6, 5.41, -6.56, 0.98743, 8, -12.89, -14.47, 0.00085, 2, 5, 18.68, -7.54, 0.33085, 6, 0.03, -7.69, 0.66915, 2, 5, 13.27, -9.81, 0.82385, 6, -5.76, -8.67, 0.17615, 2, 5, 7.67, -9.71, 0.98358, 6, -11.19, -7.3, 0.01642, 1, 5, 3.83, -8.59, 1, 1, 5, 1.22, -8.25, 1, 1, 5, -4.63, -10.41, 1, 1, 5, -6.51, -10.31, 1, 1, 5, -10.69, -0.84, 1], "hull": 24, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 0, 46], "width": 31, "height": 61}}, "p1": {"p1": {"x": 0.61, "y": -0.63, "width": 13, "height": 14}}, "p2": {"p2": {"x": 0.46, "y": -0.36, "width": 11, "height": 11}}, "p3": {"p3": {"x": 0.8, "y": -0.64, "width": 17, "height": 17}}, "s": {"s": {"x": 1.68, "y": 47.13, "width": 167, "height": 93}}, "w": {"w": {"x": 1.18, "y": 82.13, "width": 146, "height": 23}}, "yg": {"yg": {"x": 1.68, "y": 60.13, "width": 171, "height": 123}}}}], "animations": {"nowater": {"slots": {"p1": {"attachment": [{"name": null}]}, "p2": {"attachment": [{"name": null}]}, "p3": {"attachment": [{"name": null}]}, "s": {"attachment": [{"name": null}]}, "w": {"attachment": [{"name": null}]}}, "bones": {"bone2": {"rotate": [{"angle": 67.58}]}, "bone3": {"rotate": [{"angle": 40.42}]}, "bone5": {"rotate": [{"angle": 67.42}]}, "bone6": {"rotate": [{"angle": 45.55}]}, "bone8": {"rotate": [{"angle": 37.43}]}, "bone4": {"rotate": [{"angle": 19.94}]}, "bone7": {"rotate": [{"angle": 6.65}]}}}, "water": {"slots": {"p1": {"color": [{"color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00"}]}, "p2": {"color": [{"color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}]}, "p3": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00"}]}}, "bones": {"bone2": {"rotate": [{}, {"time": 0.3, "angle": -1.2}, {"time": 1.3333}]}, "bone3": {"rotate": [{}, {"time": 0.3, "angle": -8.71}, {"time": 1.3333}]}, "bone4": {"rotate": [{"angle": 7.5}, {"time": 0.3, "angle": 6.3}, {"time": 0.9, "angle": 15.44}, {"time": 1.3333, "angle": 7.5}]}, "bone5": {"rotate": [{}, {"time": 0.3333, "angle": -4.3}, {"time": 1.3333}]}, "bone7": {"rotate": [{}, {"time": 0.6667, "angle": -11.68}, {"time": 1.3333}]}, "bone9": {"translate": [{"x": 6.48, "y": -16.57}, {"time": 0.3667, "x": -0.24, "y": 22.57}, {"time": 0.6, "x": 3.36, "y": 35.54}]}, "bone11": {"translate": [{"x": 70.12, "y": -46.06}, {"time": 0.2333, "x": 75.62, "y": -47.89}, {"time": 0.8667, "x": 74.93, "y": -2.56}, {"time": 1.2333, "x": 79.67, "y": 11.75}]}, "bone10": {"translate": [{"x": 93.2, "y": -23.54}, {"time": 0.7, "x": 93.52, "y": 11.77}, {"time": 1.2, "x": 97.65, "y": 22.58}]}}}}}