
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, ERigidBody2DType, UITransform, v2, RigidBody2D, sp, UIOpacity, BoxCollider2D} from 'cc';
import {Constants} from "../Constants";
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level9')
export class Level9 extends LevelDialogBase {

    @property(Node)
    HousePicNode:Node = null

    @property(Node)
    BirdPicNode:Node = null

    @property(Node)
    HouseNode:Node = null

    @property(Node)
    BirdNode:Node = null

    @property(Node)
    HouseBirdNode:Node = null

    @property(Node)
    RoleNode2:Node = null

    isShowRealHose:boolean = false
    isRunHouse:boolean = false
    isRideHouse:boolean = false

    start(){
        super.start()
        this.HousePicNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onHousePicContact, this);
        this.HouseNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onHouseContact, this);
        this.BirdPicNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBirdPicContact, this);
        this.RoleNode2.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onRole2BeginContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        this.HouseNode.active = false
        this.BirdNode.active = false

        this.broaderY = [-1000, 6000]
    }

    onRole2BeginContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        let h = selfCollider.node.getComponent(UITransform).contentSize.height
        if(selfCollider.node.position.y - h/2 > otherCollider.node.position.y){
            this.isJumping = false;
            // console.log("jump-----", this.isJumping, this.isInWater)
        }
    }

    update(deltaTime: number) {
        super.update(deltaTime);
        if(this.isEnd) return
        //如果角色靠近马踏飞燕，就被吸收
        if(this.HouseBirdNode.active==true){
            if(this.isRideHouse==false){
                //判断距离
                let rolePos = this.roleNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let housePos = this.HouseBirdNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                housePos.y = housePos.y + 50
                if(Public.GetDistance2D(rolePos, housePos)<60){
                    this.isRideHouse=true
                    this.isStop=true
                    // this.roleNode.getComponent(BoxCollider2D).offset.y = -68
                    // this.roleNode.getComponent(BoxCollider2D).size.height = 200
                    Public.Bezier2DShow(this.roleNode, this.HouseBirdNode.getChildByName("Pos"), 0.5, 0,300,1,()=>{
                        // this.roleNode.parent = this.HouseBirdNode.getChildByName("Pos")
                        // this.HouseBirdNode.parent = this.roleNode.getChildByName("House")
                        // this.HouseBirdNode.setPosition(new Vec3(0,-68,0))
                        this.roleNode.active = false
                        this.HouseBirdNode.active = false
                        // this.roleNode.getComponent(RigidBody2D).enabled=false
                        this.RoleNode2.active = true
                        this.RoleNode2.setPosition(this.roleNode.position)
                        this.showKey()
                    })
                }
            }
        }
        // console.log("---------isStop- true1------", this.isStop, this.isRunHouse)
        if(this.isStop==true){
            let speed = Constants.MoveXSpeed;
            // console.log("---------isStop- true2------", this.moveRight, this.moveLeft)
            if(this.moveLeft){
                this.RoleNode2.setPosition(new Vec3(this.RoleNode2.position.x-deltaTime*speed, this.RoleNode2.position.y, this.RoleNode2.position.z))
                // this.HouseBirdNode.setPosition(new Vec3(this.HouseBirdNode.position.x-deltaTime*speed, this.HouseBirdNode.position.y, this.HouseBirdNode.position.z))
            }else if(this.moveRight){
                this.RoleNode2.setPosition(new Vec3(this.RoleNode2.position.x+deltaTime*speed, this.RoleNode2.position.y, this.RoleNode2.position.z))
                // this.HouseBirdNode.setPosition(new Vec3(this.HouseBirdNode.position.x+deltaTime*speed, this.HouseBirdNode.position.y, this.HouseBirdNode.position.z))
            }

            //判断是否胜利
            if(this.isLocked==false){
                const roleNodeUIT = this.RoleNode2.getComponent(UITransform)!;
                let roleNodeUITWP = roleNodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
                const doorNodeUIT = this.doorNode.getComponent(UITransform)!;
                let doorNodeUITWP = doorNodeUIT.convertToWorldSpaceAR(new Vec3(0,0,0))
                let doorDis = Public.GetDistance2D(roleNodeUITWP, doorNodeUITWP)
                if(doorDis<80){
                    this.Win()
                }
            }

            //判断钥匙是否显示，如果显示，每300毫秒检查一次
            if(this.Key && this.Key.active==true){
                let worldPosKey = this.Key.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let worldPosRole = this.RoleNode2.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
                let dis = Public.GetDistance2D(worldPosKey, worldPosRole)
                if(dis<200){
                // if(Public.IsPointInNodeArea2D(this.RoleNode2, worldPosKey)){
                    if(this.keyTouchOnce == false){
                        this.keyTouchOnce = true
                        //飞向大门
                        Public.Bezier2DShow(this.Key, this.doorNode,0.5, 0, 50,0.8,(flyObj)=>{
                            console.log("--------xxx--------")
                            audioManager.instance.playSound(Constants.Sounds.unlock)
                            //解锁
                            this.isLocked = false
                            this.doorNode.getChildByName("Lock").active = false;
                            this.Key.active = false;
                        })
                    }
                }
            }
        }
    }

    opsUp(){
        if(this.isStop==false){
            super.opsUp()
        }else{
            if(this.isJumping==false){
                this.isJumping = true;
                //施加一个向上的脉冲力,只有底部触碰后才可以再次跳跃。
                this.RoleNode2.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(0,Constants.ImpulseUp*6.5), false);
            }
        }
    }

    opsLeftStart(){
        super.opsLeftStart()
        this.moveLeft = true;
        let s = this.RoleNode2.scale
        this.RoleNode2.setScale(new Vec3(-Math.abs(s.x), s.y, s.z))
    }

    opsRightStart() {
        super.opsRightStart();
        let s = this.RoleNode2.scale
        this.RoleNode2.setScale(new Vec3(Math.abs(s.x), s.y, s.z))
    }

    onHouseContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            if(otherCollider.node.name=="BirdNode"){
                audioManager.instance.playSound(Constants.Sounds.disappear)
                let o = this.HouseNode.getChildByName("houseRun").getComponent(UIOpacity)
                let o2 = this.BirdNode.getChildByName("bird").getComponent(UIOpacity)
                // console.log("-------ooo--------", o)
                // tween(o).to(1,{opacity:0}).call(()=>{
                //     this.HouseNode.active = false
                // }).start()
                tween(o2).delay(0.2).call(()=>{
                    this.HouseNode.active = false
                    this.BirdNode.active = false
                    //展示马踏飞燕动画
                    this.HouseBirdNode.active = true
                    let p = this.BirdNode.position
                    this.HouseBirdNode.setPosition(new Vec3(p.x, p.y+60, p.z))
                    // this.HouseBirdNode.setScale(new Vec3(0.5,0.5,1))
                    // this.HouseBirdNode.getChildByName("Light").active=true
                    // audioManager.instance.playSound(Constants.Sounds.appear)
                    // tween(this.HouseBirdNode).to(1, {scale: new Vec3(1,1,1)}).start()
                    // tween(this.HouseBirdNode.getChildByName("Light"))
                    //     .to(1, {angle:360}).delay(0.2)
                    //     .call(()=>{
                    //         this.HouseBirdNode.getChildByName("Light").active=false
                    //         this.isShowRealHose = true
                    //         this.showKey()
                    // }).start()
                }).start()
            }
            if(otherCollider.node.name == "Place" && this.isRunHouse==false){
                this.isRunHouse = true
                this.HouseNode.getChildByName("houseRun").getComponent(sp.Skeleton).setAnimation(0, "run1", true)
                let hp = this.HouseNode.position
                tween(this.HouseNode).to(6, {position: new Vec3(hp.x+2000, hp.y, hp.z)}).start()
                this.scheduleOnce(()=>{
                    this.HouseNode.getComponent(RigidBody2D).enabled = false
                },0.1)
            }
            //马在燕子上面，就可以触发马踏飞燕的动画，消失然后中间显示马踏飞燕的图案
            // tween(this.roleNode).delay(0.1).call(()=>{
            //     //起到马上
            //     this.HouseNode.active = false
            //     this.roleNode.getChildByName("house").active = true
            //     this.isRideHouse = true
            // }).start()
        })
    }

    onHousePicContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.HousePicNode.position.y
            tween(this.HousePicNode)
                .to(0.05,{position:new Vec3(this.HousePicNode.position.x,y+10,this.HousePicNode.position.z)})
                .to(0.1,{position:new Vec3(this.HousePicNode.position.x,y,this.HousePicNode.position.z)})
                .call(()=>{
                    //让画框消失
                    this.HousePicNode.active = false
                    this.HouseNode.getComponent(RigidBody2D).type = ERigidBody2DType.Dynamic
                    this.HouseNode.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(0, 10), false);
                    //让马飞出来
                    this.HouseNode.active = true
                })
                .start()
        })
    }

    onBirdPicContact (selfCollider: Collider2D, otherCollider: Collider2D, contact) {
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            audioManager.instance.playSound(Constants.Sounds.out)
            let y = this.BirdPicNode.position.y
            tween(this.BirdPicNode)
                .to(0.05,{position:new Vec3(this.BirdPicNode.position.x,y+10,this.BirdPicNode.position.z)})
                .to(0.1,{position:new Vec3(this.BirdPicNode.position.x,y,this.BirdPicNode.position.z)})
                .call(()=>{
                    //让画框消失
                    this.BirdPicNode.active = false
                    // this.BirdNode.getComponent(RigidBody2D).type = ERigidBody2DType.Dynamic
                    // this.BirdNode.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(0, 10), false);
                    //让燕子飞出来
                    this.BirdNode.active = true
                })
                .start()
        })
    }


    touchStart(event){
        super.touchStart(event)

        let pos = event.getUILocation()
        const roleNode = this.BirdNode.getComponent(UITransform)!;
        let wordPos = roleNode.convertToWorldSpaceAR(new Vec3(0,0,0))
        let dis = Public.GetDistance2D(wordPos, pos)
        // console.log("----------move left1----------",wordPos, pos, leftDis)
        if(dis<60 && this.BirdNode.active == true){
            this.BirdNode.getComponent(RigidBody2D).applyLinearImpulseToCenter(v2(0, 45), false);
            console.log("self, touch self-------", dis)
        }
    }

}
