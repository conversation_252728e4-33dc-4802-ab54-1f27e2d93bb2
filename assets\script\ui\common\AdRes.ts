import {_decorator, Label} from 'cc';
import {dialogBase} from "../../framework/dialogBase";
import {Constants} from "../../game/Constants";
import {uiManager} from "../../framework/uiManager";
import {Public} from "../../game/Public";
import {clientEvent} from "../../framework/clientEvent";
import {AdCtl} from "../../channel/AdCtl";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Success
 * DateTime = Mon Feb 28 2022 17:54:00 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Success.ts
 * FileBasenameNoExtension = Success
 * URL = db://assets/script/ui/common/Success.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('AdRes')
export class AdRes extends dialogBase {
    @property(Label)
    PromptLabel:Label = null!
    @property(Label)
    BtnLabel:Label = null!

    args=null
    cb=null

    show(args){
        this.args = args
        this.cb = args.cb
        super.show(args)
        if(args.type==Constants.WinType.videoSuccess){
            this.PromptLabel.string = "恭喜获得视频奖励"
            this.BtnLabel.string = "继续"
        }else if(args.type==Constants.WinType.videoFail){
            this.PromptLabel.string = "视频未看完，未获得奖励"
            this.BtnLabel.string = "继续"
        }else if(args.type==Constants.WinType.levelSuccess){
            this.PromptLabel.string = "获得视频奖励"
            this.BtnLabel.string = "继续"
        }else if(args.type==Constants.WinType.levelFail){
            this.PromptLabel.string = "通关失败"
            this.BtnLabel.string = "继续"
        }else if(args.type==Constants.WinType.restart){
            this.PromptLabel.string = "即将重新开始"
            this.BtnLabel.string = "继续"
        }

        AdCtl.instance.HideAllCustomAD()
        AdCtl.instance.ShowBannerAd()
    }

    hide(){
        AdCtl.instance.HideBannerAD()
    }

    OnClickContinue(){
        uiManager.instance.hideDialog(Constants.Dialogs.adRes)
        this.cb && this.cb()
        // if(this.args.type==Constants.WinType.videoSuccess){
        // }else if(this.args.type==Constants.WinType.videoFail){
        // }else if(this.args.type==Constants.WinType.levelSuccess){
        // }else if(this.args.type==Constants.WinType.restart){
        //     this.restart()
        // }else if(this.args.type==Constants.WinType.levelFail){
        //     Public.CurLevelRestartCnt += 1
        //     this.restart()
        // }
    }

    restart(){
        Public.CurLevelRestartCnt += 1
        uiManager.instance.destroyDialog(Constants.Dialogs.ops)
        uiManager.instance.destroyDialog(Public.GetRightLevelPath())
        Public.LoadLevel(uiManager.instance)
    }

}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
