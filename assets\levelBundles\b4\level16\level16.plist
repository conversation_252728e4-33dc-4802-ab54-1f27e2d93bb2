<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>HillLeft.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,345},{622,343}}</string>
                <key>offset</key>
                <string>{-7,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{622,343}}</string>
                <key>sourceSize</key>
                <string>{636,343}</string>
            </dict>
            <key>HillRight.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{447,341}}</string>
                <key>offset</key>
                <string>{11,-1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{22,2},{447,341}}</string>
                <key>sourceSize</key>
                <string>{469,343}</string>
            </dict>
            <key>boy.png</key>
            <dict>
                <key>frame</key>
                <string>{{347,435},{64,110}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{64,110}}</string>
                <key>sourceSize</key>
                <string>{64,110}</string>
            </dict>
            <key>girl.png</key>
            <dict>
                <key>frame</key>
                <string>{{347,345},{88,150}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{88,150}}</string>
                <key>sourceSize</key>
                <string>{88,150}</string>
            </dict>
            <key>rainbow.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,969},{1069,267}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{1069,267}}</string>
                <key>sourceSize</key>
                <string>{1069,267}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>level16.png</string>
            <key>size</key>
            <string>{512,2048}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:3ddae0fe2268ea4b9914f4ea43a8e51e$</string>
            <key>textureFileName</key>
            <string>level16.png</string>
        </dict>
    </dict>
</plist>
