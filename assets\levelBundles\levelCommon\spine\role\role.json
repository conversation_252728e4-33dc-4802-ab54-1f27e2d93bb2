{"skeleton": {"hash": "aBD3YsmVV0I", "spine": "3.8-from-4.0.09", "x": -111.82, "y": 0.31, "width": 147.22, "height": 180.63, "images": "./spine/奶茶/images/", "audio": "E:/游戏项目/奶茶大冒险/spine"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 28.95, "rotation": 94.89, "x": -0.78, "y": 20.67}, {"name": "bone2", "parent": "bone", "length": 38.39, "rotation": 8.25, "x": 30.64, "y": -0.33}, {"name": "bone3", "parent": "bone2", "length": 32.72, "rotation": 23.13, "x": 42.9, "y": 3.43}, {"name": "bone4", "parent": "bone3", "length": 18.55, "rotation": 95.41, "x": 32.99, "y": 3.57}, {"name": "bone5", "parent": "bone4", "length": 7.9, "rotation": -1.3, "x": 19.47, "y": -6.39}, {"name": "bone6", "parent": "bone4", "length": 11.72, "rotation": 23.41, "x": 20.99, "y": -1.02}, {"name": "bone7", "parent": "bone2", "length": 11.19, "rotation": 123.8, "x": 6.69, "y": 29.36}, {"name": "bone8", "parent": "bone7", "length": 10.15, "rotation": 21.93, "x": 12.65, "y": 0.33}, {"name": "bone9", "parent": "bone2", "length": 9.14, "rotation": -145.28, "x": -5.97, "y": -25.35}, {"name": "bone10", "parent": "bone9", "length": 6.2, "rotation": -27.54, "x": 9.88, "y": -0.34}, {"name": "bone14", "parent": "bone2", "length": 10.15, "rotation": -93.02, "x": 46.35, "y": -4.32}, {"name": "bone15", "parent": "bone2", "length": 12.2, "rotation": -97.27, "x": 19.37, "y": 4.2}, {"name": "bone16", "parent": "bone2", "length": 6.52, "rotation": -112.61, "x": 0.13, "y": 8.13}, {"name": "bone11", "parent": "bone", "length": 11.44, "rotation": -142.55, "x": 4.37, "y": -7.87, "color": "2ebcffff"}, {"name": "bone12", "parent": "bone11", "length": 10.23, "rotation": -34.95, "x": 12.94, "y": -0.86, "color": "2ebcffff"}, {"name": "bone13", "parent": "bone", "length": 11.36, "rotation": 138.2, "x": 3.1, "y": 7.36, "color": "2ebcffff"}, {"name": "bone17", "parent": "bone13", "length": 10.76, "rotation": 32.24, "x": 12.43, "y": 0.58, "color": "2ebcffff"}, {"name": "bone18", "parent": "bone17", "length": 5.25, "rotation": -80.87, "x": 10.63, "y": -0.85, "color": "2ebcffff"}, {"name": "bone19", "parent": "bone12", "length": 5.08, "rotation": 78, "x": 10, "y": 0.79, "color": "2ebcffff"}, {"name": "1", "parent": "root", "x": -17.15, "y": 4.17, "color": "ff3f00ff"}, {"name": "2", "parent": "root", "x": -23.63, "y": 3.84, "color": "ff3f00ff"}, {"name": "3", "parent": "root", "x": 14.66, "y": 5.37, "color": "ff3f00ff"}, {"name": "4", "parent": "root", "x": 22.89, "y": 4.86, "color": "ff3f00ff"}, {"name": "bone20", "parent": "bone", "x": 10.84, "y": 9.93}, {"name": "bone21", "parent": "bone", "x": 20.12, "y": 14.95}, {"name": "bone22", "parent": "bone", "x": 15, "y": 3.88}, {"name": "bone23", "parent": "bone", "x": 11.18, "y": -5.27}, {"name": "bone24", "parent": "bone", "x": 15.34, "y": -12.84}, {"name": "bone25", "parent": "bone", "length": 10.39, "rotation": -164.01, "x": 3.11, "y": -6.3}, {"name": "bone26", "parent": "bone25", "length": 10.47, "rotation": -39.83, "x": 12.13, "y": -0.77}, {"name": "bone27", "parent": "bone", "length": 11.97, "rotation": 133.16, "x": -2.05, "y": -2.05, "color": "efff22ff"}, {"name": "bone28", "parent": "bone27", "length": 10.69, "rotation": -79.66, "x": 14.54, "y": -2.26, "color": "efff22ff"}, {"name": "bone29", "parent": "bone28", "length": 6.98, "rotation": 72.21, "x": 11.7, "y": -0.31, "color": "efff22ff"}, {"name": "bone30", "parent": "bone26", "length": 6.87, "rotation": 80.78, "x": 10.32, "y": 1.09}, {"name": "bone31", "parent": "root", "x": 27.4, "y": 75.99}, {"name": "bone32", "parent": "bone2", "x": 29.5, "y": 25.76}, {"name": "bone33", "parent": "bone2", "length": 10.28, "rotation": -103.14, "x": 6.59, "y": -42.22}, {"name": "bone34", "parent": "bone2", "length": 16.13, "rotation": 81.54, "x": 12.38, "y": 45.18}, {"name": "bone35", "parent": "bone2", "length": 7.9, "rotation": -72.36, "x": 23.11, "y": -27.11}, {"name": "bone36", "parent": "bone16", "length": 3.76, "rotation": -102.74, "x": 0.84, "y": -1.48}, {"name": "bone37", "parent": "bone36", "length": 3.31, "rotation": 34.6, "x": 4.15, "y": 0.35}, {"name": "bone38", "parent": "bone2", "x": 84.43, "y": -10.97}], "slots": [{"name": "3 教程素材", "bone": "root"}, {"name": "pao<PERSON><PERSON>", "bone": "bone25"}, {"name": "paolleg", "bone": "bone27"}, {"name": "lleg", "bone": "bone13", "attachment": "lleg"}, {"name": "rleg", "bone": "bone11", "attachment": "rleg"}, {"name": "lhand", "bone": "bone7", "attachment": "lhand"}, {"name": "rhand", "bone": "bone9", "attachment": "rhand"}, {"name": "body", "bone": "bone2", "attachment": "body"}, {"name": "han2", "bone": "bone5", "attachment": "han2"}, {"name": "han1", "bone": "bone6", "attachment": "han1"}, {"name": "zz5", "bone": "bone24", "attachment": "zz5"}, {"name": "zz4", "bone": "bone22", "attachment": "zz4"}, {"name": "zz3", "bone": "bone21", "attachment": "zz3"}, {"name": "zz2", "bone": "bone20", "attachment": "zz2"}, {"name": "zz1", "bone": "bone23", "attachment": "zz1"}, {"name": "BQ1sqzui", "bone": "bone16", "attachment": "BQzuiba"}, {"name": "BQmeimao", "bone": "bone14", "attachment": "BQtiaomeimao"}, {"name": "BQtiaomeimao", "bone": "bone14"}, {"name": "BQ1sqmeimao", "bone": "bone14"}, {"name": "BQ2sqmeimao", "bone": "bone31"}, {"name": "BQ2sqfh", "bone": "bone32"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "bone15", "attachment": "BQtiao"}, {"name": "BQ3sqfh", "bone": "bone31"}, {"name": "BQ3Q3", "bone": "bone34", "attachment": "BQ3Q3"}, {"name": "BQ3Q2", "bone": "bone31"}, {"name": "BQ3Q1", "bone": "bone35"}, {"name": "BQ2SQ2", "bone": "bone34"}, {"name": "BQ2SQ1", "bone": "bone33"}, {"name": "111", "bone": "bone38", "attachment": "111"}], "ik": [{"name": "1", "bones": ["bone13", "bone17"], "target": "1"}, {"name": "2", "order": 1, "bones": ["bone18"], "target": "2"}, {"name": "3", "order": 2, "bones": ["bone11", "bone12"], "target": "3", "bendPositive": false}, {"name": "4", "order": 3, "bones": ["bone19"], "target": "4"}], "skins": [{"name": "default", "attachments": {"111": {"111": {"x": -0.11, "y": -0.49, "rotation": -103.14, "width": 71, "height": 90}, "222": {"x": -0.11, "y": -0.49, "rotation": -103.14, "width": 71, "height": 90}, "333": {"x": -0.11, "y": -0.49, "rotation": -103.14, "width": 71, "height": 90}, "444": {"x": 0.23, "y": -0.82, "rotation": -103.14, "width": 71, "height": 90}}, "body": {"body": {"type": "mesh", "uvs": [0.09814, 0.39101, 0.13347, 0.49296, 0.17101, 0.51024, 0.23062, 0.51197, 0.28362, 0.69168, 0.36752, 0.94397, 0.46026, 0.99927, 0.67001, 1, 0.89081, 0.95607, 0.94601, 0.89732, 0.9416, 0.6744, 0.92393, 0.41002, 0.97693, 0.38928, 1, 0.3409, 0.99901, 0.23895, 0.93277, 0.22513, 0.57065, 0.27178, 0.45142, 0.11453, 0.4183, 0.05751, 0.3631, 0.01604, 0.28582, 0, 0.22621, 0.0074, 0, 0.14564, 0, 0.23722, 0.09152, 0.27697, 0.14672, 0.25969, 0.23946, 0.19575, 0.3079, 0.21994, 0.36752, 0.30289, 0.13789, 0.3409], "triangles": [24, 23, 25, 26, 25, 22, 25, 23, 22, 22, 21, 26, 28, 16, 3, 16, 4, 3, 28, 3, 29, 0, 3, 1, 29, 3, 0, 3, 2, 1, 16, 28, 17, 28, 27, 17, 17, 27, 18, 27, 26, 18, 26, 19, 18, 26, 20, 19, 26, 21, 20, 11, 10, 16, 16, 10, 4, 12, 11, 13, 11, 16, 15, 13, 15, 14, 15, 13, 11, 9, 8, 7, 7, 4, 10, 10, 9, 7, 5, 7, 6, 5, 4, 7], "vertices": [3, 1, 68.37, 45.44, 0.00117, 2, 43.92, 39.88, 0.02539, 3, 15.25, 33.12, 0.97343, 3, 1, 56.42, 43.27, 0.0139, 2, 31.78, 39.45, 0.10723, 3, 3.92, 37.49, 0.87887, 3, 1, 54.15, 40.07, 0.02085, 2, 29.07, 36.61, 0.13974, 3, 0.32, 35.94, 0.83941, 3, 1, 53.5, 34.74, 0.05226, 2, 27.66, 31.43, 0.25806, 3, -3.01, 31.73, 0.68967, 3, 1, 32.5, 31.75, 0.47549, 2, 6.45, 31.49, 0.39117, 3, -22.5, 40.12, 0.13334, 3, 1, 2.95, 26.7, 0.9921, 2, -23.52, 30.73, 0.00654, 3, -50.36, 51.19, 0.00136, 1, 1, -4.1, 18.93, 1, 1, 1, -5.79, 0.12, 1, 2, 1, -2.45, -20.11, 0.9797, 2, -35.58, -14.82, 0.0203, 2, 1, 3.86, -25.63, 0.94229, 2, -30.13, -21.19, 0.05771, 2, 1, 29.43, -27.42, 0.36769, 2, -5.08, -26.63, 0.63231, 2, 1, 59.86, -28.43, 9e-05, 2, 24.89, -32, 0.99991, 1, 2, 26.13, -37.18, 1, 1, 2, 31.07, -40.47, 1, 1, 2, 42.51, -43.05, 1, 1, 2, 45.41, -37.61, 1, 2, 2, 47.6, -4.65, 0.47598, 3, 1.15, -9.28, 0.52402, 1, 3, 22.08, -11.33, 1, 1, 3, 29.13, -12.8, 1, 2, 3, 35.91, -11.62, 0.98628, 4, -15.4, -1.48, 0.01372, 2, 3, 41.51, -7.1, 0.79588, 4, -11.43, -7.48, 0.20412, 2, 3, 44, -2.27, 0.51337, 4, -6.86, -10.42, 0.48663, 1, 4, 18.92, -12.08, 1, 1, 4, 25.92, -4.22, 1, 1, 4, 22.81, 4.68, 1, 2, 3, 24.84, 20.66, 0.0007, 4, 17.78, 6.49, 0.9993, 2, 3, 25.83, 9.58, 0.29035, 4, 6.66, 6.55, 0.70965, 2, 3, 19.95, 6.26, 0.88533, 4, 3.91, 12.73, 0.11467, 2, 2, 48.27, 13.97, 0.00431, 3, 9.08, 7.58, 0.99569, 2, 2, 48.72, 35.09, 0.01241, 3, 17.78, 26.82, 0.98759], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58], "width": 90, "height": 115}}, "BQ1sqzui": {"BQ1sqzui": {"x": 8.39, "y": 7.37, "rotation": 9.46, "width": 17, "height": 9}, "BQ2tiaozui": {"x": 8.88, "y": 7.46, "rotation": 9.46, "width": 28, "height": 15}, "BQpaozui": {"type": "mesh", "uvs": [0.15754, 0.02376, 0.31945, 0.11248, 0.51574, 0.12727, 0.71647, 0.12283, 0.83513, 0.07404, 0.92717, 0.08291, 0.98595, 0.17607, 0.98041, 0.3121, 0.90943, 0.40673, 0.81849, 0.51172, 0.71203, 0.51468, 0.68763, 0.66267, 0.62775, 0.778, 0.53459, 0.90664, 0.43368, 0.951, 0.33608, 0.94065, 0.23295, 0.84158, 0.17972, 0.72625, 0.17528, 0.57099, 0.1897, 0.47192, 0.1276, 0.41278, 0.06993, 0.31371, 0.03777, 0.2176, 0.03666, 0.09191, 0.10763, 0.01946], "triangles": [13, 14, 16, 14, 15, 16, 12, 13, 18, 18, 13, 16, 16, 17, 18, 19, 11, 12, 2, 19, 1, 18, 19, 12, 10, 11, 19, 2, 10, 19, 10, 3, 9, 10, 2, 3, 9, 3, 8, 19, 20, 1, 1, 20, 21, 1, 21, 0, 23, 24, 0, 3, 4, 8, 6, 7, 4, 7, 8, 4, 0, 21, 22, 6, 4, 5, 0, 22, 23], "vertices": [1, 13, -8.82, 9.22, 1, 1, 13, -3.36, 7.97, 1, 2, 13, 2.9, 8.65, 0.99997, 40, -10.33, -0.23, 3e-05, 2, 13, 9.21, 9.81, 0.99996, 40, -12.86, 5.67, 4e-05, 2, 13, 12.77, 11.59, 0.99998, 40, -15.38, 8.75, 2e-05, 2, 13, 15.71, 11.87, 0.99999, 40, -16.3, 11.56, 1e-05, 2, 13, 17.93, 9.97, 0.99999, 40, -14.94, 14.14, 1e-05, 2, 13, 18.29, 6.72, 0.99999, 40, -11.85, 15.21, 1e-05, 2, 13, 16.43, 4.11, 1, 40, -8.89, 13.97, 0, 2, 13, 13.97, 1.14, 1, 40, -5.45, 12.22, 0, 2, 13, 10.62, 0.51, 0.98718, 41, -1.82, 11.88, 0.01282, 3, 13, 10.43, -3.12, 0.82136, 40, -0.52, 9.72, 0.00125, 41, 1.48, 10.36, 0.17738, 3, 13, 9, -6.16, 0.51844, 40, 2.77, 8.99, 0.00984, 41, 3.77, 7.89, 0.47172, 2, 13, 6.57, -9.7, 0.15422, 41, 6.14, 4.32, 0.84578, 2, 13, 3.56, -11.28, 0.02485, 41, 6.49, 0.94, 0.97515, 2, 40, 9.91, 1.82, 0.0069, 41, 5.58, -2.06, 0.9931, 3, 13, -3.21, -9.75, 0.00409, 40, 8.96, -2.13, 0.23618, 41, 2.55, -4.77, 0.75972, 3, 13, -5.35, -7.3, 0.06331, 40, 7.04, -4.75, 0.54438, 41, -0.52, -5.84, 0.39231, 3, 13, -6.1, -3.64, 0.30542, 40, 3.64, -6.29, 0.6186, 41, -4.19, -5.18, 0.07599, 3, 13, -6.03, -1.22, 0.64171, 40, 1.27, -6.77, 0.35291, 41, -6.41, -4.22, 0.00537, 2, 13, -8.23, -0.15, 0.85798, 40, 0.7, -9.14, 0.14202, 2, 13, -10.44, 1.89, 0.94962, 40, -0.8, -11.75, 0.05038, 2, 13, -11.83, 4, 0.98274, 40, -2.55, -13.57, 0.01726, 2, 13, -12.36, 6.97, 0.99745, 40, -5.33, -14.75, 0.00255, 1, 13, -10.41, 9.06, 1], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48], "width": 32, "height": 24}, "BQzuiba": {"x": 2.39, "y": 2.68, "rotation": 9.46, "width": 35, "height": 22}}, "BQ2SQ1": {"BQ2SQ1": {"x": 4.93, "y": -1.35, "width": 35, "height": 22}}, "BQ2SQ2": {"BQ2SQ2": {"x": 10.2, "y": 0.29, "rotation": 175.31, "width": 50, "height": 30}}, "BQ2sqfh": {"BQ2sqfh": {"x": -0.87, "y": -0.66, "rotation": -103.14, "width": 32, "height": 29}}, "BQ2sqmeimao": {"BQ2sqmeimao": {"x": -32.4, "y": 6.9, "width": 52, "height": 24}}, "BQ3Q1": {"BQ3Q1": {"x": 28.66, "y": 1.68, "rotation": -47.02, "width": 54, "height": 63}}, "BQ3Q2": {"BQ3Q2": {"x": 36.1, "y": -16.1, "width": 35, "height": 22}}, "BQ3Q3": {"BQ3Q3": {"x": 21.65, "y": -4.63, "rotation": 175.31, "width": 80, "height": 48}}, "BQ3sqfh": {"BQ3sqfh": {"x": -61.4, "y": -2.6, "width": 32, "height": 29}}, "BQmeimao": {"BQ1sqmeimao": {"x": -0.83, "y": -26.72, "rotation": -10.12, "width": 53, "height": 44}, "BQmeimao": {"x": -2.51, "y": -2.04, "rotation": -10.12, "width": 45, "height": 22}, "BQtiaomeimao": {"x": 0.74, "y": -12.27, "rotation": -10.12, "width": 63, "height": 19}}, "BQzhengyan": {"BQ3shengqiyan": {"x": 8.37, "y": -2.11, "rotation": -5.88, "width": 42, "height": 20}, "BQbiyan": {"x": 7.32, "y": -2.5, "rotation": -5.88, "width": 30, "height": 9}, "BQtiao": {"x": 8.63, "y": 0.38, "rotation": -5.84, "width": 44, "height": 17}, "BQzhengyan": {"x": 5.81, "y": -3.75, "rotation": -5.88, "width": 28, "height": 23}}, "han1": {"han1": {"x": 6.54, "y": -0.33, "rotation": 114.9, "width": 15, "height": 23}}, "han2": {"han2": {"x": 4.96, "y": 1.26, "rotation": 155.9, "width": 19, "height": 13}}, "lhand": {"lhand": {"type": "mesh", "uvs": [0.92623, 0.18926, 0.62388, 0.35976, 0.42838, 0.56553, 0.32381, 0.83794, 0.28061, 0.96532, 0.12376, 0.97512, 0.05101, 0.93396, 0.00782, 0.86733, 0.04192, 0.66156, 0.23742, 0.3872, 0.4352, 0.18338, 0.76483, 0.01093, 0.92168, 0.02465, 0.96488, 0.15595], "triangles": [4, 5, 3, 3, 5, 6, 6, 7, 3, 7, 8, 3, 3, 8, 2, 8, 9, 2, 2, 9, 1, 9, 10, 1, 0, 11, 12, 0, 1, 11, 1, 10, 11, 0, 12, 13], "vertices": [1, 7, -4.51, 5.8, 1, 1, 7, 4.26, 3.65, 1, 2, 7, 11.96, 4.15, 0.31431, 8, 0.79, 3.8, 0.68569, 1, 8, 9.1, 4.21, 1, 1, 8, 12.94, 4.53, 1, 1, 8, 14.62, 0.98, 1, 1, 8, 14.16, -1.15, 1, 1, 8, 12.74, -2.85, 1, 1, 8, 6.87, -4.21, 1, 2, 7, 11.44, -2.87, 0.773, 8, -2.31, -2.52, 0.227, 1, 7, 3.75, -3.29, 1, 1, 7, -5.53, -0.68, 1, 1, 7, -7.92, 2.45, 1, 1, 7, -5.87, 5.84, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 25, "height": 29}}, "lleg": {"lleg": {"type": "mesh", "uvs": [0.81768, 0.01143, 0.57278, 0.16838, 0.39348, 0.38688, 0.34538, 0.49459, 0.38036, 0.74078, 0.11797, 0.7654, 0.04363, 0.8331, 0.03925, 0.9562, 0.21418, 0.99005, 0.70398, 0.95005, 0.70398, 0.74078, 0.66025, 0.52536, 0.8308, 0.30687, 0.96199, 0.13453, 0.97074, 0.03913], "triangles": [8, 5, 4, 6, 5, 8, 7, 6, 8, 3, 2, 11, 4, 3, 11, 4, 11, 10, 4, 10, 9, 8, 4, 9, 13, 0, 14, 1, 0, 13, 12, 1, 13, 2, 1, 12, 11, 2, 12], "vertices": [1, 16, -0.26, -2.34, 1, 1, 16, 5.92, -3.53, 1, 2, 16, 12.68, -2.73, 0.62535, 17, -1.55, -2.93, 0.37465, 2, 16, 15.56, -1.72, 0.0639, 17, 1.42, -3.62, 0.9361, 2, 17, 7.99, -2.43, 0.56309, 18, 1.14, -2.85, 0.43691, 1, 18, 6.17, -2.59, 1, 1, 18, 7.72, -0.89, 1, 1, 18, 8.07, 2.42, 1, 1, 18, 4.84, 3.6, 1, 2, 17, 13.15, 4.14, 0.99968, 18, -4.53, 3.27, 0.00032, 1, 17, 7.51, 3.7, 1, 2, 16, 12.65, 3.57, 0.07357, 17, 1.78, 2.41, 0.92643, 2, 16, 5.98, 2.64, 0.99657, 17, -4.36, 5.18, 0.00343, 1, 16, 0.76, 1.85, 1, 1, 16, -1.4, 0.44, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 19, "height": 27}}, "paolleg": {"paolleg": {"type": "mesh", "uvs": [0.91272, 0.24624, 0.87156, 0.26664, 0.79248, 0.39216, 0.65479, 0.59667, 0.58268, 0.60446, 0.43645, 0.30067, 0.33229, 0.07087, 0.26619, 0.07477, 0.06789, 0.35519, 0.01781, 0.46425, 0.03784, 0.64341, 0.15202, 0.68236, 0.29624, 0.59278, 0.44647, 0.85763, 0.57066, 0.9589, 0.7269, 0.90826, 0.87913, 0.74468, 0.9913, 0.45646, 0.97928, 0.23445], "triangles": [12, 11, 8, 11, 10, 8, 10, 9, 8, 8, 7, 12, 5, 7, 6, 14, 13, 4, 4, 13, 5, 13, 12, 5, 5, 12, 7, 15, 14, 3, 14, 4, 3, 15, 3, 16, 17, 0, 18, 0, 17, 1, 3, 2, 16, 16, 2, 17, 1, 17, 2], "vertices": [1, 31, -1.41, -2.11, 1, 1, 31, -0.02, -2.65, 1, 1, 31, 3.54, -2.42, 1, 2, 31, 9.6, -2.23, 0.91053, 32, -0.92, -4.85, 0.08947, 2, 31, 11.74, -3.58, 0.33161, 32, 0.8, -3, 0.66839, 2, 32, 8.28, -3.37, 0.9117, 33, -3.96, 2.33, 0.0883, 2, 32, 13.77, -3.8, 0.02413, 33, -2.69, -3.04, 0.97587, 1, 33, -0.62, -4.07, 1, 1, 33, 7.88, -2.89, 1, 1, 33, 10.35, -1.99, 1, 1, 33, 11.26, 1.19, 1, 2, 32, 10.65, 8.5, 0.03281, 33, 8.06, 3.69, 0.96719, 2, 32, 8.15, 3.83, 0.717, 33, 2.85, 4.65, 0.283, 1, 32, 1.05, 3.59, 1, 2, 31, 15.79, 1.36, 0.41781, 32, -3.34, 1.88, 0.58219, 1, 31, 10.81, 3.8, 1, 1, 31, 4.77, 4.5, 1, 1, 31, -1.44, 2.57, 1, 1, 31, -3.43, -0.93, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 35, "height": 18}}, "paorleg": {"paorleg": {"type": "mesh", "uvs": [0.15604, 0.01158, 0.07105, 0.02504, 0.04519, 0.10401, 0.20962, 0.21067, 0.37035, 0.37668, 0.37279, 0.49742, 0.23112, 0.6598, 0.06679, 0.73687, 0.09513, 0.8277, 0.32745, 0.92403, 0.75245, 0.99008, 0.92811, 0.94329, 0.95644, 0.82219, 0.64478, 0.74788, 0.54278, 0.73962, 0.69578, 0.55522, 0.71845, 0.39558, 0.52578, 0.14787, 0.25379, 0.00475], "triangles": [12, 10, 13, 11, 10, 12, 9, 14, 13, 10, 9, 13, 15, 5, 16, 15, 6, 5, 14, 6, 15, 8, 7, 6, 8, 6, 14, 9, 8, 14, 18, 2, 0, 3, 18, 17, 2, 1, 0, 18, 3, 2, 4, 3, 17, 4, 17, 16, 5, 4, 16], "vertices": [1, 29, -6.35, -0.68, 1, 1, 29, -6.43, -2.2, 1, 1, 29, -4, -3.59, 1, 1, 29, 0.48, -2.31, 1, 2, 29, 6.88, -1.83, 0.9937, 30, -3.35, -4.17, 0.0063, 2, 29, 10.85, -3.3, 0.27468, 30, 0.64, -2.76, 0.72532, 1, 30, 6.79, -3.19, 1, 1, 30, 10.25, -4.96, 1, 2, 30, 13.1, -3.47, 0.95895, 34, -4.05, -3.48, 0.04105, 2, 30, 15.01, 1.36, 0.18716, 34, 1.02, -4.58, 0.81284, 1, 34, 8.48, -3.21, 1, 1, 34, 10.34, -0.36, 1, 1, 34, 8.76, 3.61, 1, 2, 30, 7.42, 4.46, 0.085, 34, 2.86, 3.4, 0.915, 2, 30, 7.71, 2.73, 0.44484, 34, 1.2, 2.83, 0.55516, 2, 29, 14.69, 1.11, 0.04094, 30, 0.76, 3.09, 0.95906, 2, 29, 9.61, 3.46, 0.99408, 30, -4.64, 1.64, 0.00592, 1, 29, 0.34, 3.49, 1, 1, 29, -5.99, 0.96, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 17, "height": 35}}, "rhand": {"rhand": {"type": "mesh", "uvs": [0.15851, 0, 0.59782, 0.16604, 0.86629, 0.45786, 0.97612, 0.70988, 0.97612, 0.94422, 0.74426, 1, 0.65884, 0.83811, 0.56121, 0.58608, 0.33749, 0.35617, 0.05274, 0.2191, 0, 0.13067, 0.01207, 0], "triangles": [5, 6, 4, 6, 3, 4, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 9, 0, 8, 8, 0, 1, 9, 10, 0, 0, 10, 11], "vertices": [1, 9, -10.47, -0.41, 1, 1, 9, 0.24, 4.13, 1, 2, 9, 9.72, 3.65, 0.79462, 10, -1.99, 3.46, 0.20538, 2, 9, 15.64, 1.2, 0.00015, 10, 4.4, 4.03, 0.99985, 1, 10, 9.45, 2.15, 1, 1, 10, 8.64, -3.73, 1, 2, 9, 11.74, -6.31, 0.01732, 10, 4.41, -4.44, 0.98268, 2, 9, 6.04, -3.65, 0.80911, 10, -1.88, -4.71, 0.19089, 1, 9, -1.65, -3.48, 1, 1, 9, -9.05, -5.92, 1, 1, 9, -11.39, -5.3, 1, 1, 9, -13.18, -2.87, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 25, "height": 23}}, "rleg": {"rleg": {"type": "mesh", "uvs": [0.0453, 0.10683, 0.08684, 0.21762, 0.25302, 0.36841, 0.33989, 0.48843, 0.37766, 0.76848, 0.41165, 0.94389, 0.52118, 0.98698, 0.87998, 0.99313, 0.97062, 0.8608, 0.94041, 0.75309, 0.63826, 0.74694, 0.63448, 0.51921, 0.60049, 0.40534, 0.44942, 0.193, 0.18126, 0.02682, 0.0604, 0.04221], "triangles": [6, 10, 7, 8, 10, 9, 8, 7, 10, 6, 5, 10, 5, 4, 10, 4, 11, 10, 4, 3, 11, 3, 12, 11, 3, 2, 12, 2, 13, 12, 2, 1, 13, 1, 0, 14, 1, 14, 13, 14, 0, 15], "vertices": [1, 14, -1.38, -1.05, 1, 1, 14, 1.44, -2.39, 1, 2, 14, 6.91, -2.43, 0.98987, 15, -4.04, -4.74, 0.01013, 2, 14, 10.6, -3.2, 0.51484, 15, -0.58, -3.26, 0.48516, 1, 15, 7.02, -3.41, 1, 2, 15, 11.81, -3.27, 0.99999, 19, -3.6, -2.62, 1e-05, 2, 15, 13.28, -1.03, 0.79041, 19, -1.1, -3.59, 0.20959, 1, 19, 6.78, -3.12, 1, 1, 19, 8.48, 0.6, 1, 1, 19, 7.58, 3.45, 1, 2, 15, 7.18, 2.35, 0.5738, 19, 0.94, 3.08, 0.4262, 2, 14, 15.58, 1.03, 0.06623, 15, 1.07, 3.06, 0.93377, 2, 14, 12.8, 2.55, 0.70622, 15, -2.07, 2.72, 0.29378, 1, 14, 6.32, 3.96, 1, 1, 14, -0.97, 2.62, 1, 1, 14, -2.45, 0.37, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 22, "height": 27}}, "zz1": {"zz1": {"x": -0.73, "y": 0.1, "rotation": -94.89, "width": 15, "height": 15}}, "zz2": {"zz2": {"x": 0.35, "y": -0.62, "rotation": -94.89, "width": 14, "height": 14}}, "zz3": {"zz3": {"x": 0.09, "y": 0.11, "rotation": -94.89, "width": 15, "height": 15}}, "zz4": {"zz4": {"x": -0.75, "y": 0.15, "rotation": -94.89, "width": 14, "height": 15}}, "zz5": {"zz5": {"x": -0.51, "y": 0.26, "rotation": -94.89, "width": 15, "height": 15}}}}], "animations": {"angry1": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ1sqzui": {"attachment": [{"name": "BQ1sqzui"}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQ1sqmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.5333, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.6333, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffd7"}, {"time": 0.5667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.4667, "color": "fffffffe"}, {"time": 0.5333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.3333, "angle": -1.51}, {"time": 0.7333}], "translate": [{}, {"time": 0.3333, "y": -4.04}, {"time": 0.7333}]}, "bone3": {"rotate": [{}, {"time": 0.1667, "angle": -2.38}, {"time": 0.3333}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.5333, "angle": 23.34}, {"time": 0.7333}], "translate": [{"time": 0.3}, {"time": 0.5333, "x": 20.04, "y": 2.65}, {"time": 0.7333}]}, "bone6": {"translate": [{"time": 0.3667}, {"time": 0.5667, "x": 17.52, "y": 10.64}, {"time": 0.7333}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 8.8}, {"time": 0.7333}]}, "bone8": {"rotate": [{}, {"time": 0.3333, "angle": 11.59}, {"time": 0.7333}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -6.43}, {"time": 0.7333}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "angle": -13.06}, {"time": 0.7333}]}, "bone16": {"translate": [{}, {"time": 0.3333, "x": 0.9}, {"time": 0.7333}], "scale": [{"time": 0.5333}, {"time": 0.5667, "y": 0.9}, {"time": 0.6}]}, "bone11": {"rotate": [{"angle": 0.37}]}, "bone12": {"rotate": [{"angle": -8.74}]}, "bone13": {"rotate": [{"angle": 0.54}]}, "bone17": {"rotate": [{"angle": 4.38}]}, "bone18": {"rotate": [{"angle": -4.75}]}, "bone19": {"rotate": [{"angle": 7.41}]}, "bone21": {"translate": [{"x": -4.54, "y": 0.39}, {"time": 0.5333, "x": -6.16, "y": -1.22}, {"time": 0.7333, "x": -4.54, "y": 0.39}]}, "bone22": {"translate": [{}, {"time": 0.3667, "x": -0.07, "y": -1.25}, {"time": 0.7333}]}, "bone24": {"translate": [{}, {"time": 0.3, "x": -0.19, "y": -1.19}, {"time": 0.7333}]}, "bone20": {"translate": [{}, {"time": 0.4667, "x": -0.64, "y": -1.75}, {"time": 0.7333}]}, "bone23": {"translate": [{}, {"time": 0.3333, "x": -0.04, "y": -0.57}, {"time": 0.7333}]}}}, "angry2": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ1sqzui": {"attachment": [{"name": "BQ2<PERSON><PERSON><PERSON>"}]}, "BQ2SQ1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": "BQ2SQ1"}]}, "BQ2SQ2": {"color": [{"color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"name": "BQ2SQ2"}]}, "BQ2sqfh": {"color": [{"color": "ffffff40"}, {"time": 0.3, "color": "fffffff5"}, {"time": 0.5667, "color": "ffffff84"}], "attachment": [{"name": "BQ2sqfh"}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQ1sqmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "BQ3shengqiyan"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffd7"}, {"time": 0.5667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.4667, "color": "fffffffe"}, {"time": 0.5333, "color": "ffffff00"}]}}, "bones": {"bone16": {"translate": [{}, {"time": 0.3333, "x": 0.9}, {"time": 0.7333}], "scale": [{"time": 0.5333}, {"time": 0.5667, "y": 0.9}, {"time": 0.6}]}, "bone": {"rotate": [{}, {"time": 0.3333, "angle": -1.51}, {"time": 0.7333}], "translate": [{}, {"time": 0.3333, "y": -4.04}, {"time": 0.7333}]}, "bone3": {"rotate": [{}, {"time": 0.1667, "angle": -2.38}, {"time": 0.3333}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.5333, "angle": 23.34}, {"time": 0.7333}], "translate": [{"time": 0.3}, {"time": 0.5333, "x": 20.04, "y": 2.65}, {"time": 0.7333}]}, "bone6": {"translate": [{"time": 0.3667}, {"time": 0.5667, "x": 17.52, "y": 10.64}, {"time": 0.7333}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 8.8}, {"time": 0.7333}]}, "bone8": {"rotate": [{}, {"time": 0.3333, "angle": 11.59}, {"time": 0.7333}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -6.43}, {"time": 0.7333}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "angle": -13.06}, {"time": 0.7333}]}, "bone11": {"rotate": [{"angle": 0.37}]}, "bone12": {"rotate": [{"angle": -8.74}]}, "bone13": {"rotate": [{"angle": 0.54}]}, "bone17": {"rotate": [{"angle": 4.38}]}, "bone18": {"rotate": [{"angle": -4.75}]}, "bone19": {"rotate": [{"angle": 7.41}]}, "bone21": {"translate": [{"x": -4.54, "y": 0.39}, {"time": 0.5333, "x": -6.16, "y": -1.22}, {"time": 0.7333, "x": -4.54, "y": 0.39}]}, "bone22": {"translate": [{}, {"time": 0.3667, "x": -0.07, "y": -1.25}, {"time": 0.7333}]}, "bone24": {"translate": [{}, {"time": 0.3, "x": -0.19, "y": -1.19}, {"time": 0.7333}]}, "bone20": {"translate": [{}, {"time": 0.4667, "x": -0.64, "y": -1.75}, {"time": 0.7333}]}, "bone23": {"translate": [{}, {"time": 0.3333, "x": -0.04, "y": -0.57}, {"time": 0.7333}]}, "bone32": {"rotate": [{}, {"time": 0.3, "angle": -12.27}, {"time": 0.5667, "angle": 3.26}], "scale": [{}, {"time": 0.3, "x": 1.1, "y": 1.1}, {"time": 0.5667}]}, "bone33": {"rotate": [{"angle": -24.02}], "translate": [{"x": 10.57, "y": 15.98}, {"time": 0.2333, "x": -16.84, "y": -16.01}]}, "bone34": {"rotate": [{"angle": 34.77}], "translate": [{"x": 7.23, "y": -24.9}, {"time": 0.3, "x": -6.62, "y": 25.77}]}}}, "angry3": {"slots": {"111": {"color": [{"color": "ffffffff"}, {"time": 0.4333, "color": "ff9257ff"}, {"time": 0.5333, "color": "ff925792"}, {"time": 0.7333, "color": "ffffffff"}], "attachment": [{"time": 0.1333, "name": "222"}, {"time": 0.2333, "name": "333"}, {"time": 0.3333, "name": "444"}, {"time": 0.4, "name": "111"}, {"time": 0.5333, "name": "222"}, {"time": 0.6333, "name": "333"}, {"time": 0.7333, "name": "444"}]}, "BQ1sqzui": {"attachment": [{"name": "BQ2<PERSON><PERSON><PERSON>"}]}, "BQ2SQ1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": "BQ2SQ1"}]}, "BQ2SQ2": {"color": [{"color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}]}, "BQ2sqfh": {"color": [{"color": "ffffff40"}, {"time": 0.3, "color": "fffffff5"}, {"time": 0.5667, "color": "ffffff84"}], "attachment": [{"name": "BQ2sqfh"}]}, "BQ3Q1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.0333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}, {"time": 0.2333, "color": "ffffff00"}], "attachment": [{"name": "BQ3Q1"}]}, "BQ3Q3": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "fffffffe"}, {"time": 0.2667, "color": "ffffffe4"}, {"time": 0.3, "color": "ffffff00"}]}, "BQmeimao": {"attachment": [{"name": "BQ1sqmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "BQ3shengqiyan"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffd7"}, {"time": 0.5667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.4667, "color": "fffffffe"}, {"time": 0.5333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.3333, "angle": -1.51}, {"time": 0.7333}], "translate": [{}, {"time": 0.3333, "y": -4.04}, {"time": 0.7333}]}, "bone3": {"rotate": [{}, {"time": 0.1667, "angle": -2.38}, {"time": 0.3333}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.5333, "angle": 23.34}, {"time": 0.7333}], "translate": [{"time": 0.3}, {"time": 0.5333, "x": 20.04, "y": 2.65}, {"time": 0.7333}]}, "bone6": {"translate": [{"time": 0.3667}, {"time": 0.5667, "x": 17.52, "y": 10.64}, {"time": 0.7333}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 8.8}, {"time": 0.7333}]}, "bone8": {"rotate": [{}, {"time": 0.3333, "angle": 11.59}, {"time": 0.7333}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -6.43}, {"time": 0.7333}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "angle": -13.06}, {"time": 0.7333}]}, "bone16": {"translate": [{}, {"time": 0.3333, "x": 0.9}, {"time": 0.7333}], "scale": [{"time": 0.5333}, {"time": 0.5667, "y": 0.9}, {"time": 0.6}]}, "bone11": {"rotate": [{"angle": 0.37}]}, "bone12": {"rotate": [{"angle": -8.74}]}, "bone13": {"rotate": [{"angle": 0.54}]}, "bone17": {"rotate": [{"angle": 4.38}]}, "bone18": {"rotate": [{"angle": -4.75}]}, "bone19": {"rotate": [{"angle": 7.41}]}, "bone21": {"translate": [{"x": -4.54, "y": 0.39}, {"time": 0.5333, "x": -6.16, "y": -1.22}, {"time": 0.7333, "x": -4.54, "y": 0.39}]}, "bone22": {"translate": [{}, {"time": 0.3667, "x": -0.07, "y": -1.25}, {"time": 0.7333}]}, "bone24": {"translate": [{}, {"time": 0.3, "x": -0.19, "y": -1.19}, {"time": 0.7333}]}, "bone20": {"translate": [{}, {"time": 0.4667, "x": -0.64, "y": -1.75}, {"time": 0.7333}]}, "bone23": {"translate": [{}, {"time": 0.3333, "x": -0.04, "y": -0.57}, {"time": 0.7333}]}, "bone32": {"rotate": [{}, {"time": 0.3, "angle": -12.27}, {"time": 0.5667, "angle": 3.26}], "scale": [{}, {"time": 0.3, "x": 1.1, "y": 1.1}, {"time": 0.5667}]}, "bone33": {"rotate": [{"angle": -24.02}], "translate": [{"x": 10.57, "y": 15.98}, {"time": 0.2333, "x": -16.84, "y": -16.01}]}, "bone34": {"rotate": [{"angle": 34.77}], "translate": [{"x": 7.23, "y": -24.9}, {"time": 0.3, "x": -6.62, "y": 25.77}]}, "bone35": {"translate": [{}, {"time": 0.2333, "x": 15.7, "y": -34.39}]}}}, "die": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ1sqzui": {"attachment": [{"name": "BQ1sqzui"}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQmeimao"}]}, "han1": {"color": [{"color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00"}]}}, "bones": {"bone": {"translate": [{"time": 1.0667}, {"time": 1.3333, "y": 162.78}, {"time": 1.5, "y": 224.71}, {"time": 1.6667, "y": 94.75}, {"time": 1.8333, "y": -79.33}, {"time": 1.9667, "y": -351.81}]}, "bone6": {"translate": [{"x": 17.52, "y": 10.64}]}, "bone7": {"rotate": [{"angle": -80.41}]}, "bone8": {"rotate": [{"angle": -25.35}]}, "bone9": {"rotate": [{"angle": 95.76}]}, "bone10": {"rotate": [{"angle": 31.72}]}, "bone16": {"translate": [{"x": 0.15, "y": 0.64}]}, "bone11": {"rotate": [{"angle": 74.85}], "translate": [{"x": 0.97, "y": -9.76}]}, "bone13": {"rotate": [{"angle": -77.1}], "translate": [{"x": 0.79, "y": 11.86}]}, "bone12": {"rotate": [{"angle": 47.41}]}, "bone17": {"rotate": [{"angle": -24.55}]}, "bone18": {"rotate": [{"angle": -7.91}]}}, "ik": {"1": [{"mix": 0}], "2": [{"mix": 0}], "3": [{"mix": 0, "bendPositive": false}], "4": [{"mix": 0}]}}, "jump": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ1sqzui": {"attachment": [{"time": 0.2, "name": "BQ2<PERSON><PERSON><PERSON>"}, {"time": 0.3333, "name": "BQzuiba"}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQmeimao"}, {"time": 0.0667, "name": "BQtiaomeimao"}, {"time": 0.3333, "name": "BQmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.0667, "name": "BQtiao"}, {"time": 0.3333, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.6, "color": "ffffffd7"}, {"time": 0.6667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.5667, "color": "fffffffe"}, {"time": 0.6333, "color": "ffffff00"}]}}, "bones": {"bone16": {"translate": [{"x": 0.15, "y": 0.64}, {"time": 0.0333}, {"time": 0.9, "x": 0.15, "y": 0.64}]}, "4": {"translate": [{"time": 0.0333}, {"time": 0.1333, "x": -1.17, "y": 54.47}, {"time": 0.3333, "x": 12.17, "y": 215.65}, {"time": 0.4667, "x": 10.54, "y": 168.45}, {"time": 0.5667, "x": 10.52, "y": 131.97}, {"time": 0.7, "x": 9.93, "y": 84.79}, {"time": 0.8, "x": -5.09, "y": 19.97}, {"time": 0.9}]}, "3": {"translate": [{"time": 0.0333}, {"time": 0.1333, "x": 2.14, "y": 54.42}, {"time": 0.3333, "x": 21.17, "y": 205.28}, {"time": 0.4667, "x": 21.75, "y": 157.49}, {"time": 0.5667, "x": 18.61, "y": 123.62}, {"time": 0.7, "x": 15.51, "y": 79.83}, {"time": 0.8, "x": -4, "y": 19.49}, {"time": 0.9}]}, "bone12": {"rotate": [{"angle": -8.74, "curve": "stepped"}, {"time": 0.0333, "angle": -8.74}, {"time": 0.3333, "angle": 52.2}, {"time": 0.9, "angle": -8.74}]}, "bone11": {"rotate": [{"angle": 0.37, "curve": "stepped"}, {"time": 0.0333, "angle": 0.37}, {"time": 0.3333, "angle": 56.54}, {"time": 0.9, "angle": 0.37}], "translate": [{"time": 0.0333}, {"time": 0.3333, "x": -4.14, "y": -5.96}, {"time": 0.9}]}, "bone13": {"rotate": [{"angle": -14.42, "curve": "stepped"}, {"time": 0.0333, "angle": -14.42}, {"time": 0.3333, "angle": -62.25}, {"time": 0.9, "angle": -14.42}], "translate": [{"time": 0.0333}, {"time": 0.1333, "x": -2.28, "y": 11.42}, {"time": 0.3333, "x": -3.05, "y": 13.47}, {"time": 0.4667, "x": -1.93, "y": 11.97}, {"time": 0.5667, "x": -1.65, "y": 13.05}, {"time": 0.9}]}, "bone17": {"rotate": [{"angle": 22.09, "curve": "stepped"}, {"time": 0.0333, "angle": 22.09}, {"time": 0.3333, "angle": -54.12}, {"time": 0.9, "angle": 22.09}]}, "2": {"translate": [{"time": 0.0333}, {"time": 0.1333, "x": -4.02, "y": 52.98}, {"time": 0.3333, "x": -18.41, "y": 209.72}, {"time": 0.4667, "x": -19.26, "y": 164.54}, {"time": 0.5667, "x": -19.31, "y": 126.34}, {"time": 0.7, "x": -16.27, "y": 74.49}, {"time": 0.8, "x": 0.05, "y": 19.4}, {"time": 0.9}]}, "bone18": {"rotate": [{"angle": -7.91}]}, "bone": {"translate": [{}, {"time": 0.0333, "y": -7.45}, {"time": 0.9}]}, "bone23": {"translate": [{"time": 0.0333}, {"time": 0.4, "x": 17.64, "y": -3.98}, {"time": 0.9}]}, "bone24": {"translate": [{"time": 0.0333}, {"time": 0.3667, "x": 24.56, "y": -1.76}, {"time": 0.9}]}, "bone22": {"translate": [{"time": 0.0333}, {"time": 0.3333, "x": 6.99, "y": -0.06}, {"time": 0.9}]}, "bone20": {"translate": [{"time": 0.0333}, {"time": 0.3333, "x": 10.2, "y": 5.18}, {"time": 0.9}]}, "bone21": {"translate": [{"time": 0.0333}, {"time": 0.4, "x": 14.05, "y": 3.92}, {"time": 0.9}]}, "bone9": {"rotate": [{"time": 0.0333}, {"time": 0.3333, "angle": 95.76, "curve": "stepped"}, {"time": 0.6667, "angle": 95.76}, {"time": 0.9}], "translate": [{"time": 0.6667}, {"time": 0.8333, "x": -1.09, "y": -0.56}, {"time": 0.9}]}, "bone10": {"rotate": [{"time": 0.0333}, {"time": 0.3333, "angle": 31.72, "curve": "stepped"}, {"time": 0.6667, "angle": 31.72}, {"time": 0.9}]}, "bone7": {"rotate": [{"time": 0.0333}, {"time": 0.3333, "angle": -80.41, "curve": "stepped"}, {"time": 0.6667, "angle": -80.41}, {"time": 0.9}], "translate": [{"time": 0.0333}, {"time": 0.8333, "x": -1.09, "y": -0.56}, {"time": 0.9}]}, "bone8": {"rotate": [{"time": 0.0333}, {"time": 0.3333, "angle": -30.05}, {"time": 0.6667, "angle": -11.18}, {"time": 0.9}]}, "bone14": {"translate": [{"time": 0.0333}, {"time": 0.2, "x": 17.94}, {"time": 0.6333, "x": 9.08}, {"time": 0.9}]}, "bone6": {"translate": [{"time": 0.4667}, {"time": 0.6667, "x": 17.52, "y": 10.64}, {"time": 0.7667}]}, "bone5": {"rotate": [{"time": 0.4333}, {"time": 0.6333, "angle": 23.34}, {"time": 0.7667}], "translate": [{"time": 0.4}, {"time": 0.6333, "x": 20.04, "y": 2.65}, {"time": 0.7667}]}, "1": {"translate": [{"time": 0.0333}, {"time": 0.1333, "x": -6.82, "y": 54.78}, {"time": 0.3333, "x": -25.39, "y": 201.86}, {"time": 0.4667, "x": -26.65, "y": 158.2}, {"time": 0.5667, "x": -27, "y": 122.93}, {"time": 0.7, "x": -21.15, "y": 70.62}, {"time": 0.8, "x": -0.09, "y": 19.6}, {"time": 0.9}]}}, "ik": {"1": [{"mix": 0}], "2": [{"mix": 0}], "3": [{"mix": 0, "bendPositive": false}], "4": [{"mix": 0}]}}, "run": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ1sqzui": {"attachment": [{"name": "B<PERSON><PERSON><PERSON><PERSON>"}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffd7"}, {"time": 0.5667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.4667, "color": "fffffffe"}, {"time": 0.5333, "color": "ffffff00"}]}, "lleg": {"attachment": [{"name": null}]}, "paolleg": {"attachment": [{"name": "paolleg"}]}, "paorleg": {"attachment": [{"name": "pao<PERSON><PERSON>"}]}, "rleg": {"attachment": [{"name": null}]}}, "bones": {"bone27": {"rotate": [{}, {"time": 0.1333, "angle": 88.27}, {"time": 0.2667, "angle": 126.88}, {"time": 0.3667, "angle": 84.46}, {"time": 0.5, "angle": 10.55}, {"time": 0.6667}], "translate": [{"x": 0.03, "y": -0.21}, {"time": 0.5, "x": 2.13, "y": 6.85}, {"time": 0.6667, "x": 0.03, "y": -0.21}]}, "bone25": {"rotate": [{}, {"time": 0.1333, "angle": -40.52}, {"time": 0.2667, "angle": -56.27}, {"time": 0.3667, "angle": -33.23}, {"time": 0.5, "angle": 54.25}, {"time": 0.6667}], "translate": [{}, {"time": 0.1333, "x": -4.62, "y": 17.77}, {"time": 0.2667, "x": -0.81, "y": 15.76}, {"time": 0.5, "x": -4.05, "y": 1.54}, {"time": 0.6667}]}, "bone26": {"rotate": [{"angle": -25.31}, {"time": 0.1333, "angle": -47.75}, {"time": 0.3667, "angle": -67.14}, {"time": 0.5, "angle": -39.3}, {"time": 0.6667, "angle": -25.31}]}, "bone28": {"rotate": [{"angle": -14.49}, {"time": 0.1333, "angle": -39.48}, {"time": 0.2667, "angle": 22.21}, {"time": 0.3667, "angle": -29.5}, {"time": 0.5, "angle": -7.84}, {"time": 0.6667, "angle": -14.49}]}, "bone": {"rotate": [{"angle": -12.79}, {"time": 0.2667, "angle": 0.34}, {"time": 0.3667, "angle": -9.84}, {"time": 0.6667, "angle": -12.79}], "translate": [{}, {"time": 0.1333, "y": 7.42}, {"time": 0.2667, "x": 4.19, "y": 14.02}, {"time": 0.3667, "x": 2.09, "y": 1.01}, {"time": 0.5, "y": 11.82}, {"time": 0.6667}]}, "bone29": {"rotate": [{"angle": 31.32}, {"time": 0.1333, "angle": 37}, {"time": 0.2667, "angle": 25.67}, {"time": 0.3667, "angle": 59.37}, {"time": 0.5}, {"time": 0.6667, "angle": 31.32}]}, "bone30": {"rotate": [{"angle": 41.22}, {"time": 0.1333, "angle": -37.32}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5}, {"time": 0.6667, "angle": 41.22}]}, "bone8": {"rotate": [{"angle": 19.14}]}, "bone10": {"rotate": [{"angle": -25.08}]}, "bone9": {"rotate": [{"angle": 27.17}]}, "bone7": {"rotate": [{"angle": -28.51}]}, "bone6": {"translate": [{"time": 0.3667}, {"time": 0.5667, "x": 17.52, "y": 10.64}, {"time": 0.6667}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.5333, "angle": 23.34}, {"time": 0.6667}], "translate": [{"time": 0.3}, {"time": 0.5333, "x": 20.04, "y": 2.65}, {"time": 0.6667}]}, "bone24": {"translate": [{}, {"time": 0.0667, "x": 5.5, "y": -0.84}, {"time": 0.2, "x": 7.93, "y": -2.52}, {"time": 0.6667}]}, "bone23": {"translate": [{}, {"time": 0.3333, "x": 6.27, "y": -4.4}, {"time": 0.6667}]}, "bone22": {"translate": [{}, {"time": 0.2667, "x": -0.9, "y": -5.92}, {"time": 0.6667}]}, "bone20": {"translate": [{}, {"time": 0.2333, "x": 0.46, "y": -7.05}, {"time": 0.4333, "x": 0.25, "y": 3.99}, {"time": 0.6667}]}, "bone21": {"translate": [{}, {"time": 0.2667, "x": -4.58, "y": -7.63}, {"time": 0.6667}]}, "bone3": {"rotate": [{}, {"time": 0.3, "angle": 3.39}, {"time": 0.3667, "angle": -7.11}, {"time": 0.4667, "angle": 2.29}, {"time": 0.6667}]}, "bone36": {"rotate": [{}, {"time": 0.2333, "angle": 54.35}, {"time": 0.4667, "angle": -51.95}, {"time": 0.6667}]}, "bone37": {"rotate": [{}, {"time": 0.2333, "angle": -37.18}, {"time": 0.4667, "angle": -22.27}, {"time": 0.6667}]}}}, "standBy": {"slots": {"111": {"attachment": [{"name": null}]}, "BQ3Q3": {"attachment": [{"name": null}]}, "BQmeimao": {"attachment": [{"name": "BQmeimao"}]}, "BQzhengyan": {"attachment": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.5333, "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"time": 0.6333, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, "han1": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffd7"}, {"time": 0.5667, "color": "ffffff00"}]}, "han2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "fffffffe", "curve": "stepped"}, {"time": 0.4667, "color": "fffffffe"}, {"time": 0.5333, "color": "ffffff00"}]}}, "bones": {"bone": {"rotate": [{}, {"time": 0.3333, "angle": -1.51}, {"time": 0.7333}], "translate": [{}, {"time": 0.3333, "y": -4.04}, {"time": 0.7333}]}, "bone3": {"rotate": [{}, {"time": 0.1667, "angle": -2.38}, {"time": 0.3333}]}, "bone5": {"rotate": [{"time": 0.3333}, {"time": 0.5333, "angle": 23.34}, {"time": 0.7333}], "translate": [{"time": 0.3}, {"time": 0.5333, "x": 20.04, "y": 2.65}, {"time": 0.7333}]}, "bone6": {"translate": [{"time": 0.3667}, {"time": 0.5667, "x": 17.52, "y": 10.64}, {"time": 0.7333}]}, "bone7": {"rotate": [{}, {"time": 0.3333, "angle": 8.8}, {"time": 0.7333}]}, "bone8": {"rotate": [{}, {"time": 0.3333, "angle": 11.59}, {"time": 0.7333}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -6.43}, {"time": 0.7333}]}, "bone10": {"rotate": [{}, {"time": 0.3333, "angle": -13.06}, {"time": 0.7333}]}, "bone14": {"translate": [{}, {"time": 0.3333, "x": -3.17}, {"time": 0.7333}]}, "bone16": {"translate": [{}, {"time": 0.3333, "x": 0.9}, {"time": 0.7333}], "scale": [{"time": 0.5333}, {"time": 0.5667, "y": 0.9}, {"time": 0.6}]}, "bone11": {"rotate": [{"angle": 0.37}]}, "bone12": {"rotate": [{"angle": -8.74}]}, "bone13": {"rotate": [{"angle": 0.54}]}, "bone17": {"rotate": [{"angle": 4.38}]}, "bone18": {"rotate": [{"angle": -4.75}]}, "bone19": {"rotate": [{"angle": 7.41}]}, "bone21": {"translate": [{"x": -4.54, "y": 0.39}, {"time": 0.5333, "x": -6.16, "y": -1.22}, {"time": 0.7333, "x": -4.54, "y": 0.39}]}, "bone22": {"translate": [{}, {"time": 0.3667, "x": -0.07, "y": -1.25}, {"time": 0.7333}]}, "bone24": {"translate": [{}, {"time": 0.3, "x": -0.19, "y": -1.19}, {"time": 0.7333}]}, "bone20": {"translate": [{}, {"time": 0.4667, "x": -0.64, "y": -1.75}, {"time": 0.7333}]}, "bone23": {"translate": [{}, {"time": 0.3333, "x": -0.04, "y": -0.57}, {"time": 0.7333}]}}}}}