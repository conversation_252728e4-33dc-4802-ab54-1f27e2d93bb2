
import { _decorator, Contact2DType, Collider2D , tween, Vec3, Node, UITransform, EventTouch} from 'cc';
import {Constants} from "../Constants";
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";

const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 *
 */
 
@ccclass('Level6')
export class Level6 extends LevelDialogBase {

    @property(Node)
    BridgeNode:Node = null
    @property(Node)
    LockNode:Node = null
    @property(Node)
    BreakNode:Node = null

    touchBridgeCnt:number = 0
    isTouchLock:boolean = false

    start(){
        super.start()
        //鱼
        this.BridgeNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBridgeContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        //每关必须对应上
        super.superShow()

        //本关卡有锁
        this.isLocked = true
        this.Key.active = false
        // this.waterPosition = this.WaterLabel.position  //设置水的初始位置
    }

    onBridgeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        Public.RunCbContactBelow(selfCollider, otherCollider, ()=>{
            // if(otherCollider.node.name=="FishTankNode"){
            //
            // }
        })
    }

    touchStart(event:EventTouch){
        super.touchStart(event)

        let pos = event.getUILocation()
        if(Public.IsPointInNodeArea2D(this.BridgeNode, pos)){
            this.touchBridgeCnt+=1
            tween(this.BreakNode).to(0.05,{scale:new Vec3(1.1,1.1,1)}).to(0.05,{scale:new Vec3(1,1,1)}).start()
            if(this.touchBridgeCnt>=6){
                let p = this.BridgeNode.position
                // this.BridgeNode.active = false
                audioManager.instance.playSound(Constants.Sounds.crush)
                this.BridgeNode.getChildByName("Sprite").active=false
                this.BridgeNode.getChildByName("Spine").active=true
                tween(this.BridgeNode).delay(0.5).to(3, {position: new Vec3(p.x, p.y-1600, p.z)}).call(()=>{
                    this.BridgeNode.active = false
                }).start()
                tween(this.BridgeNode).delay(2).call(()=>{
                    this.showKey()
                }).start()
            }
        }
        if(Public.IsPointInNodeArea2D(this.LockNode, pos) && this.Key.active == true){
            this.isTouchLock = true
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event);
        if(this.isTouchLock==true){
            let pos = this.LockNode.position
            // console.log("event---touch Lock move", event.getDeltaX(), event.getDeltaY())
            let ePos = event.getUIDelta()
            this.LockNode.setPosition(new Vec3(pos.x+ePos.x, pos.y+ePos.y, pos.z))
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event);
        if(this.isTouchLock==true){
            this.isTouchLock = false
            //判断锁头是否离开门
            let lockNodeWPos = this.LockNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let doorNodeWPos = this.doorNode.getComponent(UITransform).convertToWorldSpaceAR(new Vec3(0,0,0))
            let dis = Public.GetDistance2D(lockNodeWPos, doorNodeWPos)
            this.isLocked = !(dis > 5 && this.BridgeNode.active == false);
        }
    }

    showKey(){
        if(this.Key && this.Key.active==false){
            audioManager.instance.playSound(Constants.Sounds.prompt)
            this.Key.active = true
            // this.Key.getComponent(RigidBody2D).type = ERigidBody2DType.Dynamic
        }
    }



}
