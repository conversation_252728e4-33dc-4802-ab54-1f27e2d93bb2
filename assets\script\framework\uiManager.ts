// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import { _decorator, Component, Node, find, isValid, Vec3, UIOpacity } from "cc";
import { resourceUtil } from "./resourceUtil";
import { poolManager } from "./poolManager";
import { tips } from "../ui/common/tips";
import {Constants} from "../game/Constants";
import {Public} from "../game/Public";
import {comm} from "./comm";
const { ccclass, property } = _decorator;

const SHOW_STR_INTERVAL_TIME = 800;

interface IPanel extends Component {
    show?: Function;
    hide?: Function;
    zIndex?: number;
    dialogPath?: string;
}

@ccclass("uiManager")
export class uiManager extends comm{

    dictSharedPanel: { [path: string]: Node } = {}
    dictLoading: { [path: string]: boolean } = {};
    arrPopupDialog: {
        panelPath: string,
        scriptName?: string,
        param: any,
        isShow: boolean
    }[] = [];
    showTipsTime: number = 0


    static _instance: uiManager;

    static get instance() {
        if (this._instance) {
            return this._instance;
        }

        this._instance = new uiManager();
        return this._instance;
    }

    getDialog(panelPath: string){
       return this.dictSharedPanel[panelPath]
    }

    async loadDialog(panelPath: string){
        return new Promise<void>(resolve=>{
            let _panelPath = panelPath.split("|")[1]
            let idxSplit = _panelPath.lastIndexOf('/');
            let scriptName = _panelPath.slice(idxSplit + 1);
            resourceUtil.createUIWithBundle(panelPath, (err, node) => {
                this.dictLoading[panelPath] = false;
                if (err) {
                    console.error(err);
                    return;
                }
                // node.zIndex = 100;
                this.dictSharedPanel[panelPath] = node!;
                // node.parent = find("Canvas").getChildByName("panelRoot")
                let script = node!.getComponent(scriptName)! as IPanel;
                //node.setSiblingIndex(script.zIndex)
                script.dialogPath = panelPath
                // console.log("scriptName2----", script.dialogPath, script.zIndex, scriptName, this.dictSharedPanel, node.parent)
                node.active = false
                resolve();
            })
        })
    }


    /**
     * 显示单例界面
     * @param {String} panelPath
     * @param {Array} args
     * @param {Function} cb 回调函数，创建完毕后回调
     * @param {Node} parent 父节点，默认是Canvas
     * @param {Node} active 默认为true
     */
    showDialog (panelPath: string, args?: any, cb?: Function, parent?:Node, active?:boolean) {
        if (this.dictLoading[panelPath]) {
            return;
        }
        if(active==null){
            active = true
        }

        let _panelPath = panelPath.split("|")[1]
        let idxSplit = _panelPath.lastIndexOf('/');
        let scriptName = _panelPath.slice(idxSplit + 1);

        if (!args) {
            args = [];
        }
        // console.log("---args------------------", args)
        if (this.dictSharedPanel.hasOwnProperty(panelPath)) {
            let panel = this.dictSharedPanel[panelPath];
            if (isValid(panel)) {
                console.log("---parent------------------", parent)
                if(parent==undefined){
                    panel.parent = find("Canvas")
                }else{
                    panel.parent = parent
                }

                // panel.position = Public.MainCamera.node.position

                let script = panel.getComponent(scriptName) as IPanel;
                // console.log("scriptName-old1----", scriptName, args)
                // if (script.show) {
                    // console.log("scriptName-old2----", scriptName, script.show, args)
                    // script.show.apply(script, args);
                script.show(args);
                // }
                //层级待定
                panel.setSiblingIndex(script.zIndex)
                script.dialogPath = panelPath

                panel.position = new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0)
                panel.active = true;

                cb && cb(script);

                return;
            }
        }

        this.dictLoading[panelPath] = true;
        resourceUtil.createUIWithBundle(panelPath, (err, node) => {
            //判断是否有可能在显示前已经被关掉了？
            let isCloseBeforeShow = false;
            if (!this.dictLoading[panelPath]) {
                //已经被关掉
                isCloseBeforeShow = true;
            }

            this.dictLoading[panelPath] = false;
            if (err) {
                console.error(err);
                return;
            }

            // node.zIndex = 100;

            this.dictSharedPanel[panelPath] = node!;
            let script = node!.getComponent(scriptName)! as IPanel;
            // console.log("scriptName1----", script, scriptName, args)
            if (script.show) {
                // console.log("scriptName1111----", script, scriptName, args)
                // script.show.apply(script, args);
                script.show(args);
            }
            //TODO 层级控制待定
            //node.setSiblingIndex(script.zIndex)
            script.dialogPath = panelPath
            // console.log("scriptName2----", script.dialogPath, script.zIndex, scriptName, this.dictSharedPanel, node.parent)
            // console.log(node.parent.children)
            node.active = active
            // node.getChildByName("Root").getComponent(UIOpacity).opacity=1

            cb && cb(script);

            //TODO 不要使用widget，会导致生成时位置移动失效！！！
            node.setPosition(new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0))
            // console.log("----node info------", node.position)

            // 直接执行不生效
            // setTimeout(()=>{
            //     // this.syncPos(node)
            //     node.setPosition(new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0))
            // },50)

            if (isCloseBeforeShow) {
                console.log("如果在显示前又被关闭 closeDialog1-----", panelPath)
                //如果在显示前又被关闭，则直接触发关闭掉
                this.hideDialog(panelPath);
            }
        }, parent);
    }

    // syncPos(dialogNode){
    //     if(dialogNode==undefined) return
    //     if(Public.MainCamera.node.position.x!=dialogNode.position.x || Public.MainCamera.node.position.y!=dialogNode.position.y){
    //         dialogNode.setPosition(new Vec3(Public.MainCamera.node.position.x, Public.MainCamera.node.position.y, 0))
    //         return
    //     }else{
    //         setTimeout(()=>{ this.syncPos(dialogNode)},50)
    //     }
    // }


    /**
     * 隐藏单例界面
     * @param {String} panelPath
     * @param {fn} callback
     */
    hideDialog (panelPath: string, callback?: Function) {
        console.log("closeDialog-----", panelPath)
        if (this.dictSharedPanel.hasOwnProperty(panelPath)) {
            let panel = this.dictSharedPanel[panelPath];
            if (panel && isValid(panel)) {
                // let ani = panel.getComponent('animationUI');
                // if (ani) {
                //     ani.close(() => {
                //         panel.parent = null;
                //         if (callback && typeof callback === 'function') {
                //             callback();
                //         }
                //     });
                // } else {
                    let _panelPath = panelPath.split("|")[1]
                    let idxSplit = _panelPath.lastIndexOf('/');
                    let scriptName = _panelPath.slice(idxSplit + 1);
                    let script = panel!.getComponent(scriptName)! as IPanel;
                    // console.log("scriptName1----", script, scriptName, args)
                    if (script.hide) {
                        // console.log("scriptName1111----", script, scriptName, args)
                        // script.show.apply(script, args);
                        script.hide();
                    }
                    panel.active = false
                    panel.parent = null;
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                // }
            } else if (callback && typeof callback === 'function') {
                callback();
            }
        }

        this.dictLoading[panelPath] = false;
    }

    destroyDialog(panelPath: string){
        if (this.dictSharedPanel.hasOwnProperty(panelPath)) {
            let panel = this.dictSharedPanel[panelPath];
            delete this.dictSharedPanel[panelPath]
            panel.destroy()
        }
    }

    /**
     * 将弹窗加入弹出窗队列
     * @param {string} panelPath
     * @param {string} scriptName
     * @param {*} param
     */
    pushToPopupSeq (panelPath: string, scriptName: string, param: any) {
        let popupDialog = {
            panelPath: panelPath,
            scriptName: scriptName,
            param: param,
            isShow: false
        };

        this.arrPopupDialog.push(popupDialog);

        this.checkPopupSeq();
    }

    /**
     * 将弹窗加入弹出窗队列
     * @param {number} index
     * @param {string} panelPath
     * @param {string} scriptName
     * @param {*} param
     */
    insertToPopupSeq (index: number, panelPath: string, param: any) {
        let popupDialog = {
            panelPath: panelPath,
            param: param,
            isShow: false
        };

        this.arrPopupDialog.splice(index, 0, popupDialog);
        //this.checkPopupSeq();
    }

    /**
     * 将弹窗从弹出窗队列中移除
     * @param {string} panelPath
     */
    shiftFromPopupSeq (panelPath: string) {
        this.hideDialog(panelPath, () => {
            if (this.arrPopupDialog[0] && this.arrPopupDialog[0].panelPath === panelPath) {
                this.arrPopupDialog.shift();
                this.checkPopupSeq();
            }
        })
    }

    /**
     * 检查当前是否需要弹窗
     */
    checkPopupSeq () {
        if (this.arrPopupDialog.length > 0) {
            let first = this.arrPopupDialog[0];

            if (!first.isShow) {
                this.showDialog(first.panelPath, first.param);
                this.arrPopupDialog[0].isShow = true;
            }
        }
    }

    // update (deltaTime: number) {
    //     // Your update function goes here.
    // }

    /**
     * 显示提示
     * @param {String} content
     * @param {String} param
     * @param {Function} cb
     */
    showTips (content: string, param?, cb?: Function) {
        var now = Date.now();
        if (now - this.showTipsTime < SHOW_STR_INTERVAL_TIME) {
            var spareTime = SHOW_STR_INTERVAL_TIME - (now - this.showTipsTime);
            const self = this;
            setTimeout(function (tipsLabel, callback) {
                self._showTipsAni(tipsLabel, param, callback);
            }.bind(this, content, cb), spareTime);

            this.showTipsTime = now + spareTime;
        } else {
            this._showTipsAni(content,param, cb);
            this.showTipsTime = now;
        }
    }

    /**
     * 内部函数
     * @param {String} content
     * @param {String} param
     * @param {Function} cb
     */
    _showTipsAni(content: string, param,  cb?: Function) {
        //todo 临时添加方案，后期需要将这些代码移到具体界面
        resourceUtil.getUIPrefabResWithBundle(Constants.Dialogs.tips, (err, prefab) => {
            if (err) {
                return;
            }

            let tipsNode = poolManager.instance.getNode(prefab!, find("Canvas")!) as Node;
            let tipScript = tipsNode.getComponent(tips)!;
            tipScript.show(content, param, cb);
        });
    }
}
