{"skeleton": {"hash": "3ZCX7CWA+5Y", "spine": "3.8-from-4.0.09", "x": -134.98, "y": -2.55, "width": 237, "height": 232, "images": "./images/", "audio": "E:/游戏项目/奶茶大冒险/spine/一夫当关"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": -17.97, "y": 63.21}, {"name": "bone2", "parent": "bone", "length": 40.3, "rotation": 98.38, "x": 1.47, "y": 7.76}, {"name": "bone3", "parent": "bone", "length": 15.15, "rotation": -100.38, "x": -2.94, "y": -2.31}, {"name": "bone4", "parent": "bone3", "length": 13.07, "rotation": 4.85, "x": 16.63, "y": 0.06}, {"name": "bone5", "parent": "bone", "length": 13.54, "rotation": -71, "x": 1.05, "y": -4.2}, {"name": "bone6", "parent": "bone5", "length": 12.87, "rotation": 0.03, "x": 15.07, "y": 0.58}, {"name": "bone7", "parent": "bone", "length": 27.17, "rotation": -30.13, "x": 12.8, "y": -5.46}, {"name": "bone8", "parent": "bone", "length": 22.76, "rotation": 174.71, "x": -17, "y": -2.1}, {"name": "bone9", "parent": "bone2", "length": 27.99, "rotation": 119, "x": 41.06, "y": 23.86}, {"name": "bone10", "parent": "bone9", "length": 22.75, "rotation": 90.12, "x": 30.16, "y": 2.83}, {"name": "bone11", "parent": "bone2", "length": 26.15, "rotation": -115.66, "x": 30.41, "y": -29.72}, {"name": "bone12", "parent": "bone11", "length": 22.84, "rotation": 6.68, "x": 31.66, "y": -0.05}, {"name": "bone13", "parent": "bone12", "length": 20.79, "rotation": 98.27, "x": 22.22, "y": 3.3}, {"name": "bone14", "parent": "bone2", "length": 29.68, "rotation": 4.28, "x": 39.32, "y": -0.92}, {"name": "bone15", "parent": "bone14", "length": 32.55, "rotation": 79.56, "x": 28.67, "y": -4.5}, {"name": "bone16", "parent": "bone", "length": 23.33, "rotation": -168.59, "x": -9.86, "y": -3.36}, {"name": "bone17", "parent": "bone16", "length": 23.09, "rotation": 77.03, "x": 24.41, "y": 2.14}, {"name": "bone20", "parent": "bone14", "length": 10.32, "rotation": 76.01, "x": 11.61, "y": 4.81}, {"name": "bone21", "parent": "bone14", "length": 4.04, "rotation": -166.1, "x": -4.65, "y": -5.45}, {"name": "bone18", "parent": "bone", "length": 25.68, "rotation": -50.66, "x": 21.16, "y": -13.11}, {"name": "bone19", "parent": "bone18", "length": 23.31, "rotation": -14.57, "x": 29.49, "y": 0.03}, {"name": "bone22", "parent": "bone19", "length": 13.2, "rotation": 62.4, "x": 24.82, "y": 0.56}, {"name": "bone23", "parent": "bone20", "length": 17.4, "rotation": -48.21, "x": 52.06, "y": -2.43}, {"name": "bone24", "parent": "bone20", "length": 19.02, "rotation": -52.27, "x": 78.13, "y": -12.59}, {"name": "bone25", "parent": "bone20", "length": 16.49, "rotation": -90.02, "x": 78.71, "y": -43.34}, {"name": "bone26", "parent": "bone20", "length": 16.11, "rotation": -96.54, "x": 100.08, "y": -68.94}, {"name": "bone27", "parent": "bone17", "length": 12.02, "rotation": -84.8, "x": 22.38, "y": -1.9}, {"name": "1", "parent": "root", "x": -46.8, "y": 30.22, "color": "ff3f00ff"}, {"name": "2", "parent": "root", "x": -61.84, "y": 28.51, "color": "ff3f00ff"}, {"name": "3", "parent": "root", "rotation": 41.99, "x": 25.46, "y": 2.6, "color": "ff3f00ff"}, {"name": "4", "parent": "root", "rotation": 19.13, "x": 45.29, "y": 1.98, "color": "ff3f00ff"}, {"name": "bone28", "parent": "bone13", "length": 5.62, "rotation": -145.03, "x": 54.68, "y": -8.9}, {"name": "bone29", "parent": "bone28", "length": 8.44, "rotation": 44.9, "x": 7.34, "y": 1.34}, {"name": "bone30", "parent": "bone13", "length": 11.34, "rotation": -151.67, "x": 45.19, "y": -8.43}, {"name": "bone31", "parent": "bone30", "length": 8.8, "rotation": 52.05, "x": 12.56, "y": 2.51}, {"name": "bone32", "parent": "bone31", "length": 7.48, "rotation": 28.9, "x": 10.16, "y": 1.77}, {"name": "bone33", "parent": "bone14", "length": 8.56, "rotation": -97.13, "x": 28.04, "y": -46.72}, {"name": "bone34", "parent": "bone33", "length": 8.77, "rotation": 3.49, "x": 10.22, "y": -0.02}, {"name": "bone35", "parent": "bone", "x": -52.06, "y": 109.09}, {"name": "bone36", "parent": "bone", "x": 37.07, "y": 113.74}], "slots": [{"name": "st", "bone": "root", "attachment": "st"}, {"name": "j1", "bone": "bone16", "attachment": "j1"}, {"name": "j2", "bone": "bone7", "attachment": "j2"}, {"name": "bx", "bone": "bone", "attachment": "bx"}, {"name": "zh", "bone": "bone9", "attachment": "zh"}, {"name": "wq", "bone": "bone13", "attachment": "wq"}, {"name": "yh", "bone": "bone11", "attachment": "yh"}, {"name": "bs", "bone": "bone2", "attachment": "bs"}, {"name": "d2", "bone": "bone3", "attachment": "d2"}, {"name": "d1", "bone": "bone5", "attachment": "d1"}, {"name": "head", "bone": "bone14", "attachment": "head"}, {"name": "huzi", "bone": "bone21", "attachment": "huzi"}, {"name": "yan", "bone": "bone15", "attachment": "yan"}, {"name": "zui3", "bone": "bone20", "attachment": "jb"}, {"name": "zui2", "bone": "root"}, {"name": "zui1", "bone": "root"}, {"name": "4", "bone": "bone26", "attachment": "4"}, {"name": "3", "bone": "bone25", "attachment": "3"}, {"name": "2", "bone": "bone24", "attachment": "2"}, {"name": "1", "bone": "bone23", "attachment": "1"}, {"name": "h1", "bone": "bone36", "attachment": "h1"}, {"name": "h3", "bone": "bone35", "attachment": "h2"}, {"name": "h2", "bone": "bone35"}, {"name": "jb", "bone": "root"}], "ik": [{"name": "1", "bones": ["bone16", "bone17"], "target": "1"}, {"name": "2", "order": 1, "bones": ["bone27"], "target": "2"}, {"name": "3", "order": 2, "bones": ["bone18", "bone19"], "target": "3", "bendPositive": false}, {"name": "4", "order": 3, "bones": ["bone22"], "target": "4"}], "skins": [{"name": "default", "attachments": {"1": {"1": {"x": 8.82, "y": 0.26, "rotation": -130.46, "width": 19, "height": 18}}, "2": {"2": {"x": 10.76, "y": 1.5, "rotation": -126.4, "width": 26, "height": 30}}, "3": {"3": {"x": 10.54, "y": -1.03, "rotation": -88.65, "width": 16, "height": 27}}, "4": {"4": {"x": 7.23, "y": 1.97, "rotation": -82.13, "width": 14, "height": 23}}, "bs": {"bs": {"type": "mesh", "uvs": [0, 0.42843, 0.03414, 0.53934, 0.07541, 0.60195, 0.10293, 0.70392, 0.25883, 0.76474, 0.51104, 0.79694, 0.75408, 0.78442, 0.93521, 0.73433, 0.95355, 0.627, 0.99023, 0.54292, 1, 0.39623, 1, 0.26743, 0.92833, 0.1118, 0.79764, 0.03309, 0.5867, 0, 0.38494, 0, 0.1442, 0.06708, 0.00434, 0.16547, 0, 0.32825], "triangles": [13, 12, 11, 16, 0, 18, 17, 16, 18, 2, 1, 0, 10, 13, 11, 0, 15, 2, 9, 8, 10, 4, 3, 2, 16, 15, 0, 14, 2, 15, 13, 10, 14, 14, 10, 8, 14, 8, 4, 4, 8, 5, 2, 14, 4, 7, 6, 8, 6, 5, 8], "vertices": [24.46, 32.25, 14.12, 31.33, 8.06, 29.26, -1.41, 28.68, -8.5, 18.54, -14.01, 1.25, -15.39, -15.99, -12.76, -29.38, -3.29, -32.09, 3.9, -35.78, 17.01, -38.41, 28.6, -40.12, 43.36, -37.15, 51.8, -29.02, 56.96, -14.64, 59.05, -0.47, 55.5, 17.33, 48.09, 28.46, 33.48, 30.93], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36], "width": 71, "height": 91}}, "bx": {"bx": {"type": "mesh", "uvs": [0.2961, 0, 0.47028, 0.07433, 0.64609, 0.06676, 0.75353, 0.01755, 0.89353, 0.24848, 1, 0.65355, 1, 0.75956, 0.84632, 0.92234, 0.69005, 1, 0.4833, 1, 0.26191, 0.89584, 0.10726, 0.69141, 0.01773, 0.42641, 0, 0.24848, 0, 0.16519, 0.16261, 0], "triangles": [10, 1, 9, 10, 11, 0, 10, 0, 1, 0, 11, 15, 11, 12, 15, 12, 13, 15, 13, 14, 15, 4, 7, 8, 8, 9, 2, 4, 8, 2, 9, 1, 2, 6, 7, 5, 4, 2, 3, 7, 4, 5], "vertices": [2, 7, -35.37, -1.21, 0.01414, 8, 2.62, -13.16, 0.98586, 2, 7, -18.7, 4.77, 0.49466, 8, -15.01, -11.58, 0.50534, 2, 7, -3.66, 13.87, 0.98064, 8, -32.49, -13.53, 0.01936, 1, 7, 4.58, 21.1, 1, 1, 7, 21.67, 19.54, 1, 1, 7, 39.62, 9.81, 1, 1, 7, 41.91, 5.87, 1, 2, 7, 32.13, -7.9, 0.99999, 8, -55.82, 21.26, 1e-05, 2, 7, 20.29, -18.63, 0.9649, 8, -40.57, 26.02, 0.0351, 2, 7, 2.41, -29.01, 0.64277, 8, -19.98, 27.93, 0.35723, 2, 7, -18.99, -36.24, 0.13339, 8, 2.48, 25.51, 0.86661, 2, 7, -36.78, -36.4, 0.00384, 8, 18.69, 18.18, 0.99616, 1, 8, 28.65, 7.66, 1, 1, 8, 31.12, 0.21, 1, 1, 8, 31.45, -3.36, 1, 1, 8, 15.92, -11.93, 1], "hull": 16, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 0, 30], "width": 100, "height": 43}}, "d1": {"d1": {"type": "mesh", "uvs": [0.02263, 0.13873, 0.0781, 0.4604, 0.18903, 0.76025, 0.34157, 0.97561, 0.54033, 1, 0.8454, 0.89928, 0.96095, 0.84749, 0.95633, 0.70846, 0.77606, 0.55036, 0.55419, 0.34046, 0.38779, 0.14691, 0.21677, 0.01607, 0.01801, 0.01607, 0, 0.1033], "triangles": [5, 4, 2, 4, 3, 2, 2, 8, 5, 5, 7, 6, 5, 8, 7, 8, 2, 1, 8, 1, 9, 1, 10, 9, 1, 0, 10, 0, 11, 10, 0, 13, 11, 11, 13, 12], "vertices": [1, 5, -0.24, -3.66, 1, 2, 5, 12.04, -6.53, 0.93428, 6, -3.03, -7.12, 0.06572, 2, 5, 23.93, -7.93, 0.05724, 6, 8.85, -8.52, 0.94276, 1, 6, 17.94, -7.94, 1, 1, 6, 20.33, -3.92, 1, 1, 6, 18.9, 3.99, 1, 1, 6, 17.86, 7.16, 1, 1, 6, 12.7, 8.83, 1, 1, 6, 5.52, 6.92, 1, 2, 5, 11.18, 5.34, 0.59144, 6, -3.89, 4.76, 0.40856, 2, 5, 2.8, 4.18, 0.9997, 6, -12.27, 3.6, 0.0003, 1, 5, -3.31, 2.12, 1, 1, 5, -4.8, -2.2, 1, 1, 5, -1.71, -3.7, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 23, "height": 39}}, "d2": {"d2": {"type": "mesh", "uvs": [0.4926, 0.04171, 0.29874, 0.28704, 0.13614, 0.53783, 0.02358, 0.78862, 0.08611, 0.86767, 0.34877, 0.94945, 0.67396, 0.97944, 0.97414, 0.96308, 0.9929, 0.80498, 0.94287, 0.53783, 0.93662, 0.14802, 0.82405, 0.02535], "triangles": [7, 6, 8, 8, 6, 5, 5, 4, 8, 8, 2, 9, 3, 2, 8, 8, 4, 3, 2, 1, 9, 1, 10, 9, 1, 0, 10, 0, 11, 10], "vertices": [1, 3, -1.23, -3.98, 1, 2, 3, 8.77, -5.5, 0.98144, 4, -8.3, -4.87, 0.01856, 2, 3, 18.89, -6.46, 0.10647, 4, 1.7, -6.68, 0.89353, 1, 4, 11.62, -7.65, 1, 1, 4, 14.59, -6.29, 1, 1, 4, 17.33, -1.54, 1, 1, 4, 17.96, 4.08, 1, 1, 4, 16.84, 9.09, 1, 1, 4, 10.67, 8.82, 1, 2, 3, 16.42, 7.03, 0.27358, 4, 0.38, 6.97, 0.72642, 1, 3, 1.49, 4.19, 1, 1, 3, -2.88, 1.45, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 17, "height": 39}}, "h1": {"h1": {"x": 12.01, "y": 3.1, "width": 30, "height": 30}}, "h3": {"h2": {"x": -6.59, "y": 0.78, "width": 33, "height": 41}}, "head": {"head": {"type": "mesh", "uvs": [0, 0.54191, 0.03858, 0.61816, 0.06842, 0.78722, 0.14798, 0.89828, 0.23417, 0.96126, 0.41318, 0.97121, 0.57893, 0.94634, 0.66181, 0.8701, 0.69165, 0.7922, 0.7596, 0.77894, 0.79938, 0.68611, 0.83585, 0.68611, 0.94856, 0.71263, 0.98005, 0.68611, 1, 0.55186, 0.98005, 0.4408, 0.92701, 0.4292, 0.81264, 0.48058, 0.7911, 0.38942, 0.76623, 0.33803, 0.75629, 0.16068, 0.69828, 0.05957, 0.53418, 0, 0.3734, 0, 0.23749, 0.08443, 0.19273, 0.18389, 0.13803, 0.24853, 0.01869, 0.40102, 0, 0.46566, 0.29208, 0.21931, 0.34572, 0.21106, 0.45026, 0.21519, 0.57955, 0.23994, 0.68546, 0.31422, 0.72809, 0.37749], "triangles": [14, 17, 16, 14, 16, 15, 14, 11, 17, 13, 11, 14, 12, 11, 13, 31, 23, 22, 30, 23, 31, 32, 22, 21, 32, 21, 20, 31, 22, 32, 33, 32, 20, 33, 20, 19, 34, 33, 19, 34, 19, 18, 18, 17, 34, 11, 10, 17, 34, 17, 8, 10, 9, 17, 34, 32, 33, 9, 8, 17, 31, 32, 8, 34, 8, 32, 30, 24, 23, 29, 25, 24, 30, 29, 24, 1, 0, 28, 26, 25, 29, 27, 26, 29, 1, 27, 29, 1, 28, 27, 3, 2, 1, 1, 30, 3, 30, 1, 29, 31, 3, 30, 31, 8, 3, 4, 3, 5, 7, 6, 8, 3, 8, 5, 6, 5, 8], "vertices": [2, 14, 51.41, 38.31, 0.97376, 37, -87.27, 12.63, 0.02624, 2, 14, 42.13, 35.97, 0.991, 37, -83.8, 3.71, 0.009, 1, 14, 22.92, 36.86, 1, 1, 14, 8.83, 30.89, 1, 1, 14, -0.17, 23.02, 1, 1, 14, -5.65, 3.7, 1, 2, 14, -7, -15.02, 0.87127, 37, -27.1, -38.7, 0.12873, 2, 14, -0.7, -25.95, 0.68994, 37, -17.04, -31.1, 0.31006, 2, 14, 7.08, -31.12, 0.45255, 37, -12.87, -22.73, 0.54745, 2, 14, 6.86, -38.88, 0.28111, 37, -5.15, -21.99, 0.71889, 3, 14, 16.03, -45.5, 0.08936, 37, 0.28, -12.07, 0.84144, 38, -10.65, -11.42, 0.0692, 3, 14, 15.13, -49.49, 0.02538, 37, 4.35, -12.46, 0.67703, 38, -6.61, -12.06, 0.2976, 2, 37, 16.63, -16.64, 0.04137, 38, 5.39, -16.97, 0.95863, 2, 37, 20.43, -14.02, 0.01136, 38, 9.34, -14.59, 0.98864, 1, 38, 13.9, -0.09, 1, 1, 38, 13.64, 12.54, 1, 2, 37, 17.28, 15.19, 0.0109, 38, 7.98, 14.76, 0.9891, 3, 14, 38.16, -52, 0.02413, 37, 3.98, 10.7, 0.66989, 38, -5.57, 11.08, 0.30598, 3, 14, 48.65, -51.88, 0.12306, 37, 2.56, 21.09, 0.8361, 38, -6.35, 21.54, 0.04084, 3, 14, 54.88, -50.42, 0.17687, 37, 0.34, 27.09, 0.81161, 38, -8.2, 27.66, 0.01151, 2, 14, 74.5, -53.69, 0.26503, 37, 1.15, 46.97, 0.73497, 2, 14, 86.97, -49.83, 0.30013, 37, -4.23, 58.87, 0.69987, 2, 14, 97.51, -33.36, 0.38095, 37, -21.88, 67.28, 0.61905, 2, 14, 101.46, -15.79, 0.46939, 37, -39.8, 69.01, 0.53061, 2, 14, 95.57, 1.13, 0.56586, 37, -55.86, 61.07, 0.43414, 2, 14, 85.8, 8.46, 0.66003, 37, -61.93, 50.46, 0.33997, 2, 14, 80.08, 16.03, 0.75959, 37, -68.72, 43.85, 0.24041, 2, 14, 66.34, 32.81, 0.92473, 37, -83.67, 28.13, 0.07527, 2, 14, 59.74, 36.44, 0.95185, 37, -86.45, 21.13, 0.04815, 3, 14, 79.49, -1.52, 0.58221, 37, -51.23, 45.44, 0.41568, 38, -58.57, 49.12, 0.00212, 3, 14, 79.07, -7.59, 0.54528, 37, -45.16, 45.78, 0.45286, 38, -52.49, 49.09, 0.00186, 3, 14, 76.06, -18.91, 0.4662, 37, -33.56, 44.2, 0.53132, 38, -41, 46.8, 0.00249, 3, 14, 70.18, -32.43, 0.35944, 37, -19.41, 40.04, 0.63604, 38, -27.13, 41.79, 0.00453, 3, 14, 59.46, -42.18, 0.24153, 37, -8.4, 30.62, 0.73618, 38, -16.72, 31.72, 0.0223, 3, 14, 51.5, -45.29, 0.17218, 37, -4.33, 23.1, 0.71099, 38, -13.11, 23.97, 0.11682], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 112, "height": 112}}, "huzi": {"huzi": {"type": "mesh", "uvs": [0.04494, 0.0268, 0.0974, 0.1291, 0.19437, 0.21972, 0.21186, 0.3951, 0.25796, 0.60263, 0.33744, 0.69617, 0.42011, 0.72832, 0.45826, 0.62601, 0.54887, 0.71078, 0.62677, 0.63478, 0.67764, 0.50909, 0.68718, 0.36002, 0.69513, 0.2811, 0.7921, 0.19049, 0.86364, 0, 0.92246, 0, 0.9388, 0.05628, 0.93263, 0.18091, 0.90711, 0.30392, 0.96696, 0.23756, 0.99777, 0.27802, 0.98985, 0.33791, 0.95112, 0.43987, 0.92295, 0.46092, 1, 0.52728, 1, 0.58392, 0.91415, 0.60497, 0.97753, 0.67942, 0.95904, 0.69884, 0.85253, 0.71664, 0.89214, 0.80243, 0.87718, 0.83156, 0.81732, 0.82832, 0.75218, 0.79433, 0.76539, 0.89306, 0.75306, 0.91411, 0.6624, 0.86555, 0.67736, 0.93353, 0.65271, 0.95619, 0.57085, 0.93838, 0.53124, 0.89145, 0.53564, 1, 0.50307, 1, 0.45642, 0.98532, 0.43089, 0.91411, 0.40339, 0.98452, 0.3453, 0.96833, 0.3497, 0.87122, 0.27048, 0.95053, 0.23967, 0.93596, 0.25551, 0.85504, 0.16572, 0.91168, 0.15252, 0.86798, 0.17805, 0.78706, 0.0953, 0.81295, 0.07418, 0.76116, 0.12787, 0.68671, 0.02224, 0.65434, 0.02488, 0.58798, 0.09883, 0.55237, 0, 0.36786, 0.002, 0.30797, 0.0689, 0.33225, 0.01344, 0.07814], "triangles": [16, 14, 15, 14, 17, 13, 16, 17, 14, 18, 13, 17, 1, 63, 0, 62, 1, 2, 1, 62, 63, 21, 19, 20, 18, 19, 21, 60, 61, 62, 62, 2, 3, 22, 18, 21, 23, 18, 22, 59, 62, 3, 60, 62, 59, 26, 23, 24, 59, 3, 4, 13, 18, 11, 25, 26, 24, 56, 59, 4, 57, 58, 59, 56, 57, 59, 28, 26, 27, 12, 13, 11, 26, 29, 23, 29, 26, 28, 11, 18, 23, 23, 10, 11, 29, 10, 23, 53, 56, 4, 33, 10, 29, 9, 10, 33, 54, 55, 56, 54, 56, 53, 32, 33, 29, 31, 32, 29, 30, 31, 29, 50, 53, 4, 50, 4, 5, 36, 9, 33, 8, 9, 36, 47, 5, 6, 50, 5, 47, 8, 6, 7, 39, 40, 8, 40, 6, 8, 35, 36, 33, 51, 52, 53, 51, 53, 50, 44, 6, 40, 47, 6, 44, 34, 35, 33, 36, 39, 8, 38, 39, 36, 48, 50, 47, 49, 50, 48, 37, 38, 36, 45, 46, 47, 44, 45, 47, 43, 44, 40, 42, 43, 40, 41, 42, 40], "vertices": [-68.04, -32.92, -59.69, -30.4, -49.72, -23.03, -39.11, -26.11, -25.25, -27.16, -16.01, -21.65, -10.01, -14.11, -13.74, -7.39, -4.42, -0.5, -4.66, 9.55, -9.04, 18.23, -16.82, 23.33, -20.79, 26.33, -20.87, 38.73, -27.79, 51.31, -24.79, 57.3, -20.83, 57.41, -14.24, 53.33, -8.72, 47.31, -9.34, 55.26, -5.53, 57.27, -2.61, 54.81, 1.07, 48.03, 0.8, 44.57, 8.41, 50.59, 11.55, 49.02, 8.34, 39.68, 15.7, 44.08, 15.83, 41.66, 11.39, 30.3, 18.16, 31.96, 19.02, 29.63, 15.79, 23.62, 10.58, 17.92, 16.73, 16.53, 17.27, 14.69, 9.95, 6.79, 14.49, 6.43, 14.49, 3.29, 9.32, -4.57, 4.7, -7.3, 10.95, -9.86, 9.29, -13.19, 6.09, -17.54, 0.84, -18.16, 3.35, -22.92, -0.51, -28.4, -5.68, -25.25, -5.32, -35.53, -7.69, -38.27, -11.37, -34.41, -12.81, -45.13, -15.91, -45.27, -19.09, -40.42, -21.88, -49.58, -25.83, -50.3, -27.22, -42.76, -34.4, -52.63, -37.94, -50.52, -36.15, -41.99, -51.42, -46.95, -54.64, -45.09, -49.88, -38.94, -66.8, -37.55], "hull": 64, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 0, 126], "width": 114, "height": 62}}, "j1": {"j1": {"type": "mesh", "uvs": [0.72913, 0, 0.96564, 0.17683, 1, 0.27509, 0.87221, 0.387, 0.72913, 0.56441, 0.68534, 0.72272, 0.69117, 0.99294, 0, 0.95746, 0, 0.81552, 0.16851, 0.67359, 0.17143, 0.55077, 0.09844, 0.50982, 0.13056, 0.28055, 0.28239, 0.08403, 0.55394, 0], "triangles": [7, 9, 6, 7, 8, 9, 9, 5, 6, 4, 5, 10, 5, 9, 10, 11, 12, 10, 4, 10, 12, 4, 12, 13, 13, 14, 4, 4, 14, 3, 3, 14, 0, 3, 1, 2, 3, 0, 1], "vertices": [1, 16, 8.68, -11.55, 1, 1, 16, 0.32, -1.57, 1, 2, 16, -0.24, 3.16, 0.99969, 17, -4.54, 24.24, 0.00031, 2, 16, 6.17, 7.12, 0.9339, 17, 0.76, 18.89, 0.0661, 2, 16, 13.81, 13.9, 0.38948, 17, 9.08, 12.96, 0.61052, 2, 16, 17.1, 20.66, 0.06409, 17, 16.41, 11.28, 0.93591, 1, 17, 28.83, 11.87, 1, 1, 27, 16.43, 4.16, 1, 2, 17, 21.48, -18.06, 0.00261, 27, 16.02, -2.36, 0.99739, 2, 17, 14.76, -11, 0.43046, 27, 8.37, -8.41, 0.56954, 3, 16, 37.2, 8.54, 0.00519, 17, 9.11, -11.03, 0.85739, 27, 7.89, -14.04, 0.13742, 3, 16, 39.9, 6.07, 0.02212, 17, 7.31, -14.22, 0.91228, 27, 10.9, -16.12, 0.0656, 3, 16, 36.46, -3.99, 0.31409, 17, -3.27, -13.12, 0.68233, 27, 8.85, -26.56, 0.00358, 2, 16, 28.27, -11.56, 0.87724, 17, -12.48, -6.84, 0.12276, 1, 16, 16.06, -13.04, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 43, "height": 46}}, "j2": {"j2": {"type": "mesh", "uvs": [0.28237, 0, 0.16867, 0.01407, 0, 0.16347, 0, 0.26171, 0.0345, 0.34358, 0.25281, 0.66081, 0.32786, 0.67309, 0.43246, 0.7877, 0.40745, 0.83887, 0.42791, 0.97395, 1, 0.96986, 0.99415, 0.84706, 0.90319, 0.74472, 0.77812, 0.72835, 0.6917, 0.56871, 0.72809, 0.5155, 0.43701, 0.14915, 0.412, 0.06933, 0.52469, 0.7677, 0.66877, 0.75052, 0.59559, 0.75786, 0.83651, 0.80542, 0.84325, 0.86618, 0.84422, 0.92411, 0.65762, 0.81557, 0.67754, 0.93215, 0.53594, 0.83811, 0.53532, 0.91961], "triangles": [21, 12, 11, 22, 21, 11, 23, 22, 11, 25, 22, 23, 23, 11, 10, 10, 25, 23, 14, 16, 15, 14, 6, 16, 6, 14, 18, 19, 14, 13, 19, 20, 14, 20, 18, 14, 7, 6, 18, 21, 13, 12, 24, 20, 19, 26, 18, 20, 26, 20, 24, 7, 18, 26, 8, 7, 26, 27, 8, 26, 24, 13, 21, 13, 24, 19, 22, 24, 21, 22, 25, 24, 27, 26, 24, 25, 27, 24, 9, 8, 27, 9, 27, 25, 10, 9, 25, 3, 2, 1, 16, 4, 3, 16, 3, 1, 0, 16, 1, 17, 16, 0, 6, 5, 16, 5, 4, 16], "vertices": [1, 20, -1.34, 11.55, 1, 1, 20, -4.58, 6.26, 1, 1, 20, -3.42, -6.46, 1, 1, 20, 1.14, -10.2, 1, 1, 20, 6.12, -11.87, 1, 2, 20, 28.31, -14.82, 0.59314, 21, 2.59, -14.67, 0.40686, 2, 20, 31.45, -12.15, 0.37312, 21, 4.96, -11.3, 0.62688, 2, 20, 40.35, -12.14, 0.0177, 21, 13.57, -9.05, 0.9823, 3, 20, 41.87, -15.14, 0.00191, 21, 15.79, -11.57, 0.99648, 22, -14.93, 2.39, 0.00162, 2, 21, 23.61, -13.96, 0.94912, 22, -13.42, -5.66, 0.05088, 1, 22, 17.42, -3.89, 1, 2, 21, 29.51, 16.99, 0.02766, 22, 16.74, 3.46, 0.97234, 2, 21, 21.88, 15.11, 0.28681, 22, 11.53, 9.35, 0.71319, 2, 21, 18.16, 9.39, 0.8102, 22, 4.74, 10, 0.1898, 2, 20, 39.06, 7.01, 0.00384, 21, 7.5, 9.16, 0.99616, 2, 20, 37.84, 10.56, 0.0283, 21, 5.43, 12.28, 0.9717, 1, 20, 10.87, 12.33, 1, 1, 20, 6.31, 14.32, 1, 3, 20, 42.67, -7.25, 0.01296, 21, 14.48, -4.11, 0.93979, 22, -7.3, 8.68, 0.04725, 3, 20, 46.48, -0.56, 0.00511, 21, 16.68, 3.27, 0.86502, 22, 0.39, 8.57, 0.12987, 3, 20, 44.48, -3.91, 0.0091, 21, 15.49, -0.44, 0.90411, 22, -3.5, 8.71, 0.0868, 3, 20, 53.18, 5.39, 0.00248, 21, 21.85, 10.58, 0.43282, 22, 8.84, 5.58, 0.5647, 3, 20, 56.24, 3.93, 0.00157, 21, 25.17, 9.85, 0.36231, 22, 9.04, 2.19, 0.63612, 3, 20, 59, 2.28, 0.0007, 21, 28.23, 8.87, 0.30418, 22, 8.93, -1.02, 0.69512, 3, 20, 48.84, -2.9, 0.00627, 21, 19.5, 1.52, 0.73665, 22, -0.52, 5.38, 0.25708, 3, 20, 54.65, -5.82, 0.00096, 21, 25.83, -0.01, 0.57544, 22, -0.27, -1.13, 0.4236, 3, 20, 46.26, -9.17, 0.00822, 21, 18.41, -5.17, 0.87139, 22, -7.25, 4.61, 0.1204, 3, 20, 50.02, -12.03, 0.00282, 21, 22.73, -7.11, 0.81427, 22, -7.94, -0.07, 0.18291], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34], "width": 54, "height": 60}}, "st": {"st": {"x": -47.48, "y": 14.95, "width": 63, "height": 35}}, "wq": {"wq": {"type": "mesh", "uvs": [0, 0.03042, 0.07485, 0.03962, 0.16843, 0.07875, 0.14971, 0.11404, 0.10961, 0.16008, 0.12298, 0.17773, 0.19517, 0.20688, 0.1711, 0.23757, 0.17912, 0.26903, 0.24329, 0.30202, 0.18714, 0.33194, 0.19517, 0.37797, 0.20586, 0.4102, 0.15506, 0.43935, 0.13634, 0.45316, 0.15239, 0.46851, 0.24062, 0.4616, 0.22992, 0.49766, 0.26736, 0.51301, 0.2326, 0.97335, 0.25399, 0.99944, 0.43313, 1, 0.45719, 0.98026, 0.50532, 0.51684, 0.54275, 0.5038, 0.54007, 0.47158, 0.60959, 0.47848, 0.70584, 0.47772, 0.83418, 0.46621, 0.94648, 0.43935, 1, 0.41327, 0.97311, 0.40253, 0.94019, 0.40945, 0.88753, 0.4104, 0.84146, 0.40599, 0.75808, 0.39654, 0.81293, 0.38867, 0.86779, 0.3723, 0.87108, 0.35562, 0.83597, 0.35279, 0.81074, 0.36254, 0.76247, 0.36191, 0.73285, 0.36191, 0.68019, 0.3468, 0.63301, 0.3298, 0.53647, 0.30745, 0.64179, 0.26873, 0.61765, 0.24701, 0.56499, 0.22591, 0.68896, 0.17397, 0.68896, 0.12508, 0.62424, 0.10556, 0.61107, 0.07628, 0.69884, 0.04102, 0.79428, 0.02465, 0.76686, 0.00639, 0.56499, 0, 0.46077, 0.01678, 0.38617, 0.03473, 0.28304, 0.00545, 0.08447, 0.00419, 0, 0.01269], "triangles": [32, 31, 30, 29, 32, 30, 33, 32, 29, 28, 35, 34, 28, 34, 33, 28, 33, 29, 28, 27, 35, 27, 26, 35, 38, 40, 39, 37, 40, 38, 36, 40, 37, 35, 42, 41, 36, 35, 41, 36, 41, 40, 35, 25, 43, 35, 43, 42, 25, 16, 12, 13, 12, 16, 14, 13, 16, 15, 14, 16, 18, 17, 16, 25, 18, 16, 25, 23, 18, 24, 23, 25, 19, 18, 23, 22, 19, 23, 20, 19, 22, 21, 20, 22, 1, 0, 61, 60, 1, 61, 53, 56, 55, 53, 55, 54, 53, 57, 56, 52, 57, 53, 58, 57, 52, 59, 1, 60, 2, 59, 58, 2, 1, 59, 49, 51, 50, 6, 5, 4, 3, 2, 51, 58, 51, 2, 52, 51, 58, 6, 51, 49, 6, 3, 51, 6, 4, 3, 48, 6, 49, 48, 8, 7, 48, 7, 6, 9, 8, 48, 45, 9, 48, 47, 45, 48, 46, 45, 47, 45, 11, 10, 45, 12, 11, 10, 9, 45, 44, 12, 45, 43, 12, 44, 43, 25, 12, 35, 26, 25], "vertices": [1, 32, -81.07, 8.95, 1, 1, 32, -76.62, 11.96, 1, 1, 32, -65.71, 12.31, 1, 1, 32, -59.54, 6.89, 1, 1, 32, -52.06, -1.06, 1, 2, 34, -55.68, -3.15, 1e-05, 32, -48.17, -2.5, 0.99999, 2, 34, -47.57, -1.81, 0.00293, 32, -39.95, -2.11, 0.99707, 2, 34, -41.92, -6.33, 0.01745, 32, -34.86, -7.26, 0.98255, 3, 34, -35.18, -9.03, 0.04852, 32, -28.49, -10.72, 0.95017, 13, 71.88, 16.21, 0.00131, 3, 34, -26.51, -8.55, 0.16443, 32, -19.81, -11.25, 0.81978, 13, 64.47, 11.67, 0.01579, 3, 34, -21.95, -14.9, 0.34103, 32, -16.02, -18.08, 0.60563, 13, 57.44, 15.1, 0.05335, 3, 34, -12.2, -19.07, 0.52314, 32, -6.82, -23.35, 0.31675, 13, 46.89, 14.14, 0.1601, 3, 34, -5.23, -21.68, 0.54386, 32, -0.2, -26.75, 0.11226, 13, 39.51, 13.14, 0.34388, 3, 34, -0.68, -27.64, 0.43642, 32, 3.64, -33.19, 0.01772, 13, 32.67, 16.22, 0.54586, 3, 34, 1.64, -30.14, 0.41181, 32, 5.64, -35.95, 0.00717, 13, 29.45, 17.32, 0.58102, 3, 34, 5.27, -30.74, 0.40211, 32, 9.19, -36.96, 0.00425, 13, 25.97, 16.12, 0.59364, 3, 34, 6.4, -24.81, 0.33546, 32, 10.99, -31.2, 0.00514, 13, 27.79, 10.37, 0.6594, 2, 34, 13.54, -29.08, 0.04918, 13, 19.47, 10.74, 0.95082, 2, 34, 17.8, -28.41, 0.00435, 13, 16.05, 8.13, 0.99565, 1, 13, -89.84, 6.14, 1, 1, 13, -95.78, 4.49, 1, 1, 13, -95.43, -7.33, 1, 1, 13, -90.83, -8.73, 1, 3, 34, 25.48, -14.68, 0.02946, 13, 15.8, -7.6, 0.96967, 35, -5.61, -20.76, 0.00086, 3, 34, 23.87, -11.15, 0.10488, 13, 18.9, -9.95, 0.88627, 35, -3.81, -17.31, 0.00885, 3, 34, 17.13, -8.05, 0.46174, 13, 26.29, -9.47, 0.40766, 35, -5.52, -10.1, 0.13059, 3, 34, 20.57, -4.63, 0.35093, 13, 24.89, -14.12, 0.13869, 35, -0.7, -10.7, 0.51038, 3, 34, 23.2, 1.16, 0.10593, 13, 25.32, -20.46, 0.02438, 35, 5.48, -9.22, 0.86969, 2, 35, 13.22, -4.88, 0.74931, 36, -0.54, -7.3, 0.25069, 2, 35, 19.2, 2.7, 0.02113, 36, 8.36, -3.56, 0.97887, 1, 36, 13.48, 1.15, 1, 1, 36, 12.51, 4.03, 1, 2, 33, 22.12, -3.97, 0.00217, 36, 9.97, 3.14, 0.99783, 2, 33, 18.77, -4.93, 0.03405, 36, 6.58, 3.95, 0.96595, 2, 33, 15.58, -4.6, 0.12132, 36, 3.96, 5.8, 0.87868, 4, 34, 7.93, 12.45, 0.00714, 33, 9.74, -3.66, 0.64921, 35, 4.99, 9.76, 0.13392, 36, -0.66, 9.49, 0.20974, 3, 33, 12.89, -1.11, 0.97859, 35, 8.16, 12.28, 0.00618, 36, 3.33, 10.16, 0.01522, 1, 33, 15.61, 3.34, 1, 1, 33, 15, 7.14, 1, 1, 33, 12.59, 7.28, 1, 1, 33, 11.45, 4.73, 1, 2, 32, 10.28, 10.17, 0.00532, 33, 8.31, 4.18, 0.99468, 2, 32, 9.22, 8.52, 0.03435, 33, 6.4, 3.76, 0.96565, 2, 32, 4.42, 7.47, 0.35618, 33, 2.26, 6.4, 0.64382, 2, 32, -0.55, 6.96, 0.78483, 33, -1.63, 9.55, 0.21517, 3, 34, -16.9, 8.29, 0.01053, 32, -8.32, 4.37, 0.98505, 33, -8.96, 13.2, 0.00442, 1, 32, -12.07, 15.03, 1, 1, 32, -17.13, 16.38, 1, 1, 32, -23.09, 16.08, 1, 1, 32, -28.73, 29.41, 1, 1, 32, -38.2, 35.48, 1, 1, 32, -44.29, 34.31, 1, 1, 32, -50.42, 37.21, 1, 1, 32, -54.13, 46.46, 1, 1, 32, -53.9, 53.8, 1, 1, 32, -58.41, 54.54, 1, 1, 32, -66.84, 44.12, 1, 1, 32, -67.3, 36.24, 1, 1, 32, -66.48, 29.87, 1, 1, 32, -75.82, 27.77, 1, 1, 32, -83.14, 16.9, 1, 1, 32, -84.5, 11.15, 1], "hull": 62, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 0, 122], "width": 66, "height": 230}}, "yan": {"yan": {"x": 15.29, "y": 0.41, "rotation": 177.78, "width": 41, "height": 11}}, "yh": {"yh": {"type": "mesh", "uvs": [0, 0.24215, 0.07462, 0.43089, 0.17081, 0.57137, 0.27332, 0.72108, 0.41053, 0.86245, 0.53665, 0.99239, 0.65874, 1, 0.6791, 0.85423, 0.69705, 0.7258, 0.69578, 0.6045, 0.69465, 0.49695, 0.75689, 0.50167, 0.86223, 0.54649, 0.9508, 0.53234, 0.9915, 0.41673, 0.99629, 0.24923, 0.85744, 0.14542, 0.77605, 0.19968, 0.72577, 0.18789, 0.70662, 0.14306, 0.4385, 0.02982, 0.10814, 0, 0, 0.01566], "triangles": [6, 5, 7, 5, 4, 7, 7, 4, 8, 9, 8, 4, 4, 3, 9, 9, 3, 10, 3, 2, 10, 13, 12, 14, 14, 12, 11, 14, 11, 17, 11, 10, 17, 10, 18, 17, 18, 20, 19, 18, 10, 20, 14, 17, 15, 15, 17, 16, 10, 2, 20, 2, 1, 20, 20, 0, 21, 20, 1, 0, 0, 22, 21], "vertices": [2, 11, -4.48, -7.3, 0.99963, 12, -36.74, -3, 0.00037, 2, 11, 4.23, -18.23, 0.93909, 12, -29.36, -14.87, 0.06091, 2, 11, 13.35, -25.54, 0.68159, 12, -21.15, -23.19, 0.31841, 2, 11, 23.08, -33.34, 0.40718, 12, -12.4, -32.06, 0.59282, 2, 11, 34.88, -39.88, 0.22201, 12, -1.43, -39.94, 0.77799, 2, 11, 45.73, -45.9, 0.05181, 12, 8.64, -47.18, 0.94819, 2, 11, 53.82, -43.93, 0.03981, 12, 16.9, -46.17, 0.96019, 2, 11, 52.15, -33.92, 0.05019, 12, 16.41, -36.03, 0.94981, 2, 11, 50.69, -25.09, 0.05933, 12, 15.99, -27.09, 0.94067, 2, 11, 48.12, -17.13, 0.03662, 12, 14.36, -18.88, 0.96338, 2, 11, 45.84, -10.06, 0.01648, 12, 12.92, -11.6, 0.98352, 2, 11, 49.98, -9.12, 0.00059, 12, 17.14, -11.14, 0.99941, 1, 12, 24.75, -12.87, 1, 1, 12, 30.49, -10.8, 1, 1, 12, 31.75, -2.45, 1, 1, 12, 29.95, 8.97, 1, 1, 12, 19.35, 14.28, 1, 2, 11, 45.04, 11.17, 0.00582, 12, 14.6, 9.58, 0.99418, 2, 11, 41.53, 10.93, 0.04348, 12, 11.09, 9.75, 0.95652, 2, 11, 39.37, 13.5, 0.10656, 12, 9.24, 12.55, 0.89344, 2, 11, 19.64, 15.55, 0.9248, 12, -10.12, 16.88, 0.0752, 1, 11, -2.42, 10.84, 1, 1, 11, -9.12, 7.63, 1], "hull": 23, "edges": [0, 2, 10, 12, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44, 6, 8, 8, 10, 2, 4, 4, 6, 12, 14, 14, 16, 16, 18, 18, 20], "width": 68, "height": 69}}, "zh": {"zh": {"type": "mesh", "uvs": [0.71893, 0, 0.38786, 0.13709, 0.10606, 0.30656, 0, 0.46168, 0, 0.58951, 0.12379, 0.72021, 0.30903, 0.81787, 0.40757, 0.90261, 0.50216, 0.99884, 0.70711, 0.97729, 0.8825, 0.82936, 0.87462, 0.72308, 0.70908, 0.6211, 0.6539, 0.61823, 0.59281, 0.55216, 0.59084, 0.51339, 0.7682, 0.4071, 0.96724, 0.31087, 0.98498, 0.11123, 0.93374, 0.02362, 0.84703, 0], "triangles": [19, 18, 20, 18, 16, 0, 20, 18, 0, 17, 16, 18, 1, 0, 16, 15, 1, 16, 2, 1, 15, 3, 2, 15, 4, 3, 15, 5, 4, 15, 14, 5, 15, 6, 5, 14, 6, 14, 13, 7, 6, 13, 13, 10, 9, 11, 13, 12, 10, 13, 11, 7, 13, 9, 8, 7, 9], "vertices": [-3.31, -9.23, 12.91, -11.45, 28.61, -10.86, 37.79, -6.36, 42.37, -0.36, 42.82, 9, 39.99, 18.41, 39.66, 24.96, 39.88, 31.94, 32.1, 36.28, 20.81, 33.92, 17.27, 28.73, 19.28, 19.63, 21.06, 18.06, 20.78, 13.36, 19.46, 11.49, 9.59, 11.14, -0.66, 11.83, -8.41, 2.93, -9.8, -2.51, -7.69, -5.88], "hull": 21, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40], "width": 43, "height": 59}}, "zui3": {"jb": {"x": 4.26, "y": -0.41, "rotation": -178.67, "width": 36, "height": 28}, "zui1": {"x": 13.43, "y": -0.87, "rotation": -178.67, "width": 22, "height": 22}, "zui2": {"x": -1.05, "y": -1.7, "rotation": -178.67, "width": 25, "height": 13}, "zui3": {"x": 0.46, "y": -2.17, "rotation": -178.67, "width": 48, "height": 26}}}}], "animations": {"angry": {"slots": {"1": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "h1": {"attachment": [{"name": null}]}, "h3": {"attachment": [{"name": null}]}, "zui3": {"attachment": [{"name": "zui2"}, {"time": 0.1, "name": "zui3"}, {"time": 0.6, "name": "zui2"}]}}, "bones": {"bone": {"translate": [{}, {"time": 0.2667, "x": 9.22, "y": 2.07}]}, "bone3": {"rotate": [{}, {"time": 0.2333, "angle": 9.57}, {"time": 0.7333}]}, "bone5": {"rotate": [{}, {"time": 0.4, "angle": 5.94}, {"time": 0.7333}]}, "bone7": {"rotate": [{}, {"time": 0.2333, "angle": 9.82}, {"time": 0.4333}]}, "bone8": {"rotate": [{}, {"time": 0.2333, "angle": -4.35}, {"time": 0.4333}]}, "bone9": {"rotate": [{}, {"time": 0.2, "angle": -4.43}, {"time": 0.4333}]}, "bone11": {"rotate": [{}, {"time": 0.3333, "angle": 23.97}, {"time": 0.4, "angle": -4.71}, {"time": 0.4333, "angle": -4.45}]}, "bone12": {"rotate": [{}, {"time": 0.3333, "angle": 24.1}, {"time": 0.4333, "angle": 7.89}]}, "bone13": {"rotate": [{}, {"time": 0.4, "angle": -3.45}, {"time": 0.4333, "angle": -3.26}]}, "bone14": {"rotate": [{}, {"time": 0.3333, "angle": 1.47}, {"time": 0.4333}]}, "bone16": {"rotate": [{"angle": 0.14}, {"time": 0.4333, "angle": 5.83}]}, "bone17": {"rotate": [{"angle": 12.71}, {"time": 0.4333, "angle": -18.06}]}, "bone18": {"rotate": [{"angle": -8.48}, {"time": 0.4333, "angle": -12.35}]}, "bone4": {"rotate": [{}, {"time": 0.3, "angle": 21.14}, {"time": 0.7333}]}, "bone6": {"rotate": [{}, {"time": 0.4333, "angle": 16.41}, {"time": 0.7333}]}, "bone19": {"rotate": [{"angle": 1.48}, {"time": 0.4333, "angle": -13.32}]}, "bone29": {"rotate": [{}, {"time": 0.5, "angle": -18.91}, {"time": 0.7333}]}, "bone31": {"rotate": [{}, {"time": 0.5, "angle": -19.38}, {"time": 0.7333}]}, "bone32": {"rotate": [{}, {"time": 0.5, "angle": -31.11}, {"time": 0.7333}]}}, "deform": {"default": {"head": {"head": [{"time": 0.3333}, {"time": 0.4333, "offset": 44, "vertices": [-1.40572, -1.15762, -1.47361, -1.06985, -1.22343, -2.61427, -1.38038, -2.53491, -1.38038, -2.53491, 0.15308, -2.40549, 0.2993, -2.39169, 0.15308, -2.40549, 0, 0, 0, 0, 0, 2e-05, -1.46804, 0, 0.18229, -1.45668, 0.09324, -1.46507, -1.46804, 0, 0.18229, -1.45668, 0.09324, -1.46507, -1.46804, 0, 0.18229, -1.45668, -1.46804, 0, 0.18229, -1.45668, -1.46804, 0, 0.18229, -1.45668, -1.46804, 0, 0.18229, -1.45668, -1.46804, 0, 0.18229, -1.45668]}]}, "huzi": {"huzi": [{"time": 0.3667}, {"time": 0.4333, "vertices": [5.74916, -3.58126, 2.16771, -2.26184, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.90094, 2.73324, 4.7124, 1.22526, 2.26196, 1.22524, 2.26196, 1.22524, 0, 0, 3.01595, 3e-05, 3.01595, 3e-05, 2.92168, 3e-05, 0, 0, 0, 0, 1.88499, -2.82734, 1.88499, -2.82734, 0, 0, 1.79077, -3.01583, 1.79077, -3.01583, 0, 0, 0, 0, 1.13103, -2.92157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.67569, 0.2828, 3.67569, 0.2828, 0, 0, 3.48717, 0.37705, 3.48717, 0.37705, 0, 0, 4.5239, -0.84815, 4.5239, -0.84815, 0, 0, 5.74913, -3.11001, 5.74913, -3.11001, 0, 0, 5.74916, -3.58126]}, {"time": 0.7333}]}, "yh": {"yh": [{}, {"time": 0.3333, "offset": 12, "vertices": [1.57022, 3.51816, 2.47742, 2.95055, 0, 0, 0, 0, 3.42232, 7.46597, 5.34413, 6.23672, 3.42232, 7.46597, 5.34413, 6.23672, 0, 0, 0, 0, -0.3769, 4.67243, 0.92294, 4.59593]}, {"time": 0.5333, "offset": 12, "vertices": [0.78511, 1.75908, 1.23871, 1.47527, 0, 0, 0, 0, -4.57503, -1.81489, -4.80781, -0.66948, -4.31077, 3.73289, -3.1561, 4.63346, 0, 0, 0, 0, -0.18845, 2.33622, 0.46147, 2.29797]}, {"time": 0.7333}]}}}}, "bizui": {"slots": {"1": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "h1": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffffec"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffffec"}, {"time": 0.9667, "color": "ffffff00"}]}, "h3": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffffe1"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffffe1"}, {"time": 0.9333, "color": "ffffff00"}]}}, "bones": {"bone24": {"translate": [{"x": -10.66, "y": -4.06}]}, "bone18": {"rotate": [{"angle": -12.35}]}, "bone19": {"rotate": [{"angle": -13.32}]}, "bone": {"translate": [{"x": 9.22, "y": 2.07}, {"time": 0.6, "x": 4.76, "y": -2.99}, {"time": 1, "x": 9.22, "y": 2.07}]}, "bone11": {"rotate": [{"angle": -4.45}]}, "bone12": {"rotate": [{"angle": 7.89}]}, "bone13": {"rotate": [{"angle": -3.26}], "translate": [{}, {"time": 0.6, "x": 0.59, "y": 1.31}, {"time": 1}]}, "bone16": {"rotate": [{"angle": 5.83}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -2.39}, {"time": 1}]}, "bone17": {"rotate": [{"angle": -18.06}]}, "bone15": {"translate": [{"y": -5.34, "curve": "stepped"}, {"time": 0.2667, "y": -5.34}, {"time": 0.3667, "x": 0.05, "y": -7.53, "curve": "stepped"}, {"time": 0.6667, "x": 0.05, "y": -7.53}, {"time": 0.7667, "x": 1.89, "y": 0.18, "curve": "stepped"}, {"time": 0.9667, "x": 1.89, "y": 0.18}, {"time": 1, "y": -5.34}]}, "bone27": {"rotate": [{"angle": 20.66}]}, "bone29": {"rotate": [{}, {"time": 0.2, "angle": 8.59}, {"time": 0.7333, "angle": -6.26}, {"time": 1}]}, "bone31": {"rotate": [{}, {"time": 0.2, "angle": 5.94}, {"time": 0.7333, "angle": -3.18}, {"time": 1}]}, "bone32": {"rotate": [{}, {"time": 0.4, "angle": 4.64}, {"time": 0.7333, "angle": -12.1}, {"time": 1}]}, "bone34": {"rotate": [{}, {"time": 0.5667, "angle": -7.22}, {"time": 1}]}, "bone35": {"translate": [{"x": 10.15, "y": -2.73}, {"time": 0.3333, "x": -22.64, "y": 0.39}, {"time": 0.6, "x": 10.15, "y": -2.73}, {"time": 0.9333, "x": -22.64, "y": 0.39}], "scale": [{"x": 0.1, "y": 0.1}, {"time": 0.0333, "x": 0.2, "y": 0.2}, {"time": 0.3333}, {"time": 0.6, "x": 0.1, "y": 0.1}, {"time": 0.6333, "x": 0.2, "y": 0.2}, {"time": 0.9333}]}, "bone36": {"translate": [{}, {"time": 0.3667, "x": 32.8, "y": 8.98}, {"time": 0.6}, {"time": 0.9667, "x": 32.8, "y": 8.98}], "scale": [{"x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 0.0333, "x": 0.1, "y": 0.1}, {"time": 0.3667}, {"time": 0.6, "x": 0.1, "y": 0.1, "curve": "stepped"}, {"time": 0.6333, "x": 0.1, "y": 0.1}, {"time": 0.9667}]}}, "deform": {"default": {"head": {"head": [{}, {"time": 0.3333, "offset": 1, "vertices": [0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, 0.48939, 0, -0.06076, 0.48561, 0.4894, 0.86099, -0.91508, 0.37878, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, -0.85926, -0.0547, 0.4894, 0.86099, -0.91508, 0.37878, -0.89035, 0.43367, 0.4894, 0.86099, -0.91508, 0.37878, -0.89035, 0.43367, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837]}, {"time": 1}]}, "huzi": {"huzi": [{}, {"time": 0.5667, "vertices": [2.46221, -2.67025, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.30478, 8e-05, 5.30478, 8e-05, 5.30478, 8e-05, 2.46218, 3e-05, 0, 0, 3.32359, 5e-05, 3.32359, 5e-05, 3.32359, 5e-05, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 4.52953, 6e-05, 4.52953, 6e-05, 0, 0, 3.75426, 6e-05, 3.75426, 6e-05, 0, 0, 2.46221, -2.67025]}, {"time": 1}]}, "yh": {"yh": [{}, {"time": 0.3333, "offset": 12, "vertices": [-2.04959, -0.27643, -2.05318, 0.24822, -4.97781, -1.29591, -5.14371, -0.00159, -7.02739, -1.57234, -7.1969, 0.24664, -7.02739, -1.57234, -7.1969, 0.24664, -3.85264, -1.53451, -4.11479, -0.51564, -2.66723, -1.06235, -2.84872, -0.35698]}, {"time": 1}]}}}}, "standBy1": {"slots": {"1": {"color": [{"color": "ffffff00"}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "fffffffe"}, {"time": 1.3333, "color": "ffffff00"}]}, "3": {"color": [{"color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}]}, "4": {"color": [{"color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}]}, "h1": {"attachment": [{"name": null}]}, "h3": {"attachment": [{"name": null}]}, "zui3": {"attachment": [{"name": "zui1"}]}}, "bones": {"bone": {"translate": [{}, {"time": 0.6667, "x": 4.6, "y": -4.69}, {"time": 1.3333}]}, "bone3": {"rotate": [{}, {"time": 0.7333, "angle": 5.56}, {"time": 1.3333}]}, "bone4": {"rotate": [{}, {"time": 0.8, "angle": 9.15}, {"time": 1.3333}]}, "bone5": {"rotate": [{}, {"time": 0.7333, "angle": 3.71}, {"time": 1.3333}]}, "bone6": {"rotate": [{}, {"time": 0.8333, "angle": 7.4}, {"time": 1.3333}]}, "bone9": {"rotate": [{}, {"time": 0.7333, "angle": -6.52}, {"time": 1.3333}]}, "bone10": {"rotate": [{}, {"time": 0.7333, "angle": 5.72}, {"time": 1.3333}]}, "bone12": {"rotate": [{}, {"time": 0.4333, "angle": 1.44}, {"time": 1.3333}]}, "bone14": {"rotate": [{}, {"time": 0.7667, "angle": -1.34}, {"time": 1.3333}]}, "bone15": {"translate": [{}, {"time": 0.6667, "x": 1.7}, {"time": 1.3333}]}, "bone20": {"translate": [{}, {"time": 0.7333, "x": 2.36, "y": 4.29}, {"time": 1.3333}]}, "bone23": {"translate": [{"x": -31.9, "y": 0.97}, {"time": 0.4667, "x": 12.85, "y": -54.72}, {"time": 0.8, "x": -6.44, "y": -9.39}, {"time": 1.1667, "x": -2.73, "y": -39.33}, {"time": 1.3333, "x": -31.9, "y": 0.97}]}, "bone24": {"translate": [{}, {"time": 0.1667, "x": -51.99, "y": 10.1}, {"time": 0.3667, "x": -24.43, "y": 12.95}, {"time": 0.7333, "x": -10.66, "y": -4.06}, {"time": 1, "x": -12.07, "y": 17.27}, {"time": 1.2667, "x": 8.56, "y": -21.07}, {"time": 1.3333}]}, "bone25": {"translate": [{}, {"time": 0.3333, "x": -3.94, "y": 30.84}, {"time": 0.9, "x": 24.22, "y": -26.29}, {"time": 1.3333}]}, "bone26": {"translate": [{}, {"time": 0.1, "x": -24.85, "y": 52.67}, {"time": 0.8, "x": 9.73, "y": 25.07}, {"time": 1.1333, "x": 14.92, "y": -15.02}, {"time": 1.3333}]}, "bone34": {"rotate": [{}, {"time": 0.6667, "angle": 10.07}, {"time": 1.3333}]}, "bone30": {"rotate": [{}, {"time": 0.6333, "angle": 2.77}, {"time": 1.3333}]}, "bone31": {"rotate": [{}, {"time": 0.3, "angle": 2.77}, {"time": 0.7333, "angle": -13.06}, {"time": 1.3333}]}, "bone32": {"rotate": [{}, {"time": 0.3, "angle": 2.77}, {"time": 0.7333, "angle": -14.14}, {"time": 1.3333}]}, "bone29": {"rotate": [{}, {"time": 0.3, "angle": 2.77}, {"time": 0.7333, "angle": -4.22}, {"time": 1.3333}]}}, "deform": {"default": {"head": {"head": [{}, {"time": 0.8, "offset": 67, "vertices": [-0.70123, 0.69582, 0.08705, 0.69913, -0.05403, -0.64841, -1.76021, 1.82714, -0.42491, 1.70505, -0.78207, -0.64841, -1.76021, 1.82714, -0.42491, -0.64841, -1.76021, 1.82714, -0.42491, -0.6484, -2.28969, 2.35253, -0.35916, -0.64841, -1.76021, 1.82714, -0.42491, -0.64841, -1.76021, 1.82714, -0.42491, -0.64841, -1.76021, 1.82714, -0.42491, -0.64842, -1.05898, 1.13132, -0.51196, 0, 0, 0, 0, 0, 0, 0, 0, 2e-05, -0.70123, 0.69582, 0.08705, 0.69913, -0.05403, -0.64841, -1.76021, 1.82714, -0.42491, 1.70505, -0.78207, -0.6484, -2.28969, 2.35253, -0.35916, 2.23296, -0.82285, -0.6484, -2.28969, 2.35253, -0.35916, 2.23296, -0.82285, -0.6484, -2.28969, 2.35253, -0.35916, 2.23296, -0.82285, 2e-05, -1.76022, 1.74662, 0.21852, 1.75497, -0.13557]}, {"time": 1.3333}]}, "huzi": {"huzi": [{}, {"time": 0.3667, "vertices": [2.46221, -2.67025, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.30478, 8e-05, 5.30478, 8e-05, 5.30478, 8e-05, 2.46218, 3e-05, 0, 0, 3.32359, 5e-05, 3.32359, 5e-05, 3.32359, 5e-05, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 4.52953, 6e-05, 4.52953, 6e-05, 0, 0, 3.75426, 6e-05, 3.75426, 6e-05, 0, 0, 2.46221, -2.67025]}, {"time": 1.3333}]}, "yh": {"yh": [{}, {"time": 0.6, "offset": 12, "vertices": [3.79518, 1.18003, 3.90669, 0.73024, 3.96739, 2.2325, 4.20026, 1.75551, 4.34882, 5.03904, 4.90578, 4.49854, 4.34882, 5.03904, 4.90578, 4.49854, 4.34883, 3.68297, 4.74795, 3.1517, 1.13025, 2.53995, 1.41818, 2.39111]}, {"time": 1.3333}]}}}}, "standBy2": {"slots": {"1": {"attachment": [{"name": null}]}, "2": {"attachment": [{"name": null}]}, "3": {"attachment": [{"name": null}]}, "4": {"attachment": [{"name": null}]}, "h1": {"attachment": [{"name": null}]}, "h3": {"attachment": [{"name": null}]}, "zui3": {"attachment": [{"name": "zui2"}]}}, "bones": {"bone24": {"translate": [{"x": -10.66, "y": -4.06}]}, "bone18": {"rotate": [{"angle": -12.35}]}, "bone19": {"rotate": [{"angle": -13.32}]}, "bone": {"translate": [{"x": 9.22, "y": 2.07}, {"time": 0.5, "x": 4.76, "y": -2.99}, {"time": 1, "x": 9.22, "y": 2.07}]}, "bone11": {"rotate": [{"angle": -4.45}]}, "bone12": {"rotate": [{"angle": 7.89}]}, "bone13": {"rotate": [{"angle": -3.26}], "translate": [{}, {"time": 0.5, "x": 0.59, "y": 1.31}, {"time": 1}]}, "bone16": {"rotate": [{"angle": 5.83}]}, "bone9": {"rotate": [{}, {"time": 0.3333, "angle": -2.39}, {"time": 1}]}, "bone17": {"rotate": [{"angle": -18.06}]}, "bone27": {"rotate": [{"angle": 20.66}]}, "bone29": {"rotate": [{}, {"time": 0.2, "angle": 8.59}, {"time": 0.6333, "angle": -6.26}, {"time": 1}]}, "bone31": {"rotate": [{}, {"time": 0.2, "angle": 5.94}, {"time": 0.6333, "angle": -3.18}, {"time": 1}]}, "bone32": {"rotate": [{}, {"time": 0.4, "angle": 4.64}, {"time": 0.6333, "angle": -12.1}, {"time": 1}]}, "bone34": {"rotate": [{}, {"time": 0.4667, "angle": -7.22}, {"time": 1}]}}, "deform": {"default": {"head": {"head": [{}, {"time": 0.3333, "offset": 1, "vertices": [0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, 0.48939, 0, -0.06076, 0.48561, 0.4894, 0.86099, -0.91508, 0.37878, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, 1e-05, 0.86099, -0.85432, -0.10683, -0.85926, -0.0547, 0.4894, 0.86099, -0.91508, 0.37878, -0.89035, 0.43367, 0.4894, 0.86099, -0.91508, 0.37878, -0.89035, 0.43367, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837, 0.48939, 0, -0.06076, 0.48561, -0.03109, 0.48837]}, {"time": 1}]}, "huzi": {"huzi": [{}, {"time": 0.4667, "vertices": [2.46221, -2.67025, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.30478, 8e-05, 5.30478, 8e-05, 5.30478, 8e-05, 2.46218, 3e-05, 0, 0, 3.32359, 5e-05, 3.32359, 5e-05, 3.32359, 5e-05, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.46218, 3e-05, 2.46218, 3e-05, 0, 0, 4.52953, 6e-05, 4.52953, 6e-05, 0, 0, 3.75426, 6e-05, 3.75426, 6e-05, 0, 0, 2.46221, -2.67025]}, {"time": 1}]}, "yh": {"yh": [{}, {"time": 0.3333, "offset": 12, "vertices": [-2.04959, -0.27643, -2.05318, 0.24822, -4.97781, -1.29591, -5.14371, -0.00159, -7.02739, -1.57234, -7.1969, 0.24664, -7.02739, -1.57234, -7.1969, 0.24664, -3.85264, -1.53451, -4.11479, -0.51564, -2.66723, -1.06235, -2.84872, -0.35698]}, {"time": 1}]}}}}}}