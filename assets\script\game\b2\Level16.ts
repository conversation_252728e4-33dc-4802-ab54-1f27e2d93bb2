
import { _decorator, EventTouch, Node , tween, Vec3, Collider2D, Contact2DType, RigidBody2D, UIOpacity, sp, UITransform} from 'cc';
import {LevelDialogBase} from "../LevelDialogBase";
import {Public} from "../Public";
import {audioManager} from "../../framework/audioManager";
import {Constants} from "../Constants";
import {uiManager} from "../../framework/uiManager";
const { ccclass, property } = _decorator;

/**
 * Predefined variables
 * Name = Level1
 * DateTime = Thu Feb 24 2022 15:09:05 GMT+0800 (中国标准时间)
 * Author = fatpander
 * FileBasename = Level1.ts
 * FileBasenameNoExtension = Level1
 * URL = db://assets/script/game/Level1.ts
 * ManualUrl = https://docs.cocos.com/creator/3.4/manual/zh/
 * 牛郎织女
 */
 
@ccclass('Level16')
export class Level16 extends LevelDialogBase {

    @property(Node)
    GirlNode:Node = null

    @property(Node)
    BoyNode:Node = null

    @property(Node)
    StepLeft:Node = null

    @property(Node)
    StepRight:Node = null

    @property(Node)
    AreaNode:Node = null

    @property(Node)
    EndNode1:Node = null
    @property(Node)
    EndNode11:Node = null

    @property(Node)
    EndNode2:Node = null
    @property(Node)
    EndNode22:Node = null

    @property(Node)
    RainbowNode:Node = null

    isTouchArea:boolean = false
    slideCnt:number = 0
    touchPath=[]

    start(){
        super.start()
        this.BoyNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onBoyNodeContact, this);
        this.GirlNode.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onGirlNodeContact, this);
        //钥匙的监听事件，看情况而定，有的关卡不需要
        this.Key.getComponent(Collider2D).on(Contact2DType.BEGIN_CONTACT, this.onKeyContact, this);
    }

    show(params){
        super.show(params)
        this.levelLabel.string = `第${Public.CurLevelNum}关`
        uiManager.instance.showDialog(Constants.Dialogs.ops, {offsetVec:new Vec3(500,0,0)})

        this.isLocked = true
        this.Key.active = false
        // this.showKey()
        this.RainbowNode.active = false
    }

    onRoleBeginContact(selfCollider, otherCollider, contact) {
        super.onRoleBeginContact(selfCollider, otherCollider, contact);
        if(otherCollider.node.name == "Boy" || otherCollider.node.name == "Girl"){

            if(this.StepLeft.active == true){
                audioManager.instance.playSound(Constants.Sounds.disappear)
                tween(this.StepLeft.getComponent(UIOpacity)).to(1, {opacity:1}).call(()=>{
                    this.StepLeft.active = false
                }).start()
                tween(this.StepRight.getComponent(UIOpacity)).to(1, {opacity:1}).call(()=>{
                    this.StepRight.active = false
                }).start()
            }

        }
    }

    //碰到地面就消失了
    onBoyNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        if(otherCollider.node.name=="Place"){
            if(this.BoyNode.active == true){
                audioManager.instance.playSound(Constants.Sounds.disappear)
                tween(this.BoyNode.getComponent(UIOpacity)).to(1, {opacity:1}).call(()=>{
                    this.BoyNode.active = false
                    this.scheduleOnce(()=>{
                        this.Lose()
                    },0.1)
                }).start()
            }
        }
    }
    //碰到地面就消失了
    onGirlNodeContact(selfCollider: Collider2D, otherCollider: Collider2D, contact){
        if(otherCollider.node.name=="Place"){
            if(this.GirlNode.active == true){
                audioManager.instance.playSound(Constants.Sounds.disappear)
                tween(this.GirlNode.getComponent(UIOpacity)).to(1, {opacity:1}).call(()=>{
                    this.GirlNode.active = false
                    this.scheduleOnce(()=>{
                        this.Lose()
                    },0.1)
                }).start()
            }
        }
    }

    touchStart(event:EventTouch){
        super.touchStart(event)
        let pos = event.getUILocation()
        //判断关卡牌子的手指的滑动行为
        // console.log("--------areaNode------", Public.IsPointInNodeArea2D(this.AreaNode, pos), pos)
        if(this.RainbowNode.active==false && Public.IsPointInNodeArea2D(this.AreaNode, pos)==true){
            this.isTouchArea = true
        }
    }

    touchMove(event:EventTouch) {
        super.touchMove(event)
        if(this.RainbowNode.active==false){
            if(this.isTouchArea==true){
                let pos = event.getLocation()
                // console.log("-----------isTouchPic move---", pos)
                if(this.touchPath.length<100){
                    this.touchPath.push(pos)
                }
            }
        }
    }

    touchEnd(event:EventTouch) {
        super.touchEnd(event)
        if(this.RainbowNode.active==false){
            if(this.isTouchArea==true){
                this.isTouchArea=false
                //便利所有点，如果距离和第一个点大于，100并且在格子内，那么就ok
                for(let i=0; i<this.touchPath.length; i++){
                    let dis = Public.GetDistance2D(this.touchPath[0], this.touchPath[i])
                    console.log("---------dis------------",dis)
                    if(dis>300){
                        this.slideCnt+=1
                        if(this.slideCnt<=2){
                            return
                        }
                        audioManager.instance.playSound(Constants.Sounds.prompt)

                        this.RainbowNode.active = true
                        tween(this.RainbowNode.getChildByName("mask").getComponent(UITransform)).to(2,{width:800}).start()
                        tween(this.RainbowNode.getChildByName("mask").getChildByName("rainbow").getComponent(UIOpacity)).to(2,{opacity:255}).call(()=>{
                            //把牛郎和织女弄到一起
                            this.BoyNode.getChildByName("boy").getComponent(sp.Skeleton).setAnimation(0,"run",true)
                            tween(this.BoyNode).to(0.5,{position:this.EndNode11.position}).call(()=>{
                                Public.Bezier2DShow(this.BoyNode, this.EndNode1, 0.5, -20, 50, 2, ()=>{
                                    this.BoyNode.setPosition(this.EndNode1.position)
                                    this.BoyNode.getChildByName("boy").getComponent(sp.Skeleton).setAnimation(0,"end",true)
                                    console.log("--------body End----")
                                    this.BoyNode.getComponent(RigidBody2D).enabled = false
                                    // this.BoyNode.getComponent()
                                })
                            }).start()

                            tween(this.GirlNode).to(0.5,{position:this.EndNode22.position}).call(()=>{
                                this.GirlNode.getChildByName("girl").getComponent(sp.Skeleton).setAnimation(1,"run",true)
                                Public.Bezier2DShow(this.GirlNode, this.EndNode2, 0.5, -20, 50, 2, ()=>{
                                    this.GirlNode.setPosition(this.EndNode2.position)
                                    this.GirlNode.getChildByName("girl").getComponent(sp.Skeleton).setAnimation(0,"end",true)
                                    this.GirlNode.getComponent(RigidBody2D).enabled = false
                                    console.log("--------girl End----")
                                    this.showKey()
                                })
                            }).start()


                        }).start()

                        break
                    }
                }
                // console.log("-----------isTouchPic end---", this.touchPath)
                this.touchPath=[]
            }
        }
    }


}

/**
 * [1] Class member could be defined like this.
 * [2] Use `property` decorator if your want the member to be serializable.
 * [3] Your initialization goes here.
 * [4] Your update function goes here.
 *
 * Learn more about scripting: https://docs.cocos.com/creator/3.4/manual/zh/scripting/
 * Learn more about CCClass: https://docs.cocos.com/creator/3.4/manual/zh/scripting/decorator.html
 * Learn more about life-cycle callbacks: https://docs.cocos.com/creator/3.4/manual/zh/scripting/life-cycle-callbacks.html
 */
