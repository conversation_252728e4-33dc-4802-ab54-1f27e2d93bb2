<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>cloth.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,437},{54,56}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{54,56}}</string>
                <key>sourceSize</key>
                <string>{54,56}</string>
            </dict>
            <key>hotboom.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{219,303}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{219,303}}</string>
                <key>sourceSize</key>
                <string>{219,303}</string>
            </dict>
            <key>scissors.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,378},{57,57}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{57,57}}</string>
                <key>sourceSize</key>
                <string>{57,57}</string>
            </dict>
            <key>stone.png</key>
            <dict>
                <key>frame</key>
                <string>{{58,437},{50,46}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{50,46}}</string>
                <key>sourceSize</key>
                <string>{50,46}</string>
            </dict>
            <key>vs.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,307},{68,69}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{68,69}}</string>
                <key>sourceSize</key>
                <string>{68,69}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>level10.png</string>
            <key>size</key>
            <string>{256,512}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9aa2091c7cb8f4b540cb0976d970e99c$</string>
            <key>textureFileName</key>
            <string>level10.png</string>
        </dict>
    </dict>
</plist>
