// Learn cc.Class:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/class.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/class.html
// Learn Attribute:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/reference/attributes.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - [Chinese] http://docs.cocos.com/creator/manual/zh/scripting/life-cycle-callbacks.html
//  - [English] http://www.cocos2d-x.org/docs/creator/en/scripting/life-cycle-callbacks.html

import {_decorator, Node, Label} from "cc";
import {dialogBase} from "../../framework/dialogBase";
import {AdCtl} from "../../channel/AdCtl";
import {clientEvent} from "../../framework/clientEvent";
import {Constants} from "../../game/Constants";

const { ccclass, property } = _decorator;

@ccclass("InitAd")
export class InitAd extends dialogBase {

    @property(Node)
    Mask:Node = null!
    @property(Label)
    goLabel:Label = null!

    cnt = 0

    onEnable () {
    }

    onDisable () {
    }


    show(){
        this.Mask.active = true
        AdCtl.instance.CreateInit(()=>{
            this.Mask.active = false
            AdCtl.instance.ShowInit()
            clientEvent.dispatchEvent(Constants.Events.finishLoading)
            this.goLabel.string = ``
            this.cnt=0
            this.unlock()
        }, false)

    }

    unlock(){
        if(this.cnt>=3){
            this.goLabel.string = `继续游戏`
            return
        }else{
            this.goLabel.string = `继续游戏${3-this.cnt}`
            this.scheduleOnce(()=>{
                this.cnt++
                this.unlock()
            },1)
        }
    }

    OnClickClose(){
        AdCtl.instance.HideInit()
        this.close()
    }

    hide(){
        // AdCtl.instance.HideInit()
    }

}
